<Project Sdk="Microsoft.NET.Sdk.WindowsDesktop">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net472</TargetFramework>
    <ProjectTypeGuids>{F8C00C2F-7264-440D-AD33-F8A6B2848747};{8854D484-3257-42D8-8C34-6F2E9BC9484A}</ProjectTypeGuids>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <OutputPath>bin\$(Platform)\$(Configuration)\</OutputPath>
    <Platforms>x64</Platforms>
    <UseWPF>true</UseWPF>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugType>full</DebugType>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DebugType>pdbonly</DebugType>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Sick.GenIStreamDotNet" Version=" 4.2.0.20192" />
    <PackageReference Include="Sick.Stream.Algorithms.DotNet" Version="1.2.1.16" />
    <PackageReference Include="Sick.Stream.Common" Version="1.2.1.16" />
    <PackageReference Include="Sick.Stream.Controls" Version="1.2.1.16" />
    <PackageReference Include="Sick.Stream.Processing" Version="1.2.1.16" />
	  <PackageReference Include="Sick.Stream.Processing.Tools" Version="1.2.1.16" />
	  <PackageReference Include="System.Net.Http" Version="4.3.4" />
	  <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>
</Project>