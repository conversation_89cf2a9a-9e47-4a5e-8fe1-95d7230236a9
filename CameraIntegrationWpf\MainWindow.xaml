<Window x:Class="CameraIntegrationWpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:Sick.Stream.Controls;assembly=Sick.Stream.Controls"
        mc:Ignorable="d"
        Title="MainWindow" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="3*"/>
            <ColumnDefinition Width="Auto"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <Grid >
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="100"/>
            </Grid.RowDefinitions>

            <controls:Viewer3DToolbar Viewer="{Binding ElementName=Viewer}"
                           HorizontalAlignment="Left"
                           Margin="5"/>
            <controls:Viewer3D Grid.Row="1"
                    x:Name="Viewer"
                    Background="Black"
                    MouseOverInfo="True"/>
            <Grid Grid.Row="2">
                <Border BorderBrush="LightGray"
                        BorderThickness="1"
                        Margin="0"/>
            </Grid>
        </Grid>

        <GridSplitter Grid.Column="1"
                      HorizontalAlignment="Stretch"
                      Margin="5,0"
                      Width="5"/>

        <Grid Grid.Column="2" Margin="5">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="5"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel>
                    <Button Content="Connect"
                            Click="ConnectButton_Click"
                            IsEnabled="{Binding IsDisconnected}"/>

                    <Button Content="Start"
                            Margin="0,5"
                            Click="StartButton_Click"
                            IsEnabled="{Binding IsStopped}"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2">
                    <Button Content="Disconnect"
                            Click="DisconnectButton_Click"
                            IsEnabled="{Binding IsConnected}"/>

                    <Button Content="Stop"
                            Margin="0,5"
                            Click="StopButton_Click"
                            IsEnabled="{Binding IsStarted}"/>
                </StackPanel>
            </Grid>
            <StackPanel Grid.Row="1" Margin="0 5 0 5">
                <TextBlock Text="Status: " />
            </StackPanel>

            <StackPanel Grid.Row="2"
                        Grid.Column="0"
                        Margin="0,5"
                        Orientation="Horizontal">
                <TextBlock Text="Frame id: " />
                <TextBlock Text="{Binding FrameId}"/>
            </StackPanel>

            <ListBox Grid.Row="3"
                     Grid.Column="0"
                     BorderBrush="LightGray"
                     ItemsSource="{Binding LogMessages}"/>
        </Grid>
    </Grid>
</Window>
