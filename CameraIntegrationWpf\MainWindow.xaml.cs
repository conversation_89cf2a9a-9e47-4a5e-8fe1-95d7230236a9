// Copyright 2023-2025 SICK AG. All rights reserved.

using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Windows;
using Sick.GenIStream;
using Sick.Stream.Processing;

namespace CameraIntegrationWpf
{
    /// <summary>
    /// This example shows how to integrate a camera in a WPF application.
    /// The example create buttons to connect/disconnect and start/stop a camera.
    /// Images acquired from the camera are displayed in the 2D viewer.
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        private readonly CameraDiscovery _discovery;
        private readonly IProcessingEnvironment _environment;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;

            _environment = new ProcessingEnvironment();

            var projectDirectory = Directory.GetParent(Environment.CurrentDirectory).Parent.Parent.Parent.FullName;
            _discovery = CameraDiscovery.CreateFromProducerFile($"{projectDirectory}\\SICKGigEVisionTL.cti");
        }

        public ICamera Camera { get; private set; }
        public FrameGrabber Grabber { get; set; }
        public ulong FrameId { get; set; }
        public bool IsStarted => Grabber?.IsStarted ?? false;
        public bool IsStopped => IsConnected && !IsStarted;
        public bool IsConnected => Camera?.IsConnected ?? false;
        public bool IsDisconnected => !IsConnected;
        public event PropertyChangedEventHandler PropertyChanged;

        private void NotifyPropertyChanged(string propName) =>
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propName));

        private void ConnectButton_Click(object sender, RoutedEventArgs e)
        {
            var discoveredCameras = _discovery.ScanForCameras();
            if (discoveredCameras.Count == 0)
            {
                Log("No cameras found");
                return;
            }

            // If this operation throws an exception, the camera might be on a different subnet
            Camera = _discovery.ConnectTo(discoveredCameras[0]);
            Camera.GetCameraParameters().DeviceScanType.Set(DeviceScanType.LINESCAN_3D);

            Camera.Disconnected += OnUnexpectedDisconnect;

            Log($"Connected to camera: {Camera.Id}");
            UpdatePropertyChanged();
        }

        private void DisconnectButton_Click(object sender, RoutedEventArgs e)
        {
            Stop();
            Camera?.Disconnect();
            Camera?.Dispose();
            Camera = null;
            Log("Camera disconnected");
            UpdatePropertyChanged();
        }

        private void StartButton_Click(object sender, RoutedEventArgs e)
        {
            Grabber = Camera?.CreateFrameGrabber(20);
            Grabber?.Start();
            // Register to get callback whenever a new frame is received from
            // the camera
            Grabber.Next += OnFrameReceived;
            Log("Camera started");
            UpdatePropertyChanged();
        }

        private void StopButton_Click(object sender, RoutedEventArgs e)
        {
            Stop();
            UpdatePropertyChanged();
        }

        private void Stop()
        {
            Grabber?.Stop();
            Grabber?.Dispose();
            Grabber = null;
            Log("Camera stopped");
        }

        private void DispatchFrame(IFrame frame)
        {
            // Create a copy of the image, so that we can access the data after
            // releasing the frame below.
            var frameCopy = frame.Copy();

            // OnFrameReceived is a callback function that is called from a thread
            // within GenIStream. Thus we dispatch to the main GUI thread to
            // update some variables and actually show the bitmap, to ensure
            // thread safety.
            Dispatcher.BeginInvoke(new Action(() =>
            {
                FrameId = frameCopy.GetFrameId();
                NotifyPropertyChanged(nameof(FrameId));
                Viewer.Image = _environment.AddFrame("Image", frameCopy);
            }));

            // Explicitly release last borrowed frame payload memory back to
            // frame grabber, since we do not need to keep it any longer
            frame.Release();
        }

        // Function called by the FrameGrabber when new frames are received
        private void OnFrameReceived(GrabResult result)
        {
            result.IfCompleteFrame(DispatchFrame);
        }

        public ObservableCollection<string> LogMessages { get; } = new ObservableCollection<string>();

        public void Log(string message) => Dispatcher.BeginInvoke(new Action(() => LogMessages.Add(message)));

        private void UpdatePropertyChanged()
        {
            NotifyPropertyChanged(nameof(IsConnected));
            NotifyPropertyChanged(nameof(IsDisconnected));
            NotifyPropertyChanged(nameof(IsStarted));
            NotifyPropertyChanged(nameof(IsStopped));
        }

        private void OnUnexpectedDisconnect(string deviceId)
        {
            Log("Camera disconnected unexpectedly.");
            Grabber?.Dispose();
            Grabber = null;
            Camera = null;
            UpdatePropertyChanged();
        }

        private void Viewer2DToolbar_Loaded(object sender, RoutedEventArgs e)
        {

        }
    }
}
