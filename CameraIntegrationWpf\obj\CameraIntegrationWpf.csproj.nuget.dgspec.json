{"format": 1, "restore": {"D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj": {}}, "projects": {"D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj", "projectName": "CameraIntegrationWpf", "projectPath": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\SICK\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Sick.GenIStreamDotNet": {"target": "Package", "version": "[4.2.0.20192, )"}, "Sick.Stream.Algorithms.DotNet": {"target": "Package", "version": "[1.2.1.16, )"}, "Sick.Stream.Common": {"target": "Package", "version": "[1.2.1.16, )"}, "Sick.Stream.Controls": {"target": "Package", "version": "[1.2.1.16, )"}, "Sick.Stream.Processing": {"target": "Package", "version": "[1.2.1.16, )"}, "Sick.Stream.Processing.Tools": {"target": "Package", "version": "[1.2.1.16, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Text.RegularExpressions": {"target": "Package", "version": "[4.3.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}}