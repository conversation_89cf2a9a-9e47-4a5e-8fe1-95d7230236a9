{"version": 3, "targets": {".NETFramework,Version=v4.7.2": {"DynamicData/9.2.1": {"type": "package", "dependencies": {"System.Reactive": "6.0.1"}, "compile": {"lib/net462/DynamicData.dll": {"related": ".xml"}}, "runtime": {"lib/net462/DynamicData.dll": {"related": ".xml"}}}, "Functional.Maybe/2.0.20": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.0/Functional.Maybe.dll": {}}, "runtime": {"lib/netstandard1.0/Functional.Maybe.dll": {}}}, "IKVM.WINDWARD/8.5.0.2": {"type": "package", "compile": {"lib/IKVM.AWT.WinForms.dll": {}, "lib/IKVM.OpenJDK.Beans.dll": {}, "lib/IKVM.OpenJDK.Charsets.dll": {}, "lib/IKVM.OpenJDK.Cldrdata.dll": {}, "lib/IKVM.OpenJDK.Corba.dll": {}, "lib/IKVM.OpenJDK.Core.dll": {}, "lib/IKVM.OpenJDK.Jdbc.dll": {}, "lib/IKVM.OpenJDK.Localedata.dll": {}, "lib/IKVM.OpenJDK.Management.dll": {}, "lib/IKVM.OpenJDK.Media.dll": {}, "lib/IKVM.OpenJDK.Misc.dll": {}, "lib/IKVM.OpenJDK.Naming.dll": {}, "lib/IKVM.OpenJDK.Nashorn.dll": {}, "lib/IKVM.OpenJDK.Remoting.dll": {}, "lib/IKVM.OpenJDK.Security.dll": {}, "lib/IKVM.OpenJDK.SwingAWT.dll": {}, "lib/IKVM.OpenJDK.Text.dll": {}, "lib/IKVM.OpenJDK.Tools.dll": {}, "lib/IKVM.OpenJDK.Util.dll": {}, "lib/IKVM.OpenJDK.XML.API.dll": {}, "lib/IKVM.OpenJDK.XML.Bind.dll": {}, "lib/IKVM.OpenJDK.XML.Crypto.dll": {}, "lib/IKVM.OpenJDK.XML.Parse.dll": {}, "lib/IKVM.OpenJDK.XML.Transform.dll": {}, "lib/IKVM.OpenJDK.XML.WebServices.dll": {}, "lib/IKVM.OpenJDK.XML.XPath.dll": {}, "lib/IKVM.Reflection.dll": {}, "lib/IKVM.Runtime.JNI.dll": {}, "lib/IKVM.Runtime.dll": {}}, "runtime": {"lib/IKVM.AWT.WinForms.dll": {}, "lib/IKVM.OpenJDK.Beans.dll": {}, "lib/IKVM.OpenJDK.Charsets.dll": {}, "lib/IKVM.OpenJDK.Cldrdata.dll": {}, "lib/IKVM.OpenJDK.Corba.dll": {}, "lib/IKVM.OpenJDK.Core.dll": {}, "lib/IKVM.OpenJDK.Jdbc.dll": {}, "lib/IKVM.OpenJDK.Localedata.dll": {}, "lib/IKVM.OpenJDK.Management.dll": {}, "lib/IKVM.OpenJDK.Media.dll": {}, "lib/IKVM.OpenJDK.Misc.dll": {}, "lib/IKVM.OpenJDK.Naming.dll": {}, "lib/IKVM.OpenJDK.Nashorn.dll": {}, "lib/IKVM.OpenJDK.Remoting.dll": {}, "lib/IKVM.OpenJDK.Security.dll": {}, "lib/IKVM.OpenJDK.SwingAWT.dll": {}, "lib/IKVM.OpenJDK.Text.dll": {}, "lib/IKVM.OpenJDK.Tools.dll": {}, "lib/IKVM.OpenJDK.Util.dll": {}, "lib/IKVM.OpenJDK.XML.API.dll": {}, "lib/IKVM.OpenJDK.XML.Bind.dll": {}, "lib/IKVM.OpenJDK.XML.Crypto.dll": {}, "lib/IKVM.OpenJDK.XML.Parse.dll": {}, "lib/IKVM.OpenJDK.XML.Transform.dll": {}, "lib/IKVM.OpenJDK.XML.WebServices.dll": {}, "lib/IKVM.OpenJDK.XML.XPath.dll": {}, "lib/IKVM.Reflection.dll": {}, "lib/IKVM.Runtime.JNI.dll": {}, "lib/IKVM.Runtime.dll": {}}}, "MaterialDesignColors/2.0.6": {"type": "package", "compile": {"lib/net452/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net452/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.5.0": {"type": "package", "dependencies": {"MaterialDesignColors": "[2.0.6, 3.0.0)"}, "compile": {"lib/net452/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net452/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.2": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net472": "1.0.2"}}, "Microsoft.NETFramework.ReferenceAssemblies.net472/1.0.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/Microsoft.Win32.Primitives.dll": {}}, "runtime": {"lib/net46/Microsoft.Win32.Primitives.dll": {}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "NLog/5.4.0": {"type": "package", "frameworkAssemblies": ["System", "System.Configuration", "System.Core", "System.IO.Compression", "System.Xml"], "compile": {"lib/net46/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/NLog.dll": {"related": ".xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "org.matheval/1.0.0.3": {"type": "package", "compile": {"lib/net472/org.matheval.dll": {}}, "runtime": {"lib/net472/org.matheval.dll": {}}}, "OxyPlot.Core/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2"}, "compile": {"lib/net462/OxyPlot.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.dll": {"related": ".xml"}}}, "OxyPlot.Wpf/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2", "OxyPlot.Core": "2.2.0", "OxyPlot.Wpf.Shared": "2.2.0"}, "frameworkAssemblies": ["ReachFramework", "System.Printing"], "compile": {"lib/net462/OxyPlot.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.Wpf.dll": {"related": ".xml"}}}, "OxyPlot.Wpf.Shared/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2", "OxyPlot.Core": "2.2.0"}, "compile": {"lib/net462/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}}, "Sick.GenIStreamDotNet/4.2.0.20192": {"type": "package", "compile": {"lib/net45/Sick.GenIStreamDotNet.dll": {}}, "runtime": {"lib/net45/Sick.GenIStreamDotNet.dll": {}}, "build": {"build/Sick.GenIStreamDotNet.props": {}}}, "Sick.Stream.Algorithms.DotNet/********": {"type": "package", "dependencies": {"Sick.GenIStreamDotNet": "4.2.0.20192", "Sick.Stream.Common": "1.2.1"}, "compile": {"lib/netstandard20/Sick.Stream.Algorithms.DotNet.dll": {}}, "runtime": {"lib/netstandard20/Sick.Stream.Algorithms.DotNet.dll": {}}, "build": {"build/net472/Sick.Stream.Algorithms.DotNet.props": {}}}, "Sick.Stream.Common/********": {"type": "package", "dependencies": {"Functional.Maybe": "2.0.20", "System.Numerics.Vectors": "4.6.1"}, "compile": {"lib/netstandard2.0/Sick.Stream.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Common.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Common.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Controls/********": {"type": "package", "dependencies": {"IKVM.WINDWARD": "8.5.0.2", "MaterialDesignColors": "2.0.6", "MaterialDesignThemes": "4.5.0", "OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "OxyPlot.Core": "2.2.0", "OxyPlot.Wpf": "2.2.0", "OxyPlot.Wpf.Shared": "2.2.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Common": "********", "Sick.Stream.Processing": "********", "SuitDotNet": "1.3.190", "System.Buffers": "4.6.1", "System.ComponentModel.Annotations": "5.0.0", "System.Memory": "4.6.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Text.Json": "9.0.3", "System.Threading.Tasks.Extensions": "4.6.2"}, "compile": {"lib/net472/Sick.Stream.Controls.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Sick.Stream.Controls.dll": {"related": ".xml"}}, "resource": {"lib/net472/zh-CN/Sick.Stream.Controls.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Processing/********": {"type": "package", "dependencies": {"DynamicData": "9.2.1", "NLog": "5.4.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Algorithms.DotNet": "********", "Sick.Stream.Common": "********", "Splat": "15.3.1", "System.Reactive": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.6.2", "org.matheval": "1.0.0.3"}, "compile": {"lib/netstandard2.0/Sick.Stream.Processing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Processing.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Processing.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Processing.Tools/********": {"type": "package", "dependencies": {"DynamicData": "9.2.1", "NLog": "5.4.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Algorithms.DotNet": "********", "Sick.Stream.Common": "********", "Sick.Stream.Processing": "********", "Splat": "15.3.1", "System.Reactive": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.6.2", "org.matheval": "1.0.0.3"}, "compile": {"lib/netstandard2.0/Sick.Stream.Processing.Tools.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Processing.Tools.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Processing.Tools.resources.dll": {"locale": "zh-CN"}}}, "Splat/15.3.1": {"type": "package", "compile": {"lib/netstandard2.0/Splat.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Splat.dll": {"related": ".xml"}}}, "SuitDotNet/1.3.190": {"type": "package", "dependencies": {"IKVM.WINDWARD": "8.5.0.2", "OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "System.Buffers": "4.5.1"}, "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System.Windows.Forms", "System.Xaml", "WindowsBase", "WindowsFormsIntegration"], "compile": {"lib/net472/SuitDotNet.dll": {"related": ".pdb"}, "lib/net472/suit-net.dll": {}}, "runtime": {"lib/net472/SuitDotNet.dll": {"related": ".pdb"}, "lib/net472/suit-net.dll": {}}}, "System.AppContext/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net463/System.AppContext.dll": {}}, "runtime": {"lib/net463/System.AppContext.dll": {}}}, "System.Buffers/4.6.1": {"type": "package", "compile": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations", "mscorlib"], "compile": {"ref/net461/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.Console/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Console.dll": {}}, "runtime": {"lib/net46/System.Console.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net462/System.Diagnostics.Tracing.dll": {}}, "runtime": {"lib/net462/System.Diagnostics.Tracing.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Globalization.Calendars/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Globalization.Calendars.dll": {}}, "runtime": {"lib/net46/System.Globalization.Calendars.dll": {}}}, "System.IO/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net462/System.IO.dll": {}}, "runtime": {"lib/net462/System.IO.dll": {}}}, "System.IO.Compression/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.IO.Compression.dll": {}}, "runtime": {"lib/net46/System.IO.Compression.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net46/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "frameworkAssemblies": ["System.IO.Compression.FileSystem", "mscorlib"], "compile": {"ref/net46/System.IO.Compression.ZipFile.dll": {}}, "runtime": {"lib/net46/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"System.IO.FileSystem.Primitives": "4.3.0"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.IO.FileSystem.dll": {}}, "runtime": {"lib/net46/System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.IO.FileSystem.Primitives.dll": {}}, "runtime": {"lib/net46/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.Pipelines/9.0.3": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Linq/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Linq.dll": {}}, "runtime": {"lib/net463/System.Linq.dll": {}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Linq.Expressions.dll": {}}, "runtime": {"lib/net463/System.Linq.Expressions.dll": {}}}, "System.Memory/4.6.2": {"type": "package", "dependencies": {"System.Buffers": "4.6.1", "System.Numerics.Vectors": "4.6.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Net.Http/4.3.4": {"type": "package", "dependencies": {"System.Security.Cryptography.X509Certificates": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net46/System.Net.Http.dll": {}}, "runtime": {"lib/net46/System.Net.Http.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Net.Http.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net46/System.Net.Http.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Net.Sockets/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.Net.Sockets.dll": {}}, "runtime": {"lib/net46/System.Net.Sockets.dll": {}}}, "System.Numerics.Vectors/4.6.1": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reactive/6.0.1": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Windows", "System.Windows.Forms", "WindowsBase"], "compile": {"lib/net472/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.Reactive.dll": {"related": ".xml"}}}, "System.Reflection/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net462/System.Reflection.dll": {}}, "runtime": {"lib/net462/System.Reflection.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core", "mscorlib"], "compile": {"ref/net462/System.Runtime.dll": {}}, "runtime": {"lib/net462/System.Runtime.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net462/System.Runtime.Extensions.dll": {}}, "runtime": {"lib/net462/System.Runtime.Extensions.dll": {}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core"], "compile": {"ref/net46/_._": {}}, "runtime": {"lib/net46/_._": {}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net463/System.Runtime.InteropServices.dll": {}}, "runtime": {"lib/net463/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtime": {"lib/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtimeTargets": {"runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtime": {"lib/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/net46/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net461/System.Security.Cryptography.X509Certificates.dll": {}}, "runtime": {"lib/net461/System.Security.Cryptography.X509Certificates.dll": {}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encodings.Web/9.0.3": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Buffers": "4.5.1", "System.IO.Pipelines": "9.0.3", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net463/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/net463/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Threading.Tasks.Extensions/4.6.2": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"lib/net462/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Timer/4.3.0": {"type": "package", "compile": {"ref/net451/_._": {}}, "runtime": {"lib/net451/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Xml", "mscorlib"], "compile": {"ref/net46/System.Xml.ReaderWriter.dll": {}}, "runtime": {"lib/net46/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Xml.Linq"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}}, ".NETFramework,Version=v4.7.2/win-x64": {"DynamicData/9.2.1": {"type": "package", "dependencies": {"System.Reactive": "6.0.1"}, "compile": {"lib/net462/DynamicData.dll": {"related": ".xml"}}, "runtime": {"lib/net462/DynamicData.dll": {"related": ".xml"}}}, "Functional.Maybe/2.0.20": {"type": "package", "dependencies": {"NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.0/Functional.Maybe.dll": {}}, "runtime": {"lib/netstandard1.0/Functional.Maybe.dll": {}}}, "IKVM.WINDWARD/8.5.0.2": {"type": "package", "compile": {"lib/IKVM.AWT.WinForms.dll": {}, "lib/IKVM.OpenJDK.Beans.dll": {}, "lib/IKVM.OpenJDK.Charsets.dll": {}, "lib/IKVM.OpenJDK.Cldrdata.dll": {}, "lib/IKVM.OpenJDK.Corba.dll": {}, "lib/IKVM.OpenJDK.Core.dll": {}, "lib/IKVM.OpenJDK.Jdbc.dll": {}, "lib/IKVM.OpenJDK.Localedata.dll": {}, "lib/IKVM.OpenJDK.Management.dll": {}, "lib/IKVM.OpenJDK.Media.dll": {}, "lib/IKVM.OpenJDK.Misc.dll": {}, "lib/IKVM.OpenJDK.Naming.dll": {}, "lib/IKVM.OpenJDK.Nashorn.dll": {}, "lib/IKVM.OpenJDK.Remoting.dll": {}, "lib/IKVM.OpenJDK.Security.dll": {}, "lib/IKVM.OpenJDK.SwingAWT.dll": {}, "lib/IKVM.OpenJDK.Text.dll": {}, "lib/IKVM.OpenJDK.Tools.dll": {}, "lib/IKVM.OpenJDK.Util.dll": {}, "lib/IKVM.OpenJDK.XML.API.dll": {}, "lib/IKVM.OpenJDK.XML.Bind.dll": {}, "lib/IKVM.OpenJDK.XML.Crypto.dll": {}, "lib/IKVM.OpenJDK.XML.Parse.dll": {}, "lib/IKVM.OpenJDK.XML.Transform.dll": {}, "lib/IKVM.OpenJDK.XML.WebServices.dll": {}, "lib/IKVM.OpenJDK.XML.XPath.dll": {}, "lib/IKVM.Reflection.dll": {}, "lib/IKVM.Runtime.JNI.dll": {}, "lib/IKVM.Runtime.dll": {}}, "runtime": {"lib/IKVM.AWT.WinForms.dll": {}, "lib/IKVM.OpenJDK.Beans.dll": {}, "lib/IKVM.OpenJDK.Charsets.dll": {}, "lib/IKVM.OpenJDK.Cldrdata.dll": {}, "lib/IKVM.OpenJDK.Corba.dll": {}, "lib/IKVM.OpenJDK.Core.dll": {}, "lib/IKVM.OpenJDK.Jdbc.dll": {}, "lib/IKVM.OpenJDK.Localedata.dll": {}, "lib/IKVM.OpenJDK.Management.dll": {}, "lib/IKVM.OpenJDK.Media.dll": {}, "lib/IKVM.OpenJDK.Misc.dll": {}, "lib/IKVM.OpenJDK.Naming.dll": {}, "lib/IKVM.OpenJDK.Nashorn.dll": {}, "lib/IKVM.OpenJDK.Remoting.dll": {}, "lib/IKVM.OpenJDK.Security.dll": {}, "lib/IKVM.OpenJDK.SwingAWT.dll": {}, "lib/IKVM.OpenJDK.Text.dll": {}, "lib/IKVM.OpenJDK.Tools.dll": {}, "lib/IKVM.OpenJDK.Util.dll": {}, "lib/IKVM.OpenJDK.XML.API.dll": {}, "lib/IKVM.OpenJDK.XML.Bind.dll": {}, "lib/IKVM.OpenJDK.XML.Crypto.dll": {}, "lib/IKVM.OpenJDK.XML.Parse.dll": {}, "lib/IKVM.OpenJDK.XML.Transform.dll": {}, "lib/IKVM.OpenJDK.XML.WebServices.dll": {}, "lib/IKVM.OpenJDK.XML.XPath.dll": {}, "lib/IKVM.Reflection.dll": {}, "lib/IKVM.Runtime.JNI.dll": {}, "lib/IKVM.Runtime.dll": {}}}, "MaterialDesignColors/2.0.6": {"type": "package", "compile": {"lib/net452/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net452/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/4.5.0": {"type": "package", "dependencies": {"MaterialDesignColors": "[2.0.6, 3.0.0)"}, "compile": {"lib/net452/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net452/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "runtime": {"lib/net462/Microsoft.Bcl.AsyncInterfaces.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.2": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net472": "1.0.2"}}, "Microsoft.NETFramework.ReferenceAssemblies.net472/1.0.2": {"type": "package", "build": {"build/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/Microsoft.Win32.Primitives.dll": {}}, "runtime": {"lib/net46/Microsoft.Win32.Primitives.dll": {}}}, "NETStandard.Library/1.6.1": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.AppContext": "4.3.0", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Console": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.Compression": "4.3.0", "System.IO.Compression.ZipFile": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Net.Http": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Timer": "4.3.0", "System.Xml.ReaderWriter": "4.3.0", "System.Xml.XDocument": "4.3.0"}}, "NLog/5.4.0": {"type": "package", "frameworkAssemblies": ["System", "System.Configuration", "System.Core", "System.IO.Compression", "System.Xml"], "compile": {"lib/net46/NLog.dll": {"related": ".xml"}}, "runtime": {"lib/net46/NLog.dll": {"related": ".xml"}}}, "OpenTK/3.1.0": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLControl/3.1.0": {"type": "package", "dependencies": {"OpenTK": "3.1.0"}, "compile": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.GLControl.dll": {"related": ".pdb;.xml"}}}, "org.matheval/1.0.0.3": {"type": "package", "compile": {"lib/net472/org.matheval.dll": {}}, "runtime": {"lib/net472/org.matheval.dll": {}}}, "OxyPlot.Core/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2"}, "compile": {"lib/net462/OxyPlot.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.dll": {"related": ".xml"}}}, "OxyPlot.Wpf/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2", "OxyPlot.Core": "2.2.0", "OxyPlot.Wpf.Shared": "2.2.0"}, "frameworkAssemblies": ["ReachFramework", "System.Printing"], "compile": {"lib/net462/OxyPlot.Wpf.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.Wpf.dll": {"related": ".xml"}}}, "OxyPlot.Wpf.Shared/2.2.0": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": "1.0.2", "OxyPlot.Core": "2.2.0"}, "compile": {"lib/net462/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}, "runtime": {"lib/net462/OxyPlot.Wpf.Shared.dll": {"related": ".xml"}}}, "Sick.GenIStreamDotNet/4.2.0.20192": {"type": "package", "compile": {"lib/net45/Sick.GenIStreamDotNet.dll": {}}, "runtime": {"lib/net45/Sick.GenIStreamDotNet.dll": {}}, "build": {"build/Sick.GenIStreamDotNet.props": {}}}, "Sick.Stream.Algorithms.DotNet/********": {"type": "package", "dependencies": {"Sick.GenIStreamDotNet": "4.2.0.20192", "Sick.Stream.Common": "1.2.1"}, "compile": {"lib/netstandard20/Sick.Stream.Algorithms.DotNet.dll": {}}, "runtime": {"lib/netstandard20/Sick.Stream.Algorithms.DotNet.dll": {}}, "build": {"build/net472/Sick.Stream.Algorithms.DotNet.props": {}}}, "Sick.Stream.Common/********": {"type": "package", "dependencies": {"Functional.Maybe": "2.0.20", "System.Numerics.Vectors": "4.6.1"}, "compile": {"lib/netstandard2.0/Sick.Stream.Common.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Common.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Common.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Controls/********": {"type": "package", "dependencies": {"IKVM.WINDWARD": "8.5.0.2", "MaterialDesignColors": "2.0.6", "MaterialDesignThemes": "4.5.0", "OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "OxyPlot.Core": "2.2.0", "OxyPlot.Wpf": "2.2.0", "OxyPlot.Wpf.Shared": "2.2.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Common": "********", "Sick.Stream.Processing": "********", "SuitDotNet": "1.3.190", "System.Buffers": "4.6.1", "System.ComponentModel.Annotations": "5.0.0", "System.Memory": "4.6.2", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Text.Json": "9.0.3", "System.Threading.Tasks.Extensions": "4.6.2"}, "compile": {"lib/net472/Sick.Stream.Controls.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Sick.Stream.Controls.dll": {"related": ".xml"}}, "resource": {"lib/net472/zh-CN/Sick.Stream.Controls.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Processing/********": {"type": "package", "dependencies": {"DynamicData": "9.2.1", "NLog": "5.4.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Algorithms.DotNet": "********", "Sick.Stream.Common": "********", "Splat": "15.3.1", "System.Reactive": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.6.2", "org.matheval": "1.0.0.3"}, "compile": {"lib/netstandard2.0/Sick.Stream.Processing.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Processing.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Processing.resources.dll": {"locale": "zh-CN"}}}, "Sick.Stream.Processing.Tools/********": {"type": "package", "dependencies": {"DynamicData": "9.2.1", "NLog": "5.4.0", "Sick.GenIStreamDotNet": "1.0.0", "Sick.Stream.Algorithms.DotNet": "********", "Sick.Stream.Common": "********", "Sick.Stream.Processing": "********", "Splat": "15.3.1", "System.Reactive": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1", "System.Threading.Tasks.Extensions": "4.6.2", "org.matheval": "1.0.0.3"}, "compile": {"lib/netstandard2.0/Sick.Stream.Processing.Tools.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Sick.Stream.Processing.Tools.dll": {"related": ".xml"}}, "resource": {"lib/netstandard2.0/zh-CN/Sick.Stream.Processing.Tools.resources.dll": {"locale": "zh-CN"}}}, "Splat/15.3.1": {"type": "package", "compile": {"lib/netstandard2.0/Splat.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Splat.dll": {"related": ".xml"}}}, "SuitDotNet/1.3.190": {"type": "package", "dependencies": {"IKVM.WINDWARD": "8.5.0.2", "OpenTK": "3.1.0", "OpenTK.GLControl": "3.1.0", "System.Buffers": "4.5.1"}, "frameworkAssemblies": ["PresentationCore", "PresentationFramework", "System.Windows.Forms", "System.Xaml", "WindowsBase", "WindowsFormsIntegration"], "compile": {"lib/net472/SuitDotNet.dll": {"related": ".pdb"}, "lib/net472/suit-net.dll": {}}, "runtime": {"lib/net472/SuitDotNet.dll": {"related": ".pdb"}, "lib/net472/suit-net.dll": {}}}, "System.AppContext/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net463/System.AppContext.dll": {}}, "runtime": {"lib/net463/System.AppContext.dll": {}}}, "System.Buffers/4.6.1": {"type": "package", "compile": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Buffers.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Collections.Concurrent/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "frameworkAssemblies": ["System.ComponentModel.DataAnnotations", "mscorlib"], "compile": {"ref/net461/System.ComponentModel.Annotations.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.ComponentModel.Annotations.dll": {}}}, "System.Console/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Console.dll": {}}, "runtime": {"lib/net46/System.Console.dll": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net462/System.Diagnostics.Tracing.dll": {}}, "runtime": {"lib/net462/System.Diagnostics.Tracing.dll": {}}}, "System.Globalization/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Globalization.Calendars/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Globalization.Calendars.dll": {}}, "runtime": {"lib/net46/System.Globalization.Calendars.dll": {}}}, "System.IO/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net462/System.IO.dll": {}}, "runtime": {"lib/net462/System.IO.dll": {}}}, "System.IO.Compression/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.IO.Compression.dll": {}}, "runtime": {"runtimes/win/lib/net46/System.IO.Compression.dll": {}}}, "System.IO.Compression.ZipFile/4.3.0": {"type": "package", "frameworkAssemblies": ["System.IO.Compression.FileSystem", "mscorlib"], "compile": {"ref/net46/System.IO.Compression.ZipFile.dll": {}}, "runtime": {"lib/net46/System.IO.Compression.ZipFile.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"System.IO.FileSystem.Primitives": "4.3.0"}, "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.IO.FileSystem.dll": {}}, "runtime": {"lib/net46/System.IO.FileSystem.dll": {}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.IO.FileSystem.Primitives.dll": {}}, "runtime": {"lib/net46/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.Pipelines/9.0.3": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"lib/net462/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Linq/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Linq.dll": {}}, "runtime": {"lib/net463/System.Linq.dll": {}}}, "System.Linq.Expressions/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Linq.Expressions.dll": {}}, "runtime": {"lib/net463/System.Linq.Expressions.dll": {}}}, "System.Memory/4.6.2": {"type": "package", "dependencies": {"System.Buffers": "4.6.1", "System.Numerics.Vectors": "4.6.1", "System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Memory.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Net.Http/4.3.4": {"type": "package", "dependencies": {"System.Security.Cryptography.X509Certificates": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net46/System.Net.Http.dll": {}}, "runtime": {"runtimes/win/lib/net46/System.Net.Http.dll": {}}}, "System.Net.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Net.Sockets/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.Net.Sockets.dll": {}}, "runtime": {"lib/net46/System.Net.Sockets.dll": {}}}, "System.Numerics.Vectors/4.6.1": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Numerics.Vectors.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.ObjectModel/4.3.0": {"type": "package", "frameworkAssemblies": ["System"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reactive/6.0.1": {"type": "package", "dependencies": {"System.Threading.Tasks.Extensions": "4.5.4"}, "frameworkAssemblies": ["System.Windows", "System.Windows.Forms", "WindowsBase"], "compile": {"lib/net472/System.Reactive.dll": {"related": ".xml"}}, "runtime": {"lib/net472/System.Reactive.dll": {"related": ".xml"}}}, "System.Reflection/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net462/System.Reflection.dll": {}}, "runtime": {"lib/net462/System.Reflection.dll": {}}}, "System.Reflection.Extensions/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Runtime/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.ComponentModel.Composition", "System.Core", "mscorlib"], "compile": {"ref/net462/System.Runtime.dll": {}}, "runtime": {"lib/net462/System.Runtime.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net462/System.Runtime.Extensions.dll": {}}, "runtime": {"lib/net462/System.Runtime.Extensions.dll": {}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core"], "compile": {"ref/net46/_._": {}}, "runtime": {"lib/net46/_._": {}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net463/System.Runtime.InteropServices.dll": {}}, "runtime": {"lib/net463/System.Runtime.InteropServices.dll": {}}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "compile": {"ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "runtime": {"runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll": {}}}, "System.Runtime.Numerics/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Numerics"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "dependencies": {"System.IO": "4.3.0", "System.Runtime": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0"}, "frameworkAssemblies": ["System.Core", "mscorlib"], "compile": {"ref/net463/System.Security.Cryptography.Algorithms.dll": {}}, "runtime": {"runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll": {}}}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Encoding.dll": {}}, "runtime": {"runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll": {}}}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net46/System.Security.Cryptography.Primitives.dll": {}}, "runtime": {"lib/net46/System.Security.Cryptography.Primitives.dll": {}}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "dependencies": {"System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0"}, "frameworkAssemblies": ["System", "System.Core", "mscorlib"], "compile": {"ref/net461/System.Security.Cryptography.X509Certificates.dll": {}}, "runtime": {"runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll": {}}}, "System.Text.Encoding/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Text.Encodings.Web/9.0.3": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Text.Json/9.0.3": {"type": "package", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "9.0.3", "System.Buffers": "4.5.1", "System.IO.Pipelines": "9.0.3", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encodings.Web": "9.0.3", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}, "compile": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/System.Text.Json.targets": {}}}, "System.Text.RegularExpressions/4.3.1": {"type": "package", "frameworkAssemblies": ["System", "mscorlib"], "compile": {"ref/net463/System.Text.RegularExpressions.dll": {}}, "runtime": {"lib/net463/System.Text.RegularExpressions.dll": {}}}, "System.Threading/4.3.0": {"type": "package", "frameworkAssemblies": ["System", "System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Core"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}, "System.Threading.Tasks.Extensions/4.6.2": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.1.1"}, "compile": {"lib/net462/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Threading.Tasks.Extensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Threading.Timer/4.3.0": {"type": "package", "compile": {"ref/net451/_._": {}}, "runtime": {"lib/net451/_._": {}}}, "System.ValueTuple/4.5.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net47/System.ValueTuple.dll": {}}, "runtime": {"lib/net47/System.ValueTuple.dll": {"related": ".xml"}}}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Xml", "mscorlib"], "compile": {"ref/net46/System.Xml.ReaderWriter.dll": {}}, "runtime": {"lib/net46/System.Xml.ReaderWriter.dll": {}}}, "System.Xml.XDocument/4.3.0": {"type": "package", "frameworkAssemblies": ["System.Xml.Linq"], "compile": {"ref/net45/_._": {}}, "runtime": {"lib/net45/_._": {}}}}}, "libraries": {"DynamicData/9.2.1": {"sha512": "1Eh0D0emAwWB5wGDbT1VY1uttUpslrlkC/jVpw7/UnBfdMFhrq647rX5Ke7JDAm/a/4AoU/cLzPlcP3P3nfT+w==", "type": "package", "path": "dynamicdata/9.2.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "README.md", "dynamicdata.9.2.1.nupkg.sha512", "dynamicdata.nuspec", "lib/net462/DynamicData.dll", "lib/net462/DynamicData.xml", "lib/net6.0/DynamicData.dll", "lib/net6.0/DynamicData.xml", "lib/net7.0/DynamicData.dll", "lib/net7.0/DynamicData.xml", "lib/net8.0/DynamicData.dll", "lib/net8.0/DynamicData.xml", "lib/netstandard2.0/DynamicData.dll", "lib/netstandard2.0/DynamicData.xml", "logo.png"]}, "Functional.Maybe/2.0.20": {"sha512": "Qa4K6Dy/e9lKew4nrrKu5Hwngs12vZnClAzycxmIfix6FnVqPq+8FLMKm3s7oJoNrTlVpzLF8T1PtXtj3YFP1w==", "type": "package", "path": "functional.maybe/2.0.20", "files": [".nupkg.metadata", ".signature.p7s", "functional.maybe.2.0.20.nupkg.sha512", "functional.maybe.nuspec", "lib/netstandard1.0/Functional.Maybe.dll"]}, "IKVM.WINDWARD/8.5.0.2": {"sha512": "iFCkc0VIeW8orYdYnRIZwQeLydktwktIA237YLq593aBk8GctbSkJbWmp0zfyBbm+FUuCJtHWAM9nQne1AuwYg==", "type": "package", "path": "ikvm.windward/8.5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "THIRD_PARTY_README", "TRADEMARK", "ikvm.windward.8.5.0.2.nupkg.sha512", "ikvm.windward.nuspec", "lib/IKVM.AWT.WinForms.dll", "lib/IKVM.OpenJDK.Beans.dll", "lib/IKVM.OpenJDK.Charsets.dll", "lib/IKVM.OpenJDK.Cldrdata.dll", "lib/IKVM.OpenJDK.Corba.dll", "lib/IKVM.OpenJDK.Core.dll", "lib/IKVM.OpenJDK.Jdbc.dll", "lib/IKVM.OpenJDK.Localedata.dll", "lib/IKVM.OpenJDK.Management.dll", "lib/IKVM.OpenJDK.Media.dll", "lib/IKVM.OpenJDK.Misc.dll", "lib/IKVM.OpenJDK.Naming.dll", "lib/IKVM.OpenJDK.Nashorn.dll", "lib/IKVM.OpenJDK.Remoting.dll", "lib/IKVM.OpenJDK.Security.dll", "lib/IKVM.OpenJDK.SwingAWT.dll", "lib/IKVM.OpenJDK.Text.dll", "lib/IKVM.OpenJDK.Tools.dll", "lib/IKVM.OpenJDK.Util.dll", "lib/IKVM.OpenJDK.XML.API.dll", "lib/IKVM.OpenJDK.XML.Bind.dll", "lib/IKVM.OpenJDK.XML.Crypto.dll", "lib/IKVM.OpenJDK.XML.Parse.dll", "lib/IKVM.OpenJDK.XML.Transform.dll", "lib/IKVM.OpenJDK.XML.WebServices.dll", "lib/IKVM.OpenJDK.XML.XPath.dll", "lib/IKVM.Reflection.dll", "lib/IKVM.Runtime.JNI.dll", "lib/IKVM.Runtime.dll", "x64/lib/JVM.DLL", "x86/lib/JVM.DLL"]}, "MaterialDesignColors/2.0.6": {"sha512": "eZNiLlMy7Ag9f3MnhVxSzkG2IIGCaRfqZKt6npEcEjLQ9kSgCYO5oUd8AgJjhpfCKwyf+GO5VDc5R88QSDTHHg==", "type": "package", "path": "materialdesigncolors/2.0.6", "files": [".nupkg.metadata", ".signature.p7s", "images/MaterialDesignColors.Icon.png", "lib/net452/MaterialDesignColors.dll", "lib/net452/MaterialDesignColors.pdb", "lib/netcoreapp3.1/MaterialDesignColors.dll", "lib/netcoreapp3.1/MaterialDesignColors.pdb", "materialdesigncolors.2.0.6.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/4.5.0": {"sha512": "G26hIGFwGH5q46CD59zGiAs4aLsASsKCCBqgnISPwIcq8pAfKRRAKxXfOQJgNDyIUnVm5Ug67gldsEgeWP6GoQ==", "type": "package", "path": "materialdesignthemes/4.5.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "images/MaterialDesignThemes.Icon.png", "lib/net452/MaterialDesignThemes.Wpf.dll", "lib/net452/MaterialDesignThemes.Wpf.pdb", "lib/net452/MaterialDesignThemes.Wpf.xml", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.dll", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.pdb", "lib/netcoreapp3.1/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.4.5.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Bcl.AsyncInterfaces/9.0.3": {"sha512": "oFFX9Ls8dnNUBCD9yzRzHTY8tqvv+CiX43B8L8DjrM8BqYTAlORYaJf6+KXNtSC2bD1135yV8OxzcZFaluow5w==", "type": "package", "path": "microsoft.bcl.asyncinterfaces/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Bcl.AsyncInterfaces.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Bcl.AsyncInterfaces.targets", "lib/net462/Microsoft.Bcl.AsyncInterfaces.dll", "lib/net462/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.0/Microsoft.Bcl.AsyncInterfaces.xml", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll", "lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.xml", "microsoft.bcl.asyncinterfaces.9.0.3.nupkg.sha512", "microsoft.bcl.asyncinterfaces.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/1.1.0": {"sha512": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "type": "package", "path": "microsoft.netcore.platforms/1.1.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.1.1.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json"]}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.2": {"sha512": "5/cSEVld+px/CuRrbohO/djfg6++eR6zGpy88MgqloXvkj//WXWpFZyu/OpkXPN0u5m+dN/EVwLNYFUxD4h2+A==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.2.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net472/1.0.2": {"sha512": "ryAuwkvjMC9xYQ0VXsG7ZBo62y5tBmYaCnovOL5IXfnQPQqjvJGRkLMDyUx+dnCb96UVLJv83R6XK+sRnDnaZg==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net472/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.7.2/Accessibility.dll", "build/.NETFramework/v4.7.2/Accessibility.xml", "build/.NETFramework/v4.7.2/CustomMarshalers.dll", "build/.NETFramework/v4.7.2/CustomMarshalers.xml", "build/.NETFramework/v4.7.2/Facades/Microsoft.Win32.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.AppContext.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.NonGeneric.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.Specialized.dll", "build/.NETFramework/v4.7.2/Facades/System.Collections.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.TypeConverter.dll", "build/.NETFramework/v4.7.2/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.7.2/Facades/System.Console.dll", "build/.NETFramework/v4.7.2/Facades/System.Data.Common.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.FileVersionInfo.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Process.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.StackTrace.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.TextWriterTraceListener.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.7.2/Facades/System.Diagnostics.TraceSource.dll", "build/.NETFramework/v4.7.2/Facades/System.Drawing.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.Calendars.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Globalization.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.Compression.ZipFile.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.DriveInfo.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.Watcher.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.FileSystem.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.IsolatedStorage.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.MemoryMappedFiles.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.Pipes.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.UnmanagedMemoryStream.dll", "build/.NETFramework/v4.7.2/Facades/System.IO.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.7.2/Facades/System.Linq.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Http.Rtc.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.NameResolution.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Ping.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Security.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.Sockets.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebSockets.Client.dll", "build/.NETFramework/v4.7.2/Facades/System.Net.WebSockets.dll", "build/.NETFramework/v4.7.2/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Reflection.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.Reader.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.7.2/Facades/System.Resources.Writer.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.CompilerServices.VisualC.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.RuntimeInformation.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Formatters.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.7.2/Facades/System.Runtime.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Claims.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Algorithms.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Csp.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Encoding.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Cryptography.X509Certificates.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.7.2/Facades/System.Security.SecureString.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.7.2/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.7.2/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Overlapped.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Thread.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.ThreadPool.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.7.2/Facades/System.Threading.dll", "build/.NETFramework/v4.7.2/Facades/System.ValueTuple.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XPath.XDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XPath.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XmlDocument.dll", "build/.NETFramework/v4.7.2/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.7.2/Facades/netstandard.dll", "build/.NETFramework/v4.7.2/ISymWrapper.dll", "build/.NETFramework/v4.7.2/ISymWrapper.xml", "build/.NETFramework/v4.7.2/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.7.2/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.7.2/Microsoft.Build.dll", "build/.NETFramework/v4.7.2/Microsoft.Build.xml", "build/.NETFramework/v4.7.2/Microsoft.CSharp.dll", "build/.NETFramework/v4.7.2/Microsoft.CSharp.xml", "build/.NETFramework/v4.7.2/Microsoft.JScript.dll", "build/.NETFramework/v4.7.2/Microsoft.JScript.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.7.2/Microsoft.VisualC.dll", "build/.NETFramework/v4.7.2/Microsoft.VisualC.xml", "build/.NETFramework/v4.7.2/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.7.2/PermissionSets/Internet.xml", "build/.NETFramework/v4.7.2/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.7.2/PresentationBuildTasks.dll", "build/.NETFramework/v4.7.2/PresentationBuildTasks.xml", "build/.NETFramework/v4.7.2/PresentationCore.dll", "build/.NETFramework/v4.7.2/PresentationCore.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Aero.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Aero.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.7.2/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.7.2/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Classic.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Classic.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Luna.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Luna.xml", "build/.NETFramework/v4.7.2/PresentationFramework.Royale.dll", "build/.NETFramework/v4.7.2/PresentationFramework.Royale.xml", "build/.NETFramework/v4.7.2/PresentationFramework.dll", "build/.NETFramework/v4.7.2/PresentationFramework.xml", "build/.NETFramework/v4.7.2/ReachFramework.dll", "build/.NETFramework/v4.7.2/ReachFramework.xml", "build/.NETFramework/v4.7.2/RedistList/FrameworkList.xml", "build/.NETFramework/v4.7.2/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.7.2/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.7.2/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.7.2/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.7.2/System.Activities.Presentation.dll", "build/.NETFramework/v4.7.2/System.Activities.Presentation.xml", "build/.NETFramework/v4.7.2/System.Activities.dll", "build/.NETFramework/v4.7.2/System.Activities.xml", "build/.NETFramework/v4.7.2/System.AddIn.Contract.dll", "build/.NETFramework/v4.7.2/System.AddIn.Contract.xml", "build/.NETFramework/v4.7.2/System.AddIn.dll", "build/.NETFramework/v4.7.2/System.AddIn.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.7.2/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.7.2/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.7.2/System.Configuration.Install.dll", "build/.NETFramework/v4.7.2/System.Configuration.Install.xml", "build/.NETFramework/v4.7.2/System.Configuration.dll", "build/.NETFramework/v4.7.2/System.Configuration.xml", "build/.NETFramework/v4.7.2/System.Core.dll", "build/.NETFramework/v4.7.2/System.Core.xml", "build/.NETFramework/v4.7.2/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.7.2/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.7.2/System.Data.Entity.Design.dll", "build/.NETFramework/v4.7.2/System.Data.Entity.Design.xml", "build/.NETFramework/v4.7.2/System.Data.Entity.dll", "build/.NETFramework/v4.7.2/System.Data.Entity.xml", "build/.NETFramework/v4.7.2/System.Data.Linq.dll", "build/.NETFramework/v4.7.2/System.Data.Linq.xml", "build/.NETFramework/v4.7.2/System.Data.OracleClient.dll", "build/.NETFramework/v4.7.2/System.Data.OracleClient.xml", "build/.NETFramework/v4.7.2/System.Data.Services.Client.dll", "build/.NETFramework/v4.7.2/System.Data.Services.Client.xml", "build/.NETFramework/v4.7.2/System.Data.Services.Design.dll", "build/.NETFramework/v4.7.2/System.Data.Services.Design.xml", "build/.NETFramework/v4.7.2/System.Data.Services.dll", "build/.NETFramework/v4.7.2/System.Data.Services.xml", "build/.NETFramework/v4.7.2/System.Data.SqlXml.dll", "build/.NETFramework/v4.7.2/System.Data.SqlXml.xml", "build/.NETFramework/v4.7.2/System.Data.dll", "build/.NETFramework/v4.7.2/System.Data.xml", "build/.NETFramework/v4.7.2/System.Deployment.dll", "build/.NETFramework/v4.7.2/System.Deployment.xml", "build/.NETFramework/v4.7.2/System.Design.dll", "build/.NETFramework/v4.7.2/System.Design.xml", "build/.NETFramework/v4.7.2/System.Device.dll", "build/.NETFramework/v4.7.2/System.Device.xml", "build/.NETFramework/v4.7.2/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.7.2/System.Diagnostics.Tracing.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.7.2/System.DirectoryServices.dll", "build/.NETFramework/v4.7.2/System.DirectoryServices.xml", "build/.NETFramework/v4.7.2/System.Drawing.Design.dll", "build/.NETFramework/v4.7.2/System.Drawing.Design.xml", "build/.NETFramework/v4.7.2/System.Drawing.dll", "build/.NETFramework/v4.7.2/System.Drawing.xml", "build/.NETFramework/v4.7.2/System.Dynamic.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.dll", "build/.NETFramework/v4.7.2/System.EnterpriseServices.xml", "build/.NETFramework/v4.7.2/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.7.2/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.7.2/System.IO.Compression.dll", "build/.NETFramework/v4.7.2/System.IO.Compression.xml", "build/.NETFramework/v4.7.2/System.IO.Log.dll", "build/.NETFramework/v4.7.2/System.IO.Log.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.Services.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.Services.xml", "build/.NETFramework/v4.7.2/System.IdentityModel.dll", "build/.NETFramework/v4.7.2/System.IdentityModel.xml", "build/.NETFramework/v4.7.2/System.Linq.xml", "build/.NETFramework/v4.7.2/System.Management.Instrumentation.dll", "build/.NETFramework/v4.7.2/System.Management.Instrumentation.xml", "build/.NETFramework/v4.7.2/System.Management.dll", "build/.NETFramework/v4.7.2/System.Management.xml", "build/.NETFramework/v4.7.2/System.Messaging.dll", "build/.NETFramework/v4.7.2/System.Messaging.xml", "build/.NETFramework/v4.7.2/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.7.2/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.7.2/System.Net.Http.dll", "build/.NETFramework/v4.7.2/System.Net.Http.xml", "build/.NETFramework/v4.7.2/System.Net.dll", "build/.NETFramework/v4.7.2/System.Net.xml", "build/.NETFramework/v4.7.2/System.Numerics.dll", "build/.NETFramework/v4.7.2/System.Numerics.xml", "build/.NETFramework/v4.7.2/System.Printing.dll", "build/.NETFramework/v4.7.2/System.Printing.xml", "build/.NETFramework/v4.7.2/System.Reflection.Context.dll", "build/.NETFramework/v4.7.2/System.Reflection.Context.xml", "build/.NETFramework/v4.7.2/System.Runtime.Caching.dll", "build/.NETFramework/v4.7.2/System.Runtime.Caching.xml", "build/.NETFramework/v4.7.2/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.7.2/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.7.2/System.Runtime.Remoting.dll", "build/.NETFramework/v4.7.2/System.Runtime.Remoting.xml", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.dll", "build/.NETFramework/v4.7.2/System.Runtime.Serialization.xml", "build/.NETFramework/v4.7.2/System.Security.dll", "build/.NETFramework/v4.7.2/System.Security.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.Web.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.Web.xml", "build/.NETFramework/v4.7.2/System.ServiceModel.dll", "build/.NETFramework/v4.7.2/System.ServiceModel.xml", "build/.NETFramework/v4.7.2/System.ServiceProcess.dll", "build/.NETFramework/v4.7.2/System.ServiceProcess.xml", "build/.NETFramework/v4.7.2/System.Speech.dll", "build/.NETFramework/v4.7.2/System.Speech.xml", "build/.NETFramework/v4.7.2/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.7.2/System.Transactions.dll", "build/.NETFramework/v4.7.2/System.Transactions.xml", "build/.NETFramework/v4.7.2/System.Web.Abstractions.dll", "build/.NETFramework/v4.7.2/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.7.2/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.dll", "build/.NETFramework/v4.7.2/System.Web.DataVisualization.xml", "build/.NETFramework/v4.7.2/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.7.2/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.7.2/System.Web.DynamicData.dll", "build/.NETFramework/v4.7.2/System.Web.DynamicData.xml", "build/.NETFramework/v4.7.2/System.Web.Entity.Design.dll", "build/.NETFramework/v4.7.2/System.Web.Entity.Design.xml", "build/.NETFramework/v4.7.2/System.Web.Entity.dll", "build/.NETFramework/v4.7.2/System.Web.Entity.xml", "build/.NETFramework/v4.7.2/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.7.2/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.7.2/System.Web.Extensions.dll", "build/.NETFramework/v4.7.2/System.Web.Extensions.xml", "build/.NETFramework/v4.7.2/System.Web.Mobile.dll", "build/.NETFramework/v4.7.2/System.Web.Mobile.xml", "build/.NETFramework/v4.7.2/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.7.2/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.7.2/System.Web.Routing.dll", "build/.NETFramework/v4.7.2/System.Web.Services.dll", "build/.NETFramework/v4.7.2/System.Web.Services.xml", "build/.NETFramework/v4.7.2/System.Web.dll", "build/.NETFramework/v4.7.2/System.Web.xml", "build/.NETFramework/v4.7.2/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.7.2/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.7.2/System.Windows.Forms.dll", "build/.NETFramework/v4.7.2/System.Windows.Forms.xml", "build/.NETFramework/v4.7.2/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.7.2/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.7.2/System.Windows.Presentation.dll", "build/.NETFramework/v4.7.2/System.Windows.Presentation.xml", "build/.NETFramework/v4.7.2/System.Windows.dll", "build/.NETFramework/v4.7.2/System.Workflow.Activities.dll", "build/.NETFramework/v4.7.2/System.Workflow.Activities.xml", "build/.NETFramework/v4.7.2/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.7.2/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.7.2/System.Workflow.Runtime.dll", "build/.NETFramework/v4.7.2/System.Workflow.Runtime.xml", "build/.NETFramework/v4.7.2/System.WorkflowServices.dll", "build/.NETFramework/v4.7.2/System.WorkflowServices.xml", "build/.NETFramework/v4.7.2/System.Xaml.dll", "build/.NETFramework/v4.7.2/System.Xaml.xml", "build/.NETFramework/v4.7.2/System.Xml.Linq.dll", "build/.NETFramework/v4.7.2/System.Xml.Linq.xml", "build/.NETFramework/v4.7.2/System.Xml.Serialization.dll", "build/.NETFramework/v4.7.2/System.Xml.dll", "build/.NETFramework/v4.7.2/System.Xml.xml", "build/.NETFramework/v4.7.2/System.dll", "build/.NETFramework/v4.7.2/System.xml", "build/.NETFramework/v4.7.2/UIAutomationClient.dll", "build/.NETFramework/v4.7.2/UIAutomationClient.xml", "build/.NETFramework/v4.7.2/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.7.2/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.7.2/UIAutomationProvider.dll", "build/.NETFramework/v4.7.2/UIAutomationProvider.xml", "build/.NETFramework/v4.7.2/UIAutomationTypes.dll", "build/.NETFramework/v4.7.2/UIAutomationTypes.xml", "build/.NETFramework/v4.7.2/WindowsBase.dll", "build/.NETFramework/v4.7.2/WindowsBase.xml", "build/.NETFramework/v4.7.2/WindowsFormsIntegration.dll", "build/.NETFramework/v4.7.2/WindowsFormsIntegration.xml", "build/.NETFramework/v4.7.2/XamlBuildTask.dll", "build/.NETFramework/v4.7.2/XamlBuildTask.xml", "build/.NETFramework/v4.7.2/mscorlib.dll", "build/.NETFramework/v4.7.2/mscorlib.xml", "build/.NETFramework/v4.7.2/namespaces.xml", "build/.NETFramework/v4.7.2/sysglobl.dll", "build/.NETFramework/v4.7.2/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net472.targets", "microsoft.netframework.referenceassemblies.net472.1.0.2.nupkg.sha512", "microsoft.netframework.referenceassemblies.net472.nuspec"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "NETStandard.Library/1.6.1": {"sha512": "WcSp3+vP+yHNgS8EV5J7pZ9IRpeDuARBPN28by8zqff1wJQXm26PVU8L3/fYLBJVU7BtDyqNVWq2KlCVvSSR4A==", "type": "package", "path": "netstandard.library/1.6.1", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "netstandard.library.1.6.1.nupkg.sha512", "netstandard.library.nuspec"]}, "NLog/5.4.0": {"sha512": "LwMcGSW3soF3/SL68rlJN3Eh3ktrAPycC3zZR/07OYBPraZUu0bygEC7kIN10lUQgMXT4s84Fi1chglGdGrQEg==", "type": "package", "path": "nlog/5.4.0", "files": [".nupkg.metadata", ".signature.p7s", "N.png", "lib/net35/NLog.dll", "lib/net35/NLog.xml", "lib/net45/NLog.dll", "lib/net45/NLog.xml", "lib/net46/NLog.dll", "lib/net46/NLog.xml", "lib/netstandard1.3/NLog.dll", "lib/netstandard1.3/NLog.xml", "lib/netstandard1.5/NLog.dll", "lib/netstandard1.5/NLog.xml", "lib/netstandard2.0/NLog.dll", "lib/netstandard2.0/NLog.xml", "nlog.5.4.0.nupkg.sha512", "nlog.nuspec"]}, "OpenTK/3.1.0": {"sha512": "xoP5kb81lRSY5RzbntaFA9yXK+aGF5SLShhns55MyZtxtNafIxRH5KvRTFss/N653brTzE+AfepjKsbMexYlBQ==", "type": "package", "path": "opentk/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "content/OpenTK.dll.config", "lib/net20/OpenTK.dll", "lib/net20/OpenTK.pdb", "lib/net20/OpenTK.xml", "opentk.3.1.0.nupkg.sha512", "opentk.nuspec"]}, "OpenTK.GLControl/3.1.0": {"sha512": "BjDzF+MhJd2LaZYzkXmgkzzXgHAko/Iur4rGy6+DPdZA1Jcc0X6jXZ6QsUsjlLRNzgevO7uAj2h1TqX7sguaYg==", "type": "package", "path": "opentk.glcontrol/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net20/OpenTK.GLControl.dll", "lib/net20/OpenTK.GLControl.pdb", "lib/net20/OpenTK.GLControl.xml", "opentk.glcontrol.3.1.0.nupkg.sha512", "opentk.glcontrol.nuspec"]}, "org.matheval/1.0.0.3": {"sha512": "DybbFCn/LoQpBCeqdUll4kwayJPrNyBSuZlufdWrMUye75rzVvsE+pjRp+be2Fleu1gYWYYXotHjUNqrfFOWCg==", "type": "package", "path": "org.matheval/1.0.0.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/org.matheval.dll", "lib/net40/org.matheval.dll", "lib/net45/org.matheval.dll", "lib/net46/org.matheval.dll", "lib/net461/org.matheval.dll", "lib/net462/org.matheval.dll", "lib/net47/org.matheval.dll", "lib/net471/org.matheval.dll", "lib/net472/org.matheval.dll", "lib/net48/org.matheval.dll", "lib/netcoreapp2.1/org.matheval.dll", "lib/netcoreapp3.1/org.matheval.dll", "lib/netstandard2.0/org.matheval.dll", "org.matheval.1.0.0.3.nupkg.sha512", "org.matheval.nuspec"]}, "OxyPlot.Core/2.2.0": {"sha512": "QhXNdXR5FPpro/VoLx3BOp6AhQo7YrbfmWEZ9cgY+pnYM7RYORZjnu+aDMA8ka9A1r8hLkX//NbCPZNUv+l8qA==", "type": "package", "path": "oxyplot.core/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.dll", "lib/net462/OxyPlot.xml", "lib/net6.0/OxyPlot.dll", "lib/net6.0/OxyPlot.xml", "lib/net8.0/OxyPlot.dll", "lib/net8.0/OxyPlot.xml", "lib/netstandard2.0/OxyPlot.dll", "lib/netstandard2.0/OxyPlot.xml", "oxyplot.core.2.2.0.nupkg.sha512", "oxyplot.core.nuspec"]}, "OxyPlot.Wpf/2.2.0": {"sha512": "69KzgsMWpJSZmeXZt07FwfV7B1D4CM1nl0MWMj4wjrCsb3USBMo32up1+fQIjxp5tHCPdGwX9VWhSxW8nsY7pQ==", "type": "package", "path": "oxyplot.wpf/2.2.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.Wpf.dll", "lib/net462/OxyPlot.Wpf.xml", "lib/net6.0-windows7.0/OxyPlot.Wpf.dll", "lib/net6.0-windows7.0/OxyPlot.Wpf.xml", "lib/net8.0-windows7.0/OxyPlot.Wpf.dll", "lib/net8.0-windows7.0/OxyPlot.Wpf.xml", "oxyplot.wpf.2.2.0.nupkg.sha512", "oxyplot.wpf.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "OxyPlot.Wpf.Shared/2.2.0": {"sha512": "JoOD/feTOlKFmgXqeTNrl4Ze0i0L6WizPapNw9pXwot+cnI0qhFgYv3tFjlSdu51hZw0EgmcgeRXMtm5bkueYA==", "type": "package", "path": "oxyplot.wpf.shared/2.2.0", "files": [".nupkg.metadata", ".signature.p7s", "OxyPlot_128.png", "README.md", "lib/net462/OxyPlot.Wpf.Shared.dll", "lib/net462/OxyPlot.Wpf.Shared.xml", "lib/net6.0-windows7.0/OxyPlot.Wpf.Shared.dll", "lib/net6.0-windows7.0/OxyPlot.Wpf.Shared.xml", "lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.dll", "lib/net8.0-windows7.0/OxyPlot.Wpf.Shared.xml", "oxyplot.wpf.shared.2.2.0.nupkg.sha512", "oxyplot.wpf.shared.nuspec"]}, "Sick.GenIStreamDotNet/4.2.0.20192": {"sha512": "8HafncOkr9nX6hD9BI9w4rsJiu3s17OcbD+lR57IPh9N03GIGC3+dbLWoUcaO1keskFcWyosvBbZVbsU4lR9jg==", "type": "package", "path": "sick.genistreamdotnet/4.2.0.20192", "files": [".nupkg.metadata", "build/CLAllSerial_MD_VC141_v3_4.dll", "build/CLProtocol_MD_VC141_v3_4.dll", "build/GCBase_MD_VC141_v3_4.dll", "build/GenApi_MD_VC141_v3_4.dll", "build/GenIStreamDotNetInterop.dll", "build/Log_MD_VC141_v3_4.dll", "build/MathParser_MD_VC141_v3_4.dll", "build/NodeMapData_MD_VC141_v3_4.dll", "build/Sick.GenIStreamDotNet.props", "build/XmlParser_MD_VC141_v3_4.dll", "build/clsercom.dll", "build/log4cpp_MD_VC141_v3_4.dll", "lib/net45/Sick.GenIStreamDotNet.dll", "lib/netstandard2.0/Sick.GenIStreamDotNet.dll", "sick.genistreamdotnet.4.2.0.20192.nupkg.sha512", "sick.genistreamdotnet.nuspec"]}, "Sick.Stream.Algorithms.DotNet/********": {"sha512": "lQ1Bz83t7bbLjfy3jo2D6s5So4CS34X870UoPdrTLIurK8tADnGTm8pUnpP/n5LWtz7ehT1pGhew75HLdJSiZw==", "type": "package", "path": "sick.stream.algorithms.dotnet/********", "files": [".nupkg.metadata", "build/net472/Sick.Stream.Algorithms.DotNet.props", "build/net472/Sick.Stream.Algorithms.Interop.dll", "build/net472/Sick.Stream.Algorithms.dll", "lib/netstandard20/Sick.Stream.Algorithms.DotNet.dll", "sick.stream.algorithms.dotnet.********.nupkg.sha512", "sick.stream.algorithms.dotnet.nuspec"]}, "Sick.Stream.Common/********": {"sha512": "KCRDn/Fxp57HouNAPPhZQsCCd6VAWD/ZbiGTy2IACw8KD2BAdqnr0vhER0yuqzx/HmB4457+GbpCpwo40YF0nQ==", "type": "package", "path": "sick.stream.common/********", "files": [".nupkg.metadata", "lib/netstandard2.0/Sick.Stream.Common.dll", "lib/netstandard2.0/Sick.Stream.Common.xml", "lib/netstandard2.0/zh-CN/Sick.Stream.Common.resources.dll", "sick.stream.common.********.nupkg.sha512", "sick.stream.common.nuspec"]}, "Sick.Stream.Controls/********": {"sha512": "8vA7sStLBEeYZJGJlMH+TzeM0ky/1JVLQCtJMm2+DAbb7eYzWQj0dp2nmOmbQlKfbDBD2BuNrtmNn+eSqf+n8g==", "type": "package", "path": "sick.stream.controls/********", "files": [".nupkg.metadata", "lib/net472/Sick.Stream.Controls.dll", "lib/net472/Sick.Stream.Controls.xml", "lib/net472/zh-CN/Sick.Stream.Controls.resources.dll", "sick.stream.controls.********.nupkg.sha512", "sick.stream.controls.nuspec"]}, "Sick.Stream.Processing/********": {"sha512": "QfdF5r8RcXiY8VtADffWlGzSbWckpoXm547ATI0pRcEP4Fx+DFlYb3/cmD65ZVb2C+UEPCVRnRmBsZaP4wKVvA==", "type": "package", "path": "sick.stream.processing/********", "files": [".nupkg.metadata", "lib/netstandard2.0/Sick.Stream.Processing.dll", "lib/netstandard2.0/Sick.Stream.Processing.xml", "lib/netstandard2.0/zh-CN/Sick.Stream.Processing.resources.dll", "sick.stream.processing.********.nupkg.sha512", "sick.stream.processing.nuspec"]}, "Sick.Stream.Processing.Tools/********": {"sha512": "2wiCPP1uEMTpMbJMq11I1asak/1l1UDE7/+cHGR658ONt2/jnRjZiBdiY7HSEquPdAzY0MXXOacz/ZuWmVWYCw==", "type": "package", "path": "sick.stream.processing.tools/********", "files": [".nupkg.metadata", "lib/netstandard2.0/Sick.Stream.Processing.Tools.dll", "lib/netstandard2.0/Sick.Stream.Processing.Tools.xml", "lib/netstandard2.0/zh-CN/Sick.Stream.Processing.Tools.resources.dll", "sick.stream.processing.tools.********.nupkg.sha512", "sick.stream.processing.tools.nuspec"]}, "Splat/15.3.1": {"sha512": "zyu98Mwp8gTi3MlS6EGtvcacmw5W/qA4/h/DVeZ28A1dR6mcM628ujHck6LK5SM6LwroR5LzBonWgeFtwo2Afg==", "type": "package", "path": "splat/15.3.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE/LICENSE", "README.md", "lib/net8.0/Splat.dll", "lib/net8.0/Splat.xml", "lib/net9.0/Splat.dll", "lib/net9.0/Splat.xml", "lib/netstandard2.0/Splat.dll", "lib/netstandard2.0/Splat.xml", "logo.png", "splat.15.3.1.nupkg.sha512", "splat.nuspec"]}, "SuitDotNet/1.3.190": {"sha512": "Qli5rh76u3nCqRkEilTlsjdjzR6mIWYFPgMNiA/6zNxN+Fu0h9pLhCj163K72Jxan0gkeeVvGG/7DxuhMx5Ibw==", "type": "package", "path": "suitdotnet/1.3.190", "files": [".nupkg.metadata", "lib/net472/SuitDotNet.dll", "lib/net472/SuitDotNet.pdb", "lib/net472/suit-net.dll", "licenses/jdk/ASSEMBLY_EXCEPTION", "licenses/jdk/LICENSE", "licenses/jdk/THIRD_PARTY_README", "suitdotnet.1.3.190.nupkg.sha512", "suitdotnet.nuspec"]}, "System.AppContext/4.3.0": {"sha512": "fKC+rmaLfeIzUhagxY17Q9siv/sPrjjKcfNg1Ic8IlQkZLipo8ljcaZQu4VtI4Jqbzjc2VTjzGLF6WmsRXAEgA==", "type": "package", "path": "system.appcontext/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.AppContext.dll", "lib/net463/System.AppContext.dll", "lib/netcore50/System.AppContext.dll", "lib/netstandard1.6/System.AppContext.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.AppContext.dll", "ref/net463/System.AppContext.dll", "ref/netstandard/_._", "ref/netstandard1.3/System.AppContext.dll", "ref/netstandard1.3/System.AppContext.xml", "ref/netstandard1.3/de/System.AppContext.xml", "ref/netstandard1.3/es/System.AppContext.xml", "ref/netstandard1.3/fr/System.AppContext.xml", "ref/netstandard1.3/it/System.AppContext.xml", "ref/netstandard1.3/ja/System.AppContext.xml", "ref/netstandard1.3/ko/System.AppContext.xml", "ref/netstandard1.3/ru/System.AppContext.xml", "ref/netstandard1.3/zh-hans/System.AppContext.xml", "ref/netstandard1.3/zh-hant/System.AppContext.xml", "ref/netstandard1.6/System.AppContext.dll", "ref/netstandard1.6/System.AppContext.xml", "ref/netstandard1.6/de/System.AppContext.xml", "ref/netstandard1.6/es/System.AppContext.xml", "ref/netstandard1.6/fr/System.AppContext.xml", "ref/netstandard1.6/it/System.AppContext.xml", "ref/netstandard1.6/ja/System.AppContext.xml", "ref/netstandard1.6/ko/System.AppContext.xml", "ref/netstandard1.6/ru/System.AppContext.xml", "ref/netstandard1.6/zh-hans/System.AppContext.xml", "ref/netstandard1.6/zh-hant/System.AppContext.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.AppContext.dll", "system.appcontext.4.3.0.nupkg.sha512", "system.appcontext.nuspec"]}, "System.Buffers/4.6.1": {"sha512": "N8GXpmiLMtljq7gwvyS+1QvKT/W2J8sNAvx+HVg4NGmsG/H+2k/y9QI23auLJRterrzCiDH+IWAw4V/GPwsMlw==", "type": "package", "path": "system.buffers/4.6.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Buffers.targets", "buildTransitive/net462/_._", "lib/net462/System.Buffers.dll", "lib/net462/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/netstandard2.1/_._", "system.buffers.4.6.1.nupkg.sha512", "system.buffers.nuspec"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Concurrent/4.3.0": {"sha512": "ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "type": "package", "path": "system.collections.concurrent/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.Concurrent.dll", "lib/netstandard1.3/System.Collections.Concurrent.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.Concurrent.dll", "ref/netcore50/System.Collections.Concurrent.xml", "ref/netcore50/de/System.Collections.Concurrent.xml", "ref/netcore50/es/System.Collections.Concurrent.xml", "ref/netcore50/fr/System.Collections.Concurrent.xml", "ref/netcore50/it/System.Collections.Concurrent.xml", "ref/netcore50/ja/System.Collections.Concurrent.xml", "ref/netcore50/ko/System.Collections.Concurrent.xml", "ref/netcore50/ru/System.Collections.Concurrent.xml", "ref/netcore50/zh-hans/System.Collections.Concurrent.xml", "ref/netcore50/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.1/System.Collections.Concurrent.dll", "ref/netstandard1.1/System.Collections.Concurrent.xml", "ref/netstandard1.1/de/System.Collections.Concurrent.xml", "ref/netstandard1.1/es/System.Collections.Concurrent.xml", "ref/netstandard1.1/fr/System.Collections.Concurrent.xml", "ref/netstandard1.1/it/System.Collections.Concurrent.xml", "ref/netstandard1.1/ja/System.Collections.Concurrent.xml", "ref/netstandard1.1/ko/System.Collections.Concurrent.xml", "ref/netstandard1.1/ru/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.1/zh-hant/System.Collections.Concurrent.xml", "ref/netstandard1.3/System.Collections.Concurrent.dll", "ref/netstandard1.3/System.Collections.Concurrent.xml", "ref/netstandard1.3/de/System.Collections.Concurrent.xml", "ref/netstandard1.3/es/System.Collections.Concurrent.xml", "ref/netstandard1.3/fr/System.Collections.Concurrent.xml", "ref/netstandard1.3/it/System.Collections.Concurrent.xml", "ref/netstandard1.3/ja/System.Collections.Concurrent.xml", "ref/netstandard1.3/ko/System.Collections.Concurrent.xml", "ref/netstandard1.3/ru/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hans/System.Collections.Concurrent.xml", "ref/netstandard1.3/zh-hant/System.Collections.Concurrent.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.concurrent.4.3.0.nupkg.sha512", "system.collections.concurrent.nuspec"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Console/4.3.0": {"sha512": "DHDrIxiqk1h03m6khKWV2X8p/uvN79rgSqpilL6uzpmSfxfU5ng8VcPtW4qsDsQDHiTv6IPV9TmD5M/vElPNLg==", "type": "package", "path": "system.console/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Console.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Console.dll", "ref/netstandard1.3/System.Console.dll", "ref/netstandard1.3/System.Console.xml", "ref/netstandard1.3/de/System.Console.xml", "ref/netstandard1.3/es/System.Console.xml", "ref/netstandard1.3/fr/System.Console.xml", "ref/netstandard1.3/it/System.Console.xml", "ref/netstandard1.3/ja/System.Console.xml", "ref/netstandard1.3/ko/System.Console.xml", "ref/netstandard1.3/ru/System.Console.xml", "ref/netstandard1.3/zh-hans/System.Console.xml", "ref/netstandard1.3/zh-hant/System.Console.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.console.4.3.0.nupkg.sha512", "system.console.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.Tools/4.3.0": {"sha512": "UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "type": "package", "path": "system.diagnostics.tools/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Tools.dll", "ref/netcore50/System.Diagnostics.Tools.xml", "ref/netcore50/de/System.Diagnostics.Tools.xml", "ref/netcore50/es/System.Diagnostics.Tools.xml", "ref/netcore50/fr/System.Diagnostics.Tools.xml", "ref/netcore50/it/System.Diagnostics.Tools.xml", "ref/netcore50/ja/System.Diagnostics.Tools.xml", "ref/netcore50/ko/System.Diagnostics.Tools.xml", "ref/netcore50/ru/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tools.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tools.xml", "ref/netstandard1.0/System.Diagnostics.Tools.dll", "ref/netstandard1.0/System.Diagnostics.Tools.xml", "ref/netstandard1.0/de/System.Diagnostics.Tools.xml", "ref/netstandard1.0/es/System.Diagnostics.Tools.xml", "ref/netstandard1.0/fr/System.Diagnostics.Tools.xml", "ref/netstandard1.0/it/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ja/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ko/System.Diagnostics.Tools.xml", "ref/netstandard1.0/ru/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Tools.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Tools.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tools.4.3.0.nupkg.sha512", "system.diagnostics.tools.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.Globalization.Calendars/4.3.0": {"sha512": "GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "type": "package", "path": "system.globalization.calendars/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Globalization.Calendars.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.dll", "ref/netstandard1.3/System.Globalization.Calendars.xml", "ref/netstandard1.3/de/System.Globalization.Calendars.xml", "ref/netstandard1.3/es/System.Globalization.Calendars.xml", "ref/netstandard1.3/fr/System.Globalization.Calendars.xml", "ref/netstandard1.3/it/System.Globalization.Calendars.xml", "ref/netstandard1.3/ja/System.Globalization.Calendars.xml", "ref/netstandard1.3/ko/System.Globalization.Calendars.xml", "ref/netstandard1.3/ru/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hans/System.Globalization.Calendars.xml", "ref/netstandard1.3/zh-hant/System.Globalization.Calendars.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.calendars.4.3.0.nupkg.sha512", "system.globalization.calendars.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.Compression.ZipFile/4.3.0": {"sha512": "G4HwjEsgIwy3JFBduZ9quBkAu+eUwjIdJleuNSgmUojbH6O3mlvEIme+GHx/cLlTAPcrnnL7GqvB9pTlWRfhOg==", "type": "package", "path": "system.io.compression.zipfile/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.Compression.ZipFile.dll", "lib/netstandard1.3/System.IO.Compression.ZipFile.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.dll", "ref/netstandard1.3/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/de/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/es/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/fr/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/it/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ja/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ko/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/ru/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.ZipFile.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.ZipFile.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.compression.zipfile.4.3.0.nupkg.sha512", "system.io.compression.zipfile.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.IO.Pipelines/9.0.3": {"sha512": "aP1Qh9llcEmo0qN+VKvVDHFMe5Cqpfb1VjhBO7rjmxCXtLs3IfVSOiNqqLBZ/4Qbcr4J0SDdJq9S7EKAGpnwEA==", "type": "package", "path": "system.io.pipelines/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/net9.0/System.IO.Pipelines.dll", "lib/net9.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.9.0.3.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Linq.Expressions/4.3.0": {"sha512": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "type": "package", "path": "system.linq.expressions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.Expressions.dll", "lib/netcore50/System.Linq.Expressions.dll", "lib/netstandard1.6/System.Linq.Expressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.dll", "ref/netcore50/System.Linq.Expressions.xml", "ref/netcore50/de/System.Linq.Expressions.xml", "ref/netcore50/es/System.Linq.Expressions.xml", "ref/netcore50/fr/System.Linq.Expressions.xml", "ref/netcore50/it/System.Linq.Expressions.xml", "ref/netcore50/ja/System.Linq.Expressions.xml", "ref/netcore50/ko/System.Linq.Expressions.xml", "ref/netcore50/ru/System.Linq.Expressions.xml", "ref/netcore50/zh-hans/System.Linq.Expressions.xml", "ref/netcore50/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.0/System.Linq.Expressions.dll", "ref/netstandard1.0/System.Linq.Expressions.xml", "ref/netstandard1.0/de/System.Linq.Expressions.xml", "ref/netstandard1.0/es/System.Linq.Expressions.xml", "ref/netstandard1.0/fr/System.Linq.Expressions.xml", "ref/netstandard1.0/it/System.Linq.Expressions.xml", "ref/netstandard1.0/ja/System.Linq.Expressions.xml", "ref/netstandard1.0/ko/System.Linq.Expressions.xml", "ref/netstandard1.0/ru/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.0/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.3/System.Linq.Expressions.dll", "ref/netstandard1.3/System.Linq.Expressions.xml", "ref/netstandard1.3/de/System.Linq.Expressions.xml", "ref/netstandard1.3/es/System.Linq.Expressions.xml", "ref/netstandard1.3/fr/System.Linq.Expressions.xml", "ref/netstandard1.3/it/System.Linq.Expressions.xml", "ref/netstandard1.3/ja/System.Linq.Expressions.xml", "ref/netstandard1.3/ko/System.Linq.Expressions.xml", "ref/netstandard1.3/ru/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.3/zh-hant/System.Linq.Expressions.xml", "ref/netstandard1.6/System.Linq.Expressions.dll", "ref/netstandard1.6/System.Linq.Expressions.xml", "ref/netstandard1.6/de/System.Linq.Expressions.xml", "ref/netstandard1.6/es/System.Linq.Expressions.xml", "ref/netstandard1.6/fr/System.Linq.Expressions.xml", "ref/netstandard1.6/it/System.Linq.Expressions.xml", "ref/netstandard1.6/ja/System.Linq.Expressions.xml", "ref/netstandard1.6/ko/System.Linq.Expressions.xml", "ref/netstandard1.6/ru/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hans/System.Linq.Expressions.xml", "ref/netstandard1.6/zh-hant/System.Linq.Expressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Linq.Expressions.dll", "system.linq.expressions.4.3.0.nupkg.sha512", "system.linq.expressions.nuspec"]}, "System.Memory/4.6.2": {"sha512": "Eq99oXEjWicekXb8AaqwYA6p+am0XrUMenq5RB6OYIL7oYY5gUzRas9+i2zcNsRNB75MpDhFsYX+uV6y89a0Eg==", "type": "package", "path": "system.memory/4.6.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Memory.targets", "buildTransitive/net462/_._", "lib/net462/System.Memory.dll", "lib/net462/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "lib/netstandard2.1/_._", "system.memory.4.6.2.nupkg.sha512", "system.memory.nuspec"]}, "System.Net.Http/4.3.4": {"sha512": "aOa2d51SEbmM+H+Csw7yJOuNZoHkrP2XnAurye5HWYgGVVU54YZDvsLUYRv6h18X3sPnjNCANmN7ZhIPiqMcjA==", "type": "package", "path": "system.net.http/4.3.4", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/Xamarinmac20/_._", "lib/monoandroid10/_._", "lib/monotouch10/_._", "lib/net45/_._", "lib/net46/System.Net.Http.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/Xamarinmac20/_._", "ref/monoandroid10/_._", "ref/monotouch10/_._", "ref/net45/_._", "ref/net46/System.Net.Http.dll", "ref/netcore50/System.Net.Http.dll", "ref/netstandard1.1/System.Net.Http.dll", "ref/netstandard1.3/System.Net.Http.dll", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Net.Http.dll", "runtimes/win/lib/net46/System.Net.Http.dll", "runtimes/win/lib/netcore50/System.Net.Http.dll", "runtimes/win/lib/netstandard1.3/System.Net.Http.dll", "system.net.http.4.3.4.nupkg.sha512", "system.net.http.nuspec"]}, "System.Net.Primitives/4.3.0": {"sha512": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "type": "package", "path": "system.net.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.3.0.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.Sockets/4.3.0": {"sha512": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "type": "package", "path": "system.net.sockets/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.sockets.4.3.0.nupkg.sha512", "system.net.sockets.nuspec"]}, "System.Numerics.Vectors/4.6.1": {"sha512": "sQxefTnhagrhoq2ReR0D/6K0zJcr9Hrd6kikeXsA1I8kOCboTavcUC4r7TSfpKFeE163uMuxZcyfO1mGO3EN8Q==", "type": "package", "path": "system.numerics.vectors/4.6.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Numerics.Vectors.targets", "buildTransitive/net462/_._", "lib/net462/System.Numerics.Vectors.dll", "lib/net462/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/netstandard2.1/_._", "system.numerics.vectors.4.6.1.nupkg.sha512", "system.numerics.vectors.nuspec"]}, "System.ObjectModel/4.3.0": {"sha512": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "type": "package", "path": "system.objectmodel/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.ObjectModel.dll", "lib/netstandard1.3/System.ObjectModel.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.ObjectModel.dll", "ref/netcore50/System.ObjectModel.xml", "ref/netcore50/de/System.ObjectModel.xml", "ref/netcore50/es/System.ObjectModel.xml", "ref/netcore50/fr/System.ObjectModel.xml", "ref/netcore50/it/System.ObjectModel.xml", "ref/netcore50/ja/System.ObjectModel.xml", "ref/netcore50/ko/System.ObjectModel.xml", "ref/netcore50/ru/System.ObjectModel.xml", "ref/netcore50/zh-hans/System.ObjectModel.xml", "ref/netcore50/zh-hant/System.ObjectModel.xml", "ref/netstandard1.0/System.ObjectModel.dll", "ref/netstandard1.0/System.ObjectModel.xml", "ref/netstandard1.0/de/System.ObjectModel.xml", "ref/netstandard1.0/es/System.ObjectModel.xml", "ref/netstandard1.0/fr/System.ObjectModel.xml", "ref/netstandard1.0/it/System.ObjectModel.xml", "ref/netstandard1.0/ja/System.ObjectModel.xml", "ref/netstandard1.0/ko/System.ObjectModel.xml", "ref/netstandard1.0/ru/System.ObjectModel.xml", "ref/netstandard1.0/zh-hans/System.ObjectModel.xml", "ref/netstandard1.0/zh-hant/System.ObjectModel.xml", "ref/netstandard1.3/System.ObjectModel.dll", "ref/netstandard1.3/System.ObjectModel.xml", "ref/netstandard1.3/de/System.ObjectModel.xml", "ref/netstandard1.3/es/System.ObjectModel.xml", "ref/netstandard1.3/fr/System.ObjectModel.xml", "ref/netstandard1.3/it/System.ObjectModel.xml", "ref/netstandard1.3/ja/System.ObjectModel.xml", "ref/netstandard1.3/ko/System.ObjectModel.xml", "ref/netstandard1.3/ru/System.ObjectModel.xml", "ref/netstandard1.3/zh-hans/System.ObjectModel.xml", "ref/netstandard1.3/zh-hant/System.ObjectModel.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.objectmodel.4.3.0.nupkg.sha512", "system.objectmodel.nuspec"]}, "System.Reactive/6.0.1": {"sha512": "rHaWtKDwCi9qJ3ObKo8LHPMuuwv33YbmQi7TcUK1C264V3MFnOr5Im7QgCTdLniztP3GJyeiSg5x8NqYJFqRmg==", "type": "package", "path": "system.reactive/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "build/net6.0-windows10.0.19041/_._", "build/net6.0/_._", "buildTransitive/net6.0-windows10.0.19041/_._", "buildTransitive/net6.0/_._", "icon.png", "lib/net472/System.Reactive.dll", "lib/net472/System.Reactive.xml", "lib/net6.0-windows10.0.19041/System.Reactive.dll", "lib/net6.0-windows10.0.19041/System.Reactive.xml", "lib/net6.0/System.Reactive.dll", "lib/net6.0/System.Reactive.xml", "lib/netstandard2.0/System.Reactive.dll", "lib/netstandard2.0/System.Reactive.xml", "lib/uap10.0.18362/System.Reactive.dll", "lib/uap10.0.18362/System.Reactive.pri", "lib/uap10.0.18362/System.Reactive.xml", "readme.md", "system.reactive.6.0.1.nupkg.sha512", "system.reactive.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Extensions/4.3.0": {"sha512": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "type": "package", "path": "system.reflection.extensions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Extensions.dll", "ref/netcore50/System.Reflection.Extensions.xml", "ref/netcore50/de/System.Reflection.Extensions.xml", "ref/netcore50/es/System.Reflection.Extensions.xml", "ref/netcore50/fr/System.Reflection.Extensions.xml", "ref/netcore50/it/System.Reflection.Extensions.xml", "ref/netcore50/ja/System.Reflection.Extensions.xml", "ref/netcore50/ko/System.Reflection.Extensions.xml", "ref/netcore50/ru/System.Reflection.Extensions.xml", "ref/netcore50/zh-hans/System.Reflection.Extensions.xml", "ref/netcore50/zh-hant/System.Reflection.Extensions.xml", "ref/netstandard1.0/System.Reflection.Extensions.dll", "ref/netstandard1.0/System.Reflection.Extensions.xml", "ref/netstandard1.0/de/System.Reflection.Extensions.xml", "ref/netstandard1.0/es/System.Reflection.Extensions.xml", "ref/netstandard1.0/fr/System.Reflection.Extensions.xml", "ref/netstandard1.0/it/System.Reflection.Extensions.xml", "ref/netstandard1.0/ja/System.Reflection.Extensions.xml", "ref/netstandard1.0/ko/System.Reflection.Extensions.xml", "ref/netstandard1.0/ru/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.extensions.4.3.0.nupkg.sha512", "system.reflection.extensions.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.CompilerServices.Unsafe/6.1.1": {"sha512": "YkOfl8PsmWT4ASkkEFFlfajgwomK8VnhwOIx0JEego69Tw5IqXjbzUBwNKcE5KprqlK92ZCYT56nQwmyEv45Ug==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.1.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "lib/net462/System.Runtime.CompilerServices.Unsafe.dll", "lib/net462/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/net7.0/_._", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.1.1.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"sha512": "cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "type": "package", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/win8/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/wpa81/System.Runtime.InteropServices.RuntimeInformation.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/unix/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/net45/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netcore50/System.Runtime.InteropServices.RuntimeInformation.dll", "runtimes/win/lib/netstandard1.1/System.Runtime.InteropServices.RuntimeInformation.dll", "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512", "system.runtime.interopservices.runtimeinformation.nuspec"]}, "System.Runtime.Numerics/4.3.0": {"sha512": "yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "type": "package", "path": "system.runtime.numerics/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.Numerics.dll", "lib/netstandard1.3/System.Runtime.Numerics.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Runtime.Numerics.dll", "ref/netcore50/System.Runtime.Numerics.xml", "ref/netcore50/de/System.Runtime.Numerics.xml", "ref/netcore50/es/System.Runtime.Numerics.xml", "ref/netcore50/fr/System.Runtime.Numerics.xml", "ref/netcore50/it/System.Runtime.Numerics.xml", "ref/netcore50/ja/System.Runtime.Numerics.xml", "ref/netcore50/ko/System.Runtime.Numerics.xml", "ref/netcore50/ru/System.Runtime.Numerics.xml", "ref/netcore50/zh-hans/System.Runtime.Numerics.xml", "ref/netcore50/zh-hant/System.Runtime.Numerics.xml", "ref/netstandard1.1/System.Runtime.Numerics.dll", "ref/netstandard1.1/System.Runtime.Numerics.xml", "ref/netstandard1.1/de/System.Runtime.Numerics.xml", "ref/netstandard1.1/es/System.Runtime.Numerics.xml", "ref/netstandard1.1/fr/System.Runtime.Numerics.xml", "ref/netstandard1.1/it/System.Runtime.Numerics.xml", "ref/netstandard1.1/ja/System.Runtime.Numerics.xml", "ref/netstandard1.1/ko/System.Runtime.Numerics.xml", "ref/netstandard1.1/ru/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hans/System.Runtime.Numerics.xml", "ref/netstandard1.1/zh-hant/System.Runtime.Numerics.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.numerics.4.3.0.nupkg.sha512", "system.runtime.numerics.nuspec"]}, "System.Security.Cryptography.Algorithms/4.3.0": {"sha512": "W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "type": "package", "path": "system.security.cryptography.algorithms/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Algorithms.dll", "lib/net461/System.Security.Cryptography.Algorithms.dll", "lib/net463/System.Security.Cryptography.Algorithms.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Algorithms.dll", "ref/net461/System.Security.Cryptography.Algorithms.dll", "ref/net463/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.3/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.4/System.Security.Cryptography.Algorithms.dll", "ref/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/osx/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net461/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/net463/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.Algorithms.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.Algorithms.dll", "system.security.cryptography.algorithms.4.3.0.nupkg.sha512", "system.security.cryptography.algorithms.nuspec"]}, "System.Security.Cryptography.Encoding/4.3.0": {"sha512": "1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "type": "package", "path": "system.security.cryptography.encoding/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Encoding.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.dll", "ref/netstandard1.3/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/de/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/es/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/it/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.Encoding.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/net46/System.Security.Cryptography.Encoding.dll", "runtimes/win/lib/netstandard1.3/System.Security.Cryptography.Encoding.dll", "system.security.cryptography.encoding.4.3.0.nupkg.sha512", "system.security.cryptography.encoding.nuspec"]}, "System.Security.Cryptography.Primitives/4.3.0": {"sha512": "7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "type": "package", "path": "system.security.cryptography.primitives/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.Primitives.dll", "lib/netstandard1.3/System.Security.Cryptography.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.Primitives.dll", "ref/netstandard1.3/System.Security.Cryptography.Primitives.dll", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.security.cryptography.primitives.4.3.0.nupkg.sha512", "system.security.cryptography.primitives.nuspec"]}, "System.Security.Cryptography.X509Certificates/4.3.0": {"sha512": "t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "type": "package", "path": "system.security.cryptography.x509certificates/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Security.Cryptography.X509Certificates.dll", "lib/net461/System.Security.Cryptography.X509Certificates.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Security.Cryptography.X509Certificates.dll", "ref/net461/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.3/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.3/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.dll", "ref/netstandard1.4/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/de/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/es/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/fr/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/it/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ja/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ko/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/ru/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hans/System.Security.Cryptography.X509Certificates.xml", "ref/netstandard1.4/zh-hant/System.Security.Cryptography.X509Certificates.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net46/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/net461/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netcore50/System.Security.Cryptography.X509Certificates.dll", "runtimes/win/lib/netstandard1.6/System.Security.Cryptography.X509Certificates.dll", "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512", "system.security.cryptography.x509certificates.nuspec"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/9.0.3": {"sha512": "5L+iI4fBMtGwt4FHLQh40/rgdbxnw6lHaLkR3gbaHG97TohzUv+z/oP03drsTR1lKCLhOkp40cFnHYOQLtpT5A==", "type": "package", "path": "system.text.encodings.web/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/net9.0/System.Text.Encodings.Web.dll", "lib/net9.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net9.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.9.0.3.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/9.0.3": {"sha512": "r2JRkLjsYrq5Dpo7+y3Wa73OfirZPdVhxiTJWwZ+oJM7FOAe0LkM3GlH+pgkNRdd1G1kwUbmRCdmh4uoaWwu1g==", "type": "package", "path": "system.text.json/9.0.3", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net8.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/net9.0/System.Text.Json.dll", "lib/net9.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.9.0.3.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.RegularExpressions/4.3.1": {"sha512": "N0kNRrWe4+nXOWlpLT4LAY5brb8caNFlUuIRpraCVMDLYutKkol1aV079rQjLuSxKMJT2SpBQsYX9xbcTMmzwg==", "type": "package", "path": "system.text.regularexpressions/4.3.1", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Text.RegularExpressions.dll", "lib/netcore50/System.Text.RegularExpressions.dll", "lib/netstandard1.6/System.Text.RegularExpressions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.dll", "ref/netcore50/System.Text.RegularExpressions.xml", "ref/netcore50/de/System.Text.RegularExpressions.xml", "ref/netcore50/es/System.Text.RegularExpressions.xml", "ref/netcore50/fr/System.Text.RegularExpressions.xml", "ref/netcore50/it/System.Text.RegularExpressions.xml", "ref/netcore50/ja/System.Text.RegularExpressions.xml", "ref/netcore50/ko/System.Text.RegularExpressions.xml", "ref/netcore50/ru/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hans/System.Text.RegularExpressions.xml", "ref/netcore50/zh-hant/System.Text.RegularExpressions.xml", "ref/netcoreapp1.1/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.dll", "ref/netstandard1.0/System.Text.RegularExpressions.xml", "ref/netstandard1.0/de/System.Text.RegularExpressions.xml", "ref/netstandard1.0/es/System.Text.RegularExpressions.xml", "ref/netstandard1.0/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.0/it/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.0/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.0/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.3/System.Text.RegularExpressions.dll", "ref/netstandard1.3/System.Text.RegularExpressions.xml", "ref/netstandard1.3/de/System.Text.RegularExpressions.xml", "ref/netstandard1.3/es/System.Text.RegularExpressions.xml", "ref/netstandard1.3/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.3/it/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.3/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.3/zh-hant/System.Text.RegularExpressions.xml", "ref/netstandard1.6/System.Text.RegularExpressions.dll", "ref/netstandard1.6/System.Text.RegularExpressions.xml", "ref/netstandard1.6/de/System.Text.RegularExpressions.xml", "ref/netstandard1.6/es/System.Text.RegularExpressions.xml", "ref/netstandard1.6/fr/System.Text.RegularExpressions.xml", "ref/netstandard1.6/it/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ja/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ko/System.Text.RegularExpressions.xml", "ref/netstandard1.6/ru/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hans/System.Text.RegularExpressions.xml", "ref/netstandard1.6/zh-hant/System.Text.RegularExpressions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.regularexpressions.4.3.1.nupkg.sha512", "system.text.regularexpressions.nuspec"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.6.2": {"sha512": "4ESZued1sBKrSmfT3XyvyFwULsBz0tv5nYfMt5SUCw6i87nuyKYX1dw4SmjYJY9i72cc2pCI059ayCa6Bn2ajg==", "type": "package", "path": "system.threading.tasks.extensions/4.6.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "PACKAGE.md", "buildTransitive/net461/System.Threading.Tasks.Extensions.targets", "buildTransitive/net462/_._", "lib/net462/System.Threading.Tasks.Extensions.dll", "lib/net462/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.1/_._", "system.threading.tasks.extensions.4.6.2.nupkg.sha512", "system.threading.tasks.extensions.nuspec"]}, "System.Threading.Timer/4.3.0": {"sha512": "Z6YfyYTCg7lOZjJzBjONJTFKGN9/NIYKSxhU5GRd+DTwHSZyvWp1xuI5aR+dLg+ayyC5Xv57KiY4oJ0tMO89fQ==", "type": "package", "path": "system.threading.timer/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net451/_._", "lib/portable-net451+win81+wpa81/_._", "lib/win81/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net451/_._", "ref/netcore50/System.Threading.Timer.dll", "ref/netcore50/System.Threading.Timer.xml", "ref/netcore50/de/System.Threading.Timer.xml", "ref/netcore50/es/System.Threading.Timer.xml", "ref/netcore50/fr/System.Threading.Timer.xml", "ref/netcore50/it/System.Threading.Timer.xml", "ref/netcore50/ja/System.Threading.Timer.xml", "ref/netcore50/ko/System.Threading.Timer.xml", "ref/netcore50/ru/System.Threading.Timer.xml", "ref/netcore50/zh-hans/System.Threading.Timer.xml", "ref/netcore50/zh-hant/System.Threading.Timer.xml", "ref/netstandard1.2/System.Threading.Timer.dll", "ref/netstandard1.2/System.Threading.Timer.xml", "ref/netstandard1.2/de/System.Threading.Timer.xml", "ref/netstandard1.2/es/System.Threading.Timer.xml", "ref/netstandard1.2/fr/System.Threading.Timer.xml", "ref/netstandard1.2/it/System.Threading.Timer.xml", "ref/netstandard1.2/ja/System.Threading.Timer.xml", "ref/netstandard1.2/ko/System.Threading.Timer.xml", "ref/netstandard1.2/ru/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hans/System.Threading.Timer.xml", "ref/netstandard1.2/zh-hant/System.Threading.Timer.xml", "ref/portable-net451+win81+wpa81/_._", "ref/win81/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.timer.4.3.0.nupkg.sha512", "system.threading.timer.nuspec"]}, "System.ValueTuple/4.5.0": {"sha512": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "type": "package", "path": "system.valuetuple/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.ValueTuple.dll", "lib/net461/System.ValueTuple.xml", "lib/net47/System.ValueTuple.dll", "lib/net47/System.ValueTuple.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.ValueTuple.dll", "lib/netstandard1.0/System.ValueTuple.xml", "lib/netstandard2.0/_._", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "lib/portable-net40+sl4+win8+wp8/System.ValueTuple.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net461/System.ValueTuple.dll", "ref/net47/System.ValueTuple.dll", "ref/netcoreapp2.0/_._", "ref/netstandard2.0/_._", "ref/portable-net40+sl4+win8+wp8/System.ValueTuple.dll", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.valuetuple.4.5.0.nupkg.sha512", "system.valuetuple.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Xml.ReaderWriter/4.3.0": {"sha512": "GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "type": "package", "path": "system.xml.readerwriter/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Xml.ReaderWriter.dll", "lib/netcore50/System.Xml.ReaderWriter.dll", "lib/netstandard1.3/System.Xml.ReaderWriter.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.dll", "ref/netcore50/System.Xml.ReaderWriter.xml", "ref/netcore50/de/System.Xml.ReaderWriter.xml", "ref/netcore50/es/System.Xml.ReaderWriter.xml", "ref/netcore50/fr/System.Xml.ReaderWriter.xml", "ref/netcore50/it/System.Xml.ReaderWriter.xml", "ref/netcore50/ja/System.Xml.ReaderWriter.xml", "ref/netcore50/ko/System.Xml.ReaderWriter.xml", "ref/netcore50/ru/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hans/System.Xml.ReaderWriter.xml", "ref/netcore50/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/System.Xml.ReaderWriter.dll", "ref/netstandard1.0/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.0/zh-hant/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/System.Xml.ReaderWriter.dll", "ref/netstandard1.3/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/de/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/es/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/fr/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/it/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ja/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ko/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/ru/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hans/System.Xml.ReaderWriter.xml", "ref/netstandard1.3/zh-hant/System.Xml.ReaderWriter.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.readerwriter.4.3.0.nupkg.sha512", "system.xml.readerwriter.nuspec"]}, "System.Xml.XDocument/4.3.0": {"sha512": "5zJ0XDxAIg8iy+t4aMnQAu0MqVbqyvfoUVl1yDV61xdo3Vth45oA2FoY4pPkxYAH5f8ixpmTqXeEIya95x0aCQ==", "type": "package", "path": "system.xml.xdocument/4.3.0", "files": [".nupkg.metadata", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Xml.XDocument.dll", "lib/netstandard1.3/System.Xml.XDocument.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Xml.XDocument.dll", "ref/netcore50/System.Xml.XDocument.xml", "ref/netcore50/de/System.Xml.XDocument.xml", "ref/netcore50/es/System.Xml.XDocument.xml", "ref/netcore50/fr/System.Xml.XDocument.xml", "ref/netcore50/it/System.Xml.XDocument.xml", "ref/netcore50/ja/System.Xml.XDocument.xml", "ref/netcore50/ko/System.Xml.XDocument.xml", "ref/netcore50/ru/System.Xml.XDocument.xml", "ref/netcore50/zh-hans/System.Xml.XDocument.xml", "ref/netcore50/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.0/System.Xml.XDocument.dll", "ref/netstandard1.0/System.Xml.XDocument.xml", "ref/netstandard1.0/de/System.Xml.XDocument.xml", "ref/netstandard1.0/es/System.Xml.XDocument.xml", "ref/netstandard1.0/fr/System.Xml.XDocument.xml", "ref/netstandard1.0/it/System.Xml.XDocument.xml", "ref/netstandard1.0/ja/System.Xml.XDocument.xml", "ref/netstandard1.0/ko/System.Xml.XDocument.xml", "ref/netstandard1.0/ru/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.0/zh-hant/System.Xml.XDocument.xml", "ref/netstandard1.3/System.Xml.XDocument.dll", "ref/netstandard1.3/System.Xml.XDocument.xml", "ref/netstandard1.3/de/System.Xml.XDocument.xml", "ref/netstandard1.3/es/System.Xml.XDocument.xml", "ref/netstandard1.3/fr/System.Xml.XDocument.xml", "ref/netstandard1.3/it/System.Xml.XDocument.xml", "ref/netstandard1.3/ja/System.Xml.XDocument.xml", "ref/netstandard1.3/ko/System.Xml.XDocument.xml", "ref/netstandard1.3/ru/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hans/System.Xml.XDocument.xml", "ref/netstandard1.3/zh-hant/System.Xml.XDocument.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.xml.xdocument.4.3.0.nupkg.sha512", "system.xml.xdocument.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.7.2": ["Sick.GenIStreamDotNet >= 4.2.0.20192", "Sick.Stream.Algorithms.DotNet >= ********", "Sick.Stream.Common >= ********", "Sick.Stream.Controls >= ********", "Sick.Stream.Processing >= ********", "Sick.Stream.Processing.Tools >= ********", "System.Net.Http >= 4.3.4", "System.Text.RegularExpressions >= 4.3.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj", "projectName": "CameraIntegrationWpf", "projectPath": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\CameraIntegrationWpf.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\SICK\\GUI Examples_Update\\CameraIntegrationWpf\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net472"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "D:\\SICK\\packages": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net472": {"targetAlias": "net472", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net472": {"targetAlias": "net472", "dependencies": {"Sick.GenIStreamDotNet": {"target": "Package", "version": "[4.2.0.20192, )"}, "Sick.Stream.Algorithms.DotNet": {"target": "Package", "version": "[********, )"}, "Sick.Stream.Common": {"target": "Package", "version": "[********, )"}, "Sick.Stream.Controls": {"target": "Package", "version": "[********, )"}, "Sick.Stream.Processing": {"target": "Package", "version": "[********, )"}, "Sick.Stream.Processing.Tools": {"target": "Package", "version": "[********, )"}, "System.Net.Http": {"target": "Package", "version": "[4.3.4, )"}, "System.Text.RegularExpressions": {"target": "Package", "version": "[4.3.1, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}}