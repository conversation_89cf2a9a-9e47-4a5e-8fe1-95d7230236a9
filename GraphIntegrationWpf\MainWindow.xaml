<Window x:Class="GraphIntegrationWpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:Sick.Stream.Controls;assembly=Sick.Stream.Controls"
        mc:Ignorable="d"
        Title="MainWindow" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="200"/>
        </Grid.ColumnDefinitions>

        <controls:Graph2D x:Name="Graph2D" IsPointsVisible="True"/>

        <StackPanel Grid.Column="1" Margin="5">
            <Button Content="Run program" Click="RunProgramClick"/>

            <Label Content="Number of samples:" />
            <TextBox Text="{Binding ExtractProfile.NumberOfSamples}"/>

            <CheckBox IsChecked="{Binding ElementName=Graph2D, Path=IsPointsVisible}"
                      Margin="0,10"
                      Content="Show profile points"/>
        </StackPanel>
    </Grid>
</Window>
