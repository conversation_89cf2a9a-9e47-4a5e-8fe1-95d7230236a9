// Copyright 2023-2025 SICK AG. All rights reserved.

using System;
using System.Windows;
using System.Windows.Media;
using Sick.Stream.Processing;
using Sick.Stream.Processing.Tools;
using System.IO;

namespace GraphIntegrationWpf
{
    /// <summary>
    /// This example shows how to integrate the GraphControl in a WPF application.
    /// The example loads an environment with a step program containing a ExtractProfile step.
    /// After running the program the extracted profile is visualized in the graph control.
    /// </summary>
    public partial class MainWindow : Window
    {
        private readonly IStepProgram _program;

        public MainWindow()
        {
            InitializeComponent();
            DataContext = this;
            string projectDir = Directory.GetParent(Environment.CurrentDirectory).Parent.Parent.Parent.FullName;

            // Load the environment file.
            IProcessingEnvironment environment = new ProcessingEnvironment();
            environment.Load(Path.Combine(projectDir, "Environment.envi"));

            // Load a frame and add it to the ProcessEnvironment.
            environment.LoadImage("Image", Path.Combine(projectDir, "..\\Images\\image.dat"));

            // Get the step program from the environment.
            _program = environment.GetStepProgram("Program");

            // Get the FindBlobs tool from the step program.
            ExtractProfile = _program.FindStep<ExtractProfile>("Extract profile").Tool;
        }

        public ExtractProfile ExtractProfile { get; }

        private void RunProgramClick(object sender, RoutedEventArgs e)
        {
            _program.RunProgram();
            Graph2D.DrawVariable(ExtractProfile.Profiles, Colors.Green);
        }
    }
}
