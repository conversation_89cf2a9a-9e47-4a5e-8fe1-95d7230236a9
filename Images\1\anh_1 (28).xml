<icon_data_format name="" valuetype="">
    <parameter name="size">9620</parameter>
    <parameter name="version">1</parameter>
    <parameter name="layout">SUBCOMPONENT</parameter>
    <component name="Ranger3Range" valuetype="Hi3D">
        <parameter name="size">9620</parameter>
        <parameter name="height">1</parameter>
        <worldrangetraits>
            <parameter name="lower bound x">24.03056526184082</parameter>
            <parameter name="upper bound x">74.780769562348723</parameter>
            <parameter name="lower bound r">25.034015599056147</parameter>
            <parameter name="upper bound r">47.52962881012354</parameter>
            <parameter name="coordinate unit"></parameter>
        </worldrangetraits>
        <genistreamtraits>
            <parameter name="region id">scan 3d extraction 1</parameter>
            <parameter name="extraction method">hi 3d</parameter>
            <parameter name="output mode">rectified c</parameter>
            <parameter name="width">2560</parameter>
            <parameter name="height">832</parameter>
            <parameter name="offset X">0</parameter>
            <parameter name="offset Y">0</parameter>
            <parameter name="a axis range scale">0.015864396467804909</parameter>
            <parameter name="a axis range offset">24.03056526184082</parameter>
            <parameter name="a axis range min">0</parameter>
            <parameter name="a axis range max">3199</parameter>
            <parameter name="a axis range missing">false</parameter>
            <parameter name="a axis range missing value">0</parameter>
            <parameter name="b axis range scale">0.10999999940395355</parameter>
            <parameter name="b axis range offset">0</parameter>
            <parameter name="b axis range min">-inf</parameter>
            <parameter name="b axis range max">inf</parameter>
            <parameter name="b axis range missing">false</parameter>
            <parameter name="b axis range missing value">0</parameter>
            <parameter name="c axis range scale">0.00034326629247516394</parameter>
            <parameter name="c axis range offset">25.033672332763672</parameter>
            <parameter name="c axis range min">1</parameter>
            <parameter name="c axis range max">65535</parameter>
            <parameter name="c axis range missing">true</parameter>
            <parameter name="c axis range missing value">0</parameter>
            <parameter name="unit">millimeter</parameter>
            <parameter name="skew x">0</parameter>
            <parameter name="skew y">1</parameter>
            <parameter name="skew z">0</parameter>
        </genistreamtraits>
        <subcomponent name="Range" valuetype="WORD">
            <parameter name="size">6400</parameter>
            <parameter name="width">3200</parameter>
        </subcomponent>
        <subcomponent name="Intensity" valuetype="BYTE">
            <parameter name="size">3200</parameter>
            <parameter name="width">3200</parameter>
        </subcomponent>
        <subcomponent name="Mark" valuetype="INT">
            <parameter name="size">20</parameter>
            <parameter name="width">5</parameter>
        </subcomponent>
    </component>
    <genistream>
        <parameter name="version">0.0.0.0</parameter>
        <parameter name="save time">2025-08-25T04:10:19Z</parameter>
        <parameter name="frame id">186</parameter>
        <parameter name="frame completeness">complete</parameter>
        <parameter name="delivered frame height">500</parameter>
        <parameter name="camera laser angle">38</parameter>
        <framemetadata>
            <parameter name="frame trigger counter">1460</parameter>
        </framemetadata>
    </genistream>
    <additionalinfo>
        <parameter name="acquisition start time">2025-08-25T03:40:30Z</parameter>
        <parameter name="grab time">2025-08-25T04:10:12Z</parameter>
        <parameter name="gentl producer">SICKGigEVisionTL</parameter>
        <parameter name="gentl producer version">3.1.0.25</parameter>
        <parameter name="device model">Ruler3004</parameter>
        <parameter name="device serial number">24280001</parameter>
        <parameter name="device name">SICK AG Ruler3004 (SICKGigEVisionTL_DEV_00067762cd4b)</parameter>
        <parameter name="device firmware">FW:4.2.0.20192,FPGA:5.1.2263 (RELEASE 20250328T15:00ZZ rev: "a44de66a92d43c3c8ab38e17ec43dfaf5d791ad2")</parameter>
        <parameter name="device manufacturer info">Type:V3DX3-004BR21A Part:1126984</parameter>
        <parameter name="device boot path">Primary</parameter>
        <parameter name="gev stream channel packet size">1456</parameter>
        <cameraconfig>
            #Version,1
            #Root
            #DeviceControl
            DeviceScanType,Linescan3D
            DeviceLinkThroughputLimit,118750000
            #ImageFormatControl
            ExposureTime_RegionSelector_Region0,40
            TestPattern_ComponentSelector_Intensity_RegionSelector_Region0,Off
            ExposureTime_RegionSelector_Region1,40
            Width_RegionSelector_Region1,2560
            Height_RegionSelector_Region1,832
            OffsetX_RegionSelector_Region1,0
            OffsetY_RegionSelector_Region1,0
            ExposureTime_RegionSelector_Region1DualExposure,3
            Height_RegionSelector_Scan3dExtraction1,500
            ComponentEnable_ComponentSelector_Range_RegionSelector_Scan3dExtraction1,1
            PixelFormat_ComponentSelector_Range_RegionSelector_Scan3dExtraction1,Coord3D_C16
            TestPattern_ComponentSelector_Range_RegionSelector_Scan3dExtraction1,Off
            ComponentEnable_ComponentSelector_Reflectance_RegionSelector_Scan3dExtraction1,1
            ComponentEnable_ComponentSelector_Scatter_RegionSelector_Scan3dExtraction1,0
            PixelFormat_ComponentSelector_Scatter_RegionSelector_Scan3dExtraction1,Mono16
            #Scan3dControl
            Scan3dExtractionMethod_Scan3dExtractionSelector_Scan3dExtraction1,Hi3D
            WamSize_Scan3dExtractionSelector_Scan3dExtraction1,Normal
            SearchMode3D_Scan3dExtractionSelector_Scan3dExtraction1,GlobalMax
            RangeAxis_Scan3dExtractionSelector_Scan3dExtraction1,Standard
            RangeResolution_Scan3dExtractionSelector_Scan3dExtraction1,SixteenthPixel
            RoiOptimalMarginLow_Scan3dExtractionSelector_Scan3dExtraction1,0
            RoiOptimalMarginHigh_Scan3dExtractionSelector_Scan3dExtraction1,1
            DetectionThreshold_Scan3dExtractionSelector_Scan3dExtraction1,20
            SearchDirection_Scan3dExtractionSelector_Scan3dExtraction1,Standard
            ReflectanceFilter_Scan3dExtractionSelector_Scan3dExtraction1,1
            ScatterMode_Scan3dExtractionSelector_Scan3dExtraction1,SymmetricSideBand
            ScatterOffset_Scan3dExtractionSelector_Scan3dExtraction1,6
            ScatterWidth_Scan3dExtractionSelector_Scan3dExtraction1,4
            ScatterGain_Scan3dExtractionSelector_Scan3dExtraction1,4
            ScatterReductionCurve_Scan3dExtractionSelector_Scan3dExtraction1,Linear
            ScatterReductionLowerLimit_Scan3dExtractionSelector_Scan3dExtraction1,0
            ScatterReductionUpperLimit_Scan3dExtractionSelector_Scan3dExtraction1,1500
            ScatterReductionKnee1Input_Scan3dExtractionSelector_Scan3dExtraction1,22
            ScatterReductionKnee1Output_Scan3dExtractionSelector_Scan3dExtraction1,12
            ScatterReductionKnee2Input_Scan3dExtractionSelector_Scan3dExtraction1,54
            ScatterReductionKnee2Output_Scan3dExtractionSelector_Scan3dExtraction1,39
            Scan3dOutputMode_Scan3dExtractionSelector_Scan3dExtraction1,RectifiedC
            Scan3dCalibrationModel_Scan3dExtractionSelector_Scan3dExtraction1,FactoryCalibration
            Scan3dRectificationWidth_Scan3dExtractionSelector_Scan3dExtraction1,3200
            Scan3dRectificationMethod_Scan3dExtractionSelector_Scan3dExtraction1,TopMost
            Scan3dRectificationSpread_Scan3dExtractionSelector_Scan3dExtraction1,1.20000005
            Scan3dRectificationAdjustment_Scan3dExtractionSelector_Scan3dExtraction1,Auto
            Scan3dRectificationAdjustmentResolution_Scan3dExtractionSelector_Scan3dExtraction1,1
            Scan3dRectificationAdjustmentOffset_Scan3dExtractionSelector_Scan3dExtraction1,0
            Scan3dDualExposure_Scan3dExtractionSelector_Scan3dExtraction1,Off
            Scan3dBlendingThreshold_Scan3dExtractionSelector_Scan3dExtraction1,170
            DetectionThreshold_Scan3dExtractionSelector_Scan3dExtraction1DualExposure,20
            #AcquisitionControl
            AcquisitionMode,Continuous
            AcquisitionStopMode,Complete
            AcquisitionFrameRate,20
            AcquisitionLineRate,1000
            TriggerMode_TriggerSelector_LineStart,On
            TriggerSource_TriggerSelector_LineStart,Encoder
            TriggerMode_TriggerSelector_FrameStart,On
            TriggerSource_TriggerSelector_FrameStart,FrameTriggerInput
            TriggerActivation_TriggerSelector_FrameStart,RisingEdge
            TriggerDelaySource_TriggerSelector_FrameStart,TimeBased
            TriggerDelayDistance_TriggerSelector_FrameStart,0
            TriggerDelay_TriggerSelector_FrameStart,0
            TriggerMode_TriggerSelector_AreascanFrameStart,Off
            TriggerSource_TriggerSelector_AreascanFrameStart,Encoder
            TriggerSource_TriggerSelector_Region1ExposureStart,LineStart
            TriggerDelay_TriggerSelector_Region1ExposureStart,1.29999995
            HoldFrameLevelOneLineAfterPulseEnd,0
            MultiSlopeMode,PresetMedium
            MultiSlopeKneePointCount,1
            #DigitalIOControl
            LineFormat_LineSelector_EncoderAInput,RS422
            LineFormat_LineSelector_EncoderBInput,RS422
            LineSource_LineSelector_LaserStrobe1Output,All3dExposures
            LineInverter_LineSelector_LaserStrobe2Output,1
            LineSource_LineSelector_LaserStrobe2Output,LaserStrobe2Timer
            LineRiseTime_LineSelector_LaserStrobe2Output,0
            LineInverter_LineSelector_FrameSynchronizationOutput,0
            SignalConfiguration_IOPinSelector_IO4,Input
            #CounterAndTimerControl
            TimerDuration_TimerSelector_LaserStrobe1Timer,0
            TimerDelay_TimerSelector_LaserStrobe1Timer,0
            TimerTriggerSource_TimerSelector_LaserStrobe1Timer,Off
            TimerDuration_TimerSelector_LaserStrobe2Timer,0
            TimerDelay_TimerSelector_LaserStrobe2Timer,0
            TimerTriggerSource_TimerSelector_LaserStrobe2Timer,Off
            TimerDuration_TimerSelector_FrameSynchronizationTimer,2
            TimerTriggerSource_TimerSelector_FrameSynchronizationTimer,FrameStart
            #EncoderControl
            EncoderResolution,0.109999999
            EncoderMode,FourPhase
            EncoderDivider,1
            EncoderOutputMode,Motion
            EncoderResetSource,Off
            #EventControl
            EventNotification_EventSelector_LogMessage,On
            #LogMessageData
            #EventTestData
            #UserSetControl
            #FileAccessControl
            #ChunkDataControl
            ChunkModeActive,1
            #TestControl
            TestPayloadFormatMode,Off
            #TransportLayerControl
            #GigEVision
            GevSCPD,0
            #PtpControl
            #Capabilities
            #CalibrationConditions
            #ImagerControl
            ImagerScheduler,Standard
            ImagerExposureStartAlignment,Off
            #ProductConfiguration
            #LaserControl
            #FirmwareUpdate
            #FrameBufferStatus
            #PartSyncStatus
            #MultipartFrontendStatus
            #DescramblerStatus
            #PostProcessingStatus
            #CalibrationStatus
            #RectificationStatus
            #ImagerInterfaceStatus
            #M30Debug
            #M30RegisterPeekPoke
            #DataDumper
            #LogEventTest
            #DataTypesTest
            #ValidationTest
            #AvailableTest
        </cameraconfig>
        <datastreamstatistics>
            <parameter name="seen packet count">639282</parameter>
            <parameter name="lost packet count">0</parameter>
            <parameter name="resend command count">0</parameter>
            <parameter name="resend packet count">0</parameter>
            <parameter name="delivered packet count">639282</parameter>
            <parameter name="unavailable packet count">0</parameter>
            <parameter name="duplicate packet count">0</parameter>
            <parameter name="skipped block count">0</parameter>
            <parameter name="engine underrun count">0</parameter>
            <parameter name="discarded block count">0</parameter>
            <parameter name="incomplete block count">0</parameter>
            <parameter name="oversized block count">0</parameter>
        </datastreamstatistics>
    </additionalinfo>
</icon_data_format>
