# Hướng dẫn sử dụng ứng dụng Detection

## C<PERSON><PERSON> cải thiện đã thực hiện:

### 1. **Sửa lỗi parsing YOLOv8 output**
- Đ<PERSON> cập nhật logic parse để phù hợp với format YOLOv8 mới (không có objectness score riêng)
- YOLOv8 output format: [x_center, y_center, width, height, class0_conf, class1_conf, ...]

### 2. **Cải thiện confidence threshold**
- Giảm threshold từ 0.6 xuống 0.5 để detect nhiều object hơn
- <PERSON><PERSON> hợp với setting trong Python code (conf=0.6)

### 3. **Thêm Non-Maximum Suppression (NMS)**
- Loại bỏ các detection trùng lặp
- IoU threshold = 0.4 để tránh duplicate bounding boxes

### 4. **Cải thiện visualization**
- Tăng font size lên 16 và độ dày viền lên 3
- Căn giữa text phía trên bounding box
- Chỉ hiển thị "NG" hoặc "OK" (không hiển thị confidence)
- <PERSON><PERSON><PERSON> đỏ cho NG (class 0), màu xanh lá cho OK (class khác)

### 5. **Thêm debug logging**
- Hiển thị thông tin về output shape
- Log số lượng detections tìm được
- Thông tin chi tiết về từng detection

### 6. **Cải thiện preprocessing**
- Đảm bảo ảnh được convert sang RGB format
- Letterbox resize với padding đen
- Normalize pixel values về [0, 1]

## Cách sử dụng:

### Bước 1: Chạy ứng dụng
```bash
cd "Viewer3DIntegrationWpf"
dotnet run
```

### Bước 2: Load ảnh 3D
1. Click nút **"Load Image"**
2. Chọn file .dat (ảnh 3D từ SICK camera)
3. Ảnh sẽ được hiển thị ở Viewer2D và Viewer3D

### Bước 3: Chạy detection
1. Click nút **"Run program"**
2. Ứng dụng sẽ:
   - Preprocess ảnh (resize về 640x640)
   - Chạy ONNX model detection
   - Vẽ bounding boxes với labels NG/OK
   - Hiển thị kết quả ở YoloViewer (bên phải)

### Bước 4: Xem kết quả
- Kết quả detection hiển thị ở panel bên phải
- Bounding boxes màu đỏ = NG (class 0)
- Bounding boxes màu xanh = OK (class khác 0)
- Text log hiển thị thông tin chi tiết

## Troubleshooting:

### Nếu không detect được gì:
1. **Kiểm tra model path**: Đảm bảo file `nidec/models/best.onnx` tồn tại
2. **Kiểm tra ảnh input**: Ảnh phải có đối tượng rõ ràng
3. **Giảm confidence threshold**: Sửa trong code từ 0.5 xuống 0.3
4. **Kiểm tra log**: Xem thông tin debug trong ResultText

### Nếu model không load được:
1. Kiểm tra đường dẫn model
2. Đảm bảo ONNX Runtime được cài đặt đúng
3. Kiểm tra format model (phải là YOLOv8 ONNX)

### Nếu detection không chính xác:
1. Kiểm tra preprocessing (resize, normalize)
2. Điều chỉnh confidence threshold
3. Điều chỉnh NMS IoU threshold
4. Kiểm tra class mapping (0=NG, khác=OK)

## Kết quả mong đợi:
- Giống như ảnh mẫu với các bounding boxes màu đỏ (NG) và xanh (OK)
- Text "NG" hoặc "OK" hiển thị phía trên mỗi bounding box
- Không hiển thị confidence score (chỉ hiển thị trong log)
