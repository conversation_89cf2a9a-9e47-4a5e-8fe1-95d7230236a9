<Window x:Class="Viewer3DIntegrationWpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:Sick.Stream.Controls;assembly=Sick.Stream.Controls"
        mc:Ignorable="d"
        Title="MainWindow" Height="600" Width="900"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"/>
            <ColumnDefinition Width="300"/>
        </Grid.ColumnDefinitions>

        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="200"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <controls:Viewer2DToolbar Viewer="{Binding ElementName=Viewer2D}"
                                          HorizontalAlignment="Left"
                                          Margin="5"
                                          Grid.Column="0"/>
                <controls:Viewer3DToolbar Viewer="{Binding ElementName=Viewer3D}"
                          HorizontalAlignment="Left"
                          Margin="5" Grid.Column="1"/>
            </Grid>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid Grid.Column="0" Margin="0">
                    <controls:Viewer2D x:Name="Viewer2D" Background="Black" MouseOverInfo="True"/>
                </Grid>
                <Grid Grid.Column="1" Margin="5 0 0 0">
                    <controls:Viewer3D x:Name="Viewer3D"
                   Background="Black"
                   MouseOverInfo="True"/>
                </Grid>
                
            </Grid>
            
            <Grid Grid.Row="2" Margin="5">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="2" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="3" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="4" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="5" BorderBrush="Black" BorderThickness="1" Margin="0 0 5 0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                <Border Grid.Column="6" BorderBrush="Black" BorderThickness="1" Margin="0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        <TextBlock Text="NG" FontSize="24" FontWeight="Bold"  HorizontalAlignment="Center" Margin="0" Background="Red"/>
                    </Grid>
                </Border>
                
            </Grid>
        </Grid>

        <StackPanel Grid.Column="1" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="400"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0">
                    <!-- Nhóm nút bấm -->
                    <StackPanel Orientation="Vertical" HorizontalAlignment="Center">
                        <Button Content="Run program" Width="120" Height="30" Click="RunProgramClick" Margin="0 10"/>
                        <Button Content="Load Image" Width="120" Height="30" Click="LoadImageClick" Margin="0 10"/>
                        <Button Content="Save Image" Width="120" Height="30" Click="SaveImageClick" Margin="0 10"/>
                    </StackPanel>
                </Grid>

                <Grid Grid.Row="2">
                    <!-- Kết quả -->
                    <TextBlock x:Name="ResultText" Margin="0,20,0,0" FontSize="14" Foreground="DarkBlue" Text="Chưa có kết quả" TextWrapping="Wrap"/>
                </Grid>

                <Grid Grid.Row="1">
                    <Image x:Name="YoloViewer" Grid.Column="2"/>
                </Grid>

            </Grid>
        </StackPanel>

    </Grid>
</Window>
