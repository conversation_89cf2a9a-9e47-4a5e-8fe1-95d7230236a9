// Copyright 2023-2025 SICK AG. All rights reserved.

using Microsoft.ML.OnnxRuntime;
using Microsoft.ML.OnnxRuntime.Tensors;
using Microsoft.Win32;
using Sick.Stream.Algorithms;
using Sick.Stream.Processing;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Media.Imaging;

// <PERSON>as để tránh trùng lặp
using SD = System.Drawing;
using SDI = System.Drawing.Imaging;
using SWMI = System.Windows.Media.Imaging;

namespace Viewer3DIntegrationWpf
{
    public partial class MainWindow : Window
    {
        private IProcessingEnvironment _environment;
        private SD.Bitmap _detectBitmap;
        private InferenceSession _session;

        // model và input size
        string modelPath = @"D:\SICK\GUI Examples_Update\Viewer3DIntegrationWpf\nidec\models\best.onnx";
        int inputWidth = 640;
        int inputHeight = 640;

        public MainWindow()
        {
            InitializeComponent();
            ResultText.Text = "App started. Load 3D image and run detection...\n";

            // khởi tạo session (bắt lỗi nếu model không tìm thấy)
            try
            {
                _session = new InferenceSession(modelPath);
                ResultText.Text += $"Loaded model: {modelPath}\n";
            }
            catch (Exception ex)
            {
                ResultText.Text += $"Failed to load model: {ex.Message}\n";
                MessageBox.Show($"Cannot load ONNX model: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // ---- Run button: chạy detection, vẽ overlay, hiển thị kết quả ----
        private void RunProgramClick(object sender, RoutedEventArgs e)
        {
            if (_detectBitmap == null)
            {
                MessageBox.Show("Chưa load ảnh nào!", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (_session == null)
            {
                MessageBox.Show("Model chưa được load!", "Lỗi", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            ResultText.Text += "Bắt đầu detection...\n";

            try
            {
                // 1) Run detection
                var results = RunDetection(_detectBitmap);

                // 2) Vẽ bounding box + label lên ảnh gốc (không scale)
                var detectedBmp = DrawDetections(_detectBitmap, results);

                // 3) Convert sang BitmapSource để hiển thị ở YoloViewer (WPF Image)
                SWMI.BitmapSource detectedSrc = BitmapToSource(detectedBmp);

                YoloViewer.Source = detectedSrc;

                // 4) Log kết quả
                if (results.Count > 0)
                {
                    ResultText.Text += $"Tìm thấy {results.Count} đối tượng:\n";
                    foreach (var box in results)
                    {
                        string status = box.ClassId == 0 ? "NG" : "OK";
                        ResultText.Text += $"- {status} tại [{box.X},{box.Y},{box.Width},{box.Height}], confidence={box.Confidence:F3}\n";
                    }
                }
                else
                {
                    ResultText.Text += "Không tìm thấy đối tượng nào.\n";
                }
            }
            catch (Exception ex)
            {
                ResultText.Text += $"Lỗi khi chạy detection: {ex.Message}\n";
                MessageBox.Show($"Lỗi: {ex.Message}", "Lỗi Detection", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        // ---- Load .dat và chuẩn bị ảnh để detect ----
        private void LoadImageClick(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "Chọn file ảnh 3D",
                Filter = "Data files (*.dat)|*.dat|All files (*.*)|*.*",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments)
            };

            if (openFileDialog.ShowDialog() == true)
            {
                try
                {
                    _environment = new ProcessingEnvironment();
                    var image = _environment.LoadImage("Image", openFileDialog.FileName);
                    _lastLoadedFile = openFileDialog.FileName;
                    ResultText.Text += $"Loaded: {openFileDialog.FileName}\n";

                    Viewer3D.Image = image;
                    Viewer2D.Image = image;

                    // tạo BitmapSource đã hiệu chỉnh tỉ lệ → chuyển sang System.Drawing.Bitmap để detect
                    BitmapSource detectImg = GetCorrectedRangeBitmap(image);
                    _detectBitmap = BitmapFromSource(detectImg);

                    ResultText.Text = "Image prepared for detection.\n";
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Load image failed: {ex.Message}", "Error", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                MessageBox.Show("Bạn chưa chọn file nào!", "Thông báo", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        // Helper struct để lưu thông tin letterbox
        // Struct lưu thông tin resize letterbox
        public struct ResizeInfo
        {
            public float Scale;
            public int PadW;
            public int PadH;
            public int OrigW;
            public int OrigH;
        }

        private (DenseTensor<float>, ResizeInfo) PreprocessImage(SD.Bitmap image)
        {
            int origW = image.Width;
            int origH = image.Height;

            ResultText.Text += $"Original image size: {origW}x{origH}\n";

            // Letterbox resize về 640x640
            var resized = LetterboxResize(image, inputWidth, inputHeight, out ResizeInfo resizeInfo);

            ResultText.Text += $"Resized to: {resized.Width}x{resized.Height}, scale: {resizeInfo.Scale:F3}, pad: ({resizeInfo.PadW}, {resizeInfo.PadH})\n";

            var tensor = new DenseTensor<float>(new[] { 1, 3, inputHeight, inputWidth });

            // Đảm bảo ảnh ở định dạng RGB
            var rgbBitmap = ToRgbBitmap(resized);

            var bmpData = rgbBitmap.LockBits(
                new SD.Rectangle(0, 0, rgbBitmap.Width, rgbBitmap.Height),
                SDI.ImageLockMode.ReadOnly,
                SDI.PixelFormat.Format24bppRgb);

            int stride = bmpData.Stride;
            int bytes = stride * rgbBitmap.Height;
            byte[] rgbValues = new byte[bytes];
            System.Runtime.InteropServices.Marshal.Copy(bmpData.Scan0, rgbValues, 0, bytes);

            rgbBitmap.UnlockBits(bmpData);

            // Normalize pixel values to [0, 1] và sắp xếp theo format CHW
            for (int y = 0; y < inputHeight; y++)
            {
                for (int x = 0; x < inputWidth; x++)
                {
                    int idx = y * stride + x * 3;
                    if (idx + 2 < rgbValues.Length)
                    {
                        byte b = rgbValues[idx + 0];
                        byte g = rgbValues[idx + 1];
                        byte r = rgbValues[idx + 2];

                        // Normalize to [0, 1]
                        tensor[0, 0, y, x] = r / 255f;
                        tensor[0, 1, y, x] = g / 255f;
                        tensor[0, 2, y, x] = b / 255f;
                    }
                }
            }

            resizeInfo.OrigW = origW;
            resizeInfo.OrigH = origH;

            // Cleanup
            if (rgbBitmap != resized)
                rgbBitmap.Dispose();

            return (tensor, resizeInfo);
        }

        private List<DetectionResult> RunDetection(SD.Bitmap bmp)
        {
            var (inputTensor, resizeInfo) = PreprocessImage(bmp);

            using var results = _session.Run(new List<NamedOnnxValue>
    {
        NamedOnnxValue.CreateFromTensor("images", inputTensor)
    });

            var output = results.First().AsTensor<float>();
            return ParseYoloOutput(output, resizeInfo);
        }

        private List<DetectionResult> ParseYoloOutput(Tensor<float> output, ResizeInfo resizeInfo,
                                                      float confThreshold = 0.6f)
        {
            var results = new List<DetectionResult>();

            // YOLOv8: output shape [1, 84, 8400] (batch, channels, num_predictions)
            int batch = output.Dimensions[0];
            int channels = output.Dimensions[1]; // 84 = 4 box + 80 classes (không có objectness riêng)
            int numPred = output.Dimensions[2];  // 8400

            ResultText.Text += $"Output shape: [{batch}, {channels}, {numPred}]\n";

            for (int i = 0; i < numPred; i++)
            {
                // YOLOv8 format: [x_center, y_center, width, height, class0_conf, class1_conf, ...]
                float x = output[0, 0, i];
                float y = output[0, 1, i];
                float w = output[0, 2, i];
                float h = output[0, 3, i];

                // Tìm class có confidence cao nhất
                int bestClass = -1;
                float maxClassConf = 0;
                for (int c = 4; c < channels; c++)
                {
                    float classConf = output[0, c, i];
                    if (classConf > maxClassConf)
                    {
                        maxClassConf = classConf;
                        bestClass = c - 4; // class index bắt đầu từ 0
                    }
                }

                // Kiểm tra confidence threshold
                if (maxClassConf < confThreshold) continue;

                // Scale ngược về ảnh gốc
                float bx = (x - resizeInfo.PadW) / resizeInfo.Scale;
                float by = (y - resizeInfo.PadH) / resizeInfo.Scale;
                float bw = w / resizeInfo.Scale;
                float bh = h / resizeInfo.Scale;

                // Convert từ center format sang top-left format
                int X = (int)(bx - bw / 2);
                int Y = (int)(by - bh / 2);
                int W = (int)bw;
                int H = (int)bh;

                // Clamp trong ảnh gốc
                if (X < 0) X = 0;
                if (Y < 0) Y = 0;
                if (X + W > resizeInfo.OrigW) W = resizeInfo.OrigW - X;
                if (Y + H > resizeInfo.OrigH) H = resizeInfo.OrigH - Y;

                // Chỉ thêm nếu bounding box hợp lệ
                if (W > 0 && H > 0)
                {
                    results.Add(new DetectionResult
                    {
                        Label = $"Class_{bestClass}",
                        ClassId = bestClass,
                        X = X,
                        Y = Y,
                        Width = W,
                        Height = H,
                        Confidence = maxClassConf
                    });
                }
            }

            ResultText.Text += $"Found {results.Count} detections\n";
            return results;
        }

        private SD.Bitmap LetterboxResize(SD.Bitmap src, int targetW, int targetH, out ResizeInfo info)
        {
            float scale = Math.Min((float)targetW / src.Width, (float)targetH / src.Height);
            int newW = (int)(src.Width * scale);
            int newH = (int)(src.Height * scale);

            int padW = (targetW - newW) / 2;
            int padH = (targetH - newH) / 2;

            var resized = new SD.Bitmap(targetW, targetH);
            using (var g = SD.Graphics.FromImage(resized))
            {
                g.Clear(SD.Color.Black);
                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                g.DrawImage(src, padW, padH, newW, newH);
            }

            info = new ResizeInfo
            {
                Scale = scale,
                PadW = padW,
                PadH = padH
            };

            return resized;
        }

        // ---- Drawing helpers ----
        private SD.Bitmap DrawDetections(SD.Bitmap source, List<DetectionResult> detections)
        {
            SD.Bitmap bmp = ToRgbBitmap(source); // đảm bảo ảnh RGB

            using (SD.Graphics g = SD.Graphics.FromImage(bmp))
            using (SD.Font font = new SD.Font("Arial", 16, SD.FontStyle.Bold))
            {
                g.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                g.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;

                foreach (var det in detections)
                {
                    // Nếu class == 0 => NG (đỏ), ngược lại OK (xanh lá)
                    SD.Color color = det.ClassId == 0 ? SD.Color.Red : SD.Color.Lime;
                    string label = det.ClassId == 0 ? "NG" : "OK";

                    using (SD.Pen pen = new SD.Pen(color, 3)) // Tăng độ dày viền
                    using (SD.SolidBrush brush = new SD.SolidBrush(color))
                    {
                        // Vẽ bounding box
                        g.DrawRectangle(pen, det.X, det.Y, det.Width, det.Height);

                        // Label text - chỉ hiển thị NG/OK, không hiển thị confidence
                        string text = label;
                        SD.SizeF size = g.MeasureString(text, font);

                        // Tính toán vị trí text ở giữa phía trên bounding box
                        float textX = det.X + (det.Width - size.Width) / 2;
                        float textY = det.Y - size.Height - 5;

                        // Đảm bảo text không bị cắt ở rìa ảnh
                        if (textX < 0) textX = 0;
                        if (textY < 0) textY = det.Y + 5;
                        if (textX + size.Width > bmp.Width) textX = bmp.Width - size.Width;

                        // Vẽ nền đen cho text để dễ đọc
                        g.FillRectangle(SD.Brushes.Black, textX - 2, textY - 2, size.Width + 4, size.Height + 4);

                        // Vẽ chữ
                        g.DrawString(text, font, brush, textX, textY);
                    }
                }
            }
            return bmp;
        }


        // Convert System.Drawing.Bitmap -> BitmapSource
        public static SWMI.BitmapSource BitmapToSource(SD.Bitmap bmp)
        {
            using (var ms = new MemoryStream())
            {
                bmp.Save(ms, SDI.ImageFormat.Bmp);
                ms.Seek(0, SeekOrigin.Begin);
                var image = new SWMI.BitmapImage();
                image.BeginInit();
                image.StreamSource = ms;
                image.CacheOption = SWMI.BitmapCacheOption.OnLoad;
                image.EndInit();
                image.Freeze();
                return image;
            }
        }

        // Convert BitmapSource -> System.Drawing.Bitmap
        public static SD.Bitmap BitmapFromSource(BitmapSource bitmapsource)
        {
            using (var outStream = new MemoryStream())
            {
                var encoder = new SWMI.BmpBitmapEncoder();
                encoder.Frames.Add(SWMI.BitmapFrame.Create(bitmapsource));
                encoder.Save(outStream);
                outStream.Seek(0, SeekOrigin.Begin);
                return new SD.Bitmap(outStream);
            }
        }

        private SD.Bitmap ToRgbBitmap(SD.Bitmap src)
        {
            var newBmp = new SD.Bitmap(src.Width, src.Height, SDI.PixelFormat.Format24bppRgb);
            using (SD.Graphics g = SD.Graphics.FromImage(newBmp))
            {
                g.DrawImage(src, new SD.Rectangle(0, 0, newBmp.Width, newBmp.Height));
            }
            return newBmp;
        }

        // ---- Save image button (keeps original behavior) ----
        private string _lastLoadedFile;
        private void SaveImageClick(object sender, RoutedEventArgs e)
        {
            if (Viewer2D.Image is IImage img)
            {
                string outputFolder = @"D:\SICK\GUI Examples\Images\OutputImages";
                Directory.CreateDirectory(outputFolder);

                string originalPath = _lastLoadedFile ?? "output";
                string defaultName = Path.GetFileNameWithoutExtension(originalPath) + ".png";

                var sfd = new SaveFileDialog
                {
                    Title = "Lưu ảnh Range chuẩn",
                    Filter = "PNG Image (*.png)|*.png",
                    FileName = defaultName,
                };

                if (sfd.ShowDialog() == true)
                {
                    BitmapSource corrected = GetCorrectedRangeBitmap(img);
                    using (var fs = new FileStream(sfd.FileName, FileMode.Create))
                    {
                        var encoder = new PngBitmapEncoder();
                        encoder.Frames.Add(SWMI.BitmapFrame.Create(corrected));
                        encoder.Save(fs);
                    }
                    ResultText.Text = $"Saved: {sfd.FileName}\n";
                }
            }
        }

        // Get image from IImage (Reflectance) and correct Y-resolution
        private BitmapSource GetCorrectedRangeBitmap(IImage img)
        {
            using (var frame = img.GetFrame())
            using (var comp = frame.GetReflectance())
            {
                uint width = comp.GetWidth();
                uint height = comp.GetDeliveredHeight();
                uint stride = comp.GetStride();
                uint bitsPerPixel = comp.GetBitsPerPixel();
                IntPtr dataPtr = comp.GetData();
                int dataSize = (int)comp.GetDeliveredDataSize();

                byte[] rawData = new byte[dataSize];
                System.Runtime.InteropServices.Marshal.Copy(dataPtr, rawData, 0, dataSize);

                BitmapSource bmp;

                if (bitsPerPixel == 8)
                {
                    bmp = BitmapSource.Create((int)width, (int)height, 96, 96,
                        System.Windows.Media.PixelFormats.Gray8, null, rawData, (int)stride);
                }
                else if (bitsPerPixel == 16)
                {
                    ushort[] src = new ushort[dataSize / 2];
                    Buffer.BlockCopy(rawData, 0, src, 0, dataSize);
                    ushort min = src.Min();
                    ushort max = src.Max();
                    double scale = max > min ? 255.0 / (max - min) : 1.0;
                    byte[] dst = new byte[src.Length];
                    for (int i = 0; i < src.Length; i++) dst[i] = (byte)((src[i] - min) * scale);
                    bmp = BitmapSource.Create((int)width, (int)height, 96, 96,
                        System.Windows.Media.PixelFormats.Gray8, null, dst, (int)width);
                }
                else
                {
                    throw new NotSupportedException($"Unsupported BitsPerPixel: {bitsPerPixel}");
                }

                // scale Y to make pixels "square"
                double xRes = 1.0;
                double yRes = img.YResolution;
                double scaleY = xRes / yRes;

                return new TransformedBitmap(bmp, new System.Windows.Media.ScaleTransform(1.0, scaleY));
            }
        }
    }

    public class DetectionResult
    {
        public string Label { get; set; }
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; }
        public int Height { get; set; }
        public float Confidence { get; set; }
        public int ClassId { get; set; }   // thêm classId
    }

}
