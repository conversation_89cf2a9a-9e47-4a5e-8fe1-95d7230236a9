import onnxruntime as ort
import numpy as np
import cv2
import os

def test_model():
    """Test ONNX model để kiểm tra input/output shape"""
    
    model_path = "models/best.onnx"
    
    if not os.path.exists(model_path):
        print(f"Model không tồn tại: {model_path}")
        return
    
    # Load model
    session = ort.InferenceSession(model_path)
    
    # In thông tin model
    print("=== THÔNG TIN MODEL ===")
    print(f"Model path: {model_path}")
    
    # Input info
    print("\nINPUTS:")
    for input_meta in session.get_inputs():
        print(f"  Name: {input_meta.name}")
        print(f"  Shape: {input_meta.shape}")
        print(f"  Type: {input_meta.type}")
    
    # Output info
    print("\nOUTPUTS:")
    for output_meta in session.get_outputs():
        print(f"  Name: {output_meta.name}")
        print(f"  Shape: {output_meta.shape}")
        print(f"  Type: {output_meta.type}")
    
    # Test với dummy input
    print("\n=== TEST VỚI DUMMY INPUT ===")
    input_shape = session.get_inputs()[0].shape
    print(f"Expected input shape: {input_shape}")
    
    # Tạo dummy input
    if input_shape[0] == 'batch_size' or input_shape[0] is None:
        batch_size = 1
    else:
        batch_size = input_shape[0]
    
    dummy_input = np.random.rand(batch_size, 3, 640, 640).astype(np.float32)
    print(f"Dummy input shape: {dummy_input.shape}")
    
    # Run inference
    input_name = session.get_inputs()[0].name
    outputs = session.run(None, {input_name: dummy_input})
    
    print(f"\nOutput shapes:")
    for i, output in enumerate(outputs):
        print(f"  Output {i}: {output.shape}")
        print(f"  Min: {output.min():.4f}, Max: {output.max():.4f}")
        
        # Nếu là output chính (thường là output đầu tiên)
        if i == 0:
            print(f"  Sample values from first detection:")
            if len(output.shape) == 3:  # [batch, features, detections]
                print(f"    First 10 values: {output[0, :10, 0]}")
            elif len(output.shape) == 2:  # [detections, features]
                print(f"    First 10 values: {output[0, :10]}")

def test_with_real_image():
    """Test với ảnh thật"""
    
    model_path = "models/best.onnx"
    test_image_path = r"D:\SICK\GUI Examples\Images\OutputImages\anh_3 (1).png"
    
    if not os.path.exists(model_path):
        print(f"Model không tồn tại: {model_path}")
        return
        
    if not os.path.exists(test_image_path):
        print(f"Test image không tồn tại: {test_image_path}")
        return
    
    print("\n=== TEST VỚI ẢNH THẬT ===")
    
    # Load model
    session = ort.InferenceSession(model_path)
    
    # Load và preprocess image
    image = cv2.imread(test_image_path)
    print(f"Original image shape: {image.shape}")
    
    # Resize về 640x640 (letterbox)
    def letterbox_resize(img, target_size=640):
        h, w = img.shape[:2]
        scale = min(target_size / w, target_size / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # Resize
        resized = cv2.resize(img, (new_w, new_h))
        
        # Pad
        pad_w = (target_size - new_w) // 2
        pad_h = (target_size - new_h) // 2
        
        padded = np.full((target_size, target_size, 3), 114, dtype=np.uint8)
        padded[pad_h:pad_h+new_h, pad_w:pad_w+new_w] = resized
        
        return padded, scale, pad_w, pad_h
    
    processed_img, scale, pad_w, pad_h = letterbox_resize(image)
    print(f"Processed image shape: {processed_img.shape}")
    
    # Convert to tensor format [1, 3, 640, 640]
    input_tensor = processed_img.transpose(2, 0, 1)  # HWC -> CHW
    input_tensor = input_tensor.astype(np.float32) / 255.0  # Normalize
    input_tensor = np.expand_dims(input_tensor, axis=0)  # Add batch dimension
    
    print(f"Input tensor shape: {input_tensor.shape}")
    print(f"Input tensor range: [{input_tensor.min():.3f}, {input_tensor.max():.3f}]")
    
    # Run inference
    input_name = session.get_inputs()[0].name
    outputs = session.run(None, {input_name: input_tensor})
    
    output = outputs[0]
    print(f"Output shape: {output.shape}")
    
    # Parse detections
    if len(output.shape) == 3:  # [batch, features, detections]
        detections = output[0]  # Remove batch dimension
        num_detections = detections.shape[1]
        
        print(f"Number of detection candidates: {num_detections}")
        
        # Check for valid detections
        valid_detections = 0
        for i in range(num_detections):
            # Lấy confidence scores (từ index 4 trở đi)
            class_scores = detections[4:, i]
            max_score = np.max(class_scores)
            
            if max_score > 0.5:  # Threshold
                valid_detections += 1
                class_id = np.argmax(class_scores)
                x, y, w, h = detections[:4, i]
                
                print(f"  Detection {valid_detections}: class={class_id}, score={max_score:.3f}, bbox=[{x:.1f}, {y:.1f}, {w:.1f}, {h:.1f}]")
        
        print(f"Total valid detections: {valid_detections}")

if __name__ == "__main__":
    test_model()
    test_with_real_image()
