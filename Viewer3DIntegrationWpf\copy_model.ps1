# Script để copy model file v<PERSON>o thư mục output

$sourceModel = "nidec\models\best.onnx"
$outputDir = "bin\AnyCPU\Debug\net472"
$targetDir = "$outputDir\nidec\models"

# Tạo thư mục target nếu chưa có
if (!(Test-Path $targetDir)) {
    New-Item -ItemType Directory -Path $targetDir -Force
    Write-Host "Created directory: $targetDir"
}

# Copy model file
if (Test-Path $sourceModel) {
    Copy-Item $sourceModel -Destination "$targetDir\best.onnx" -Force
    Write-Host "Copied model from $sourceModel to $targetDir\best.onnx"
} else {
    Write-Host "Source model not found: $sourceModel"
}

# Ki<PERSON><PERSON> tra kết quả
if (Test-Path "$targetDir\best.onnx") {
    Write-Host "Model file successfully copied!"
    $fileInfo = Get-Item "$targetDir\best.onnx"
    Write-Host "File size: $($fileInfo.Length) bytes"
} else {
    Write-Host "Failed to copy model file!"
}
