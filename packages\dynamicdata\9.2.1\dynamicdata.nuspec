﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>DynamicData</id>
    <version>9.2.1</version>
    <title>Dynamic Data</title>
    <authors><PERSON></authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>logo.png</icon>
    <readme>README.md</readme>
    <projectUrl>http://dynamic-data.org/</projectUrl>
    <description>Bring the power of Rx to collections using Dynamic Data.
Dynamic Data is a comprehensive caching and data manipulation solution which introduces domain centric observable collections.
      Linq extensions enable dynamic filtering, sorting, grouping, transforms, binding, pagination, data virtualisation, expiration, disposal management plus more.</description>
    <releaseNotes>https://github.com/reactiveui/DynamicData/releases</releaseNotes>
    <copyright>Copyright (c) <PERSON> 2011-2025</copyright>
    <tags>DynamicData Dynamic Data Rx Reactive Observable Cache Binding ObservableCache ObservableList ObservableCollection Collection Linq</tags>
    <repository type="git" url="https://github.com/reactiveui/DynamicData" branch="refs/heads/main" commit="2cefd22e985e1cf25d9b5c11028a5be03b411b30" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0">
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net7.0">
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net8.0">
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0">
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>