﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Functional.Maybe</id>
    <version>2.0.20</version>
    <authors><PERSON><PERSON>,<PERSON></authors>
    <owners><PERSON><PERSON>,<PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>https://raw.githubusercontent.com/AndreyTsvetkov/Functional.Maybe/master/License.txt</licenseUrl>
    <projectUrl>https://github.com/AndreyTsvetkov/Functional.Maybe</projectUrl>
    <iconUrl>http://findicons.com/files/icons/1462/candy_hearts/32/maybe.png</iconUrl>
    <description>Option types for C# with LINQ support and rich fluent syntax for many popular uses:

		var maybeOne = "one".ToMaybe();
		Maybe&lt;string&gt; maybeAnother;

		var maybeBoth = 
			from one in maybeOne
			from another in maybeAnother
			select one + another;

		maybeBoth.Match(
			both =&gt; Console.WriteLine("Result is: {0}", both), 
			@else: () =&gt; Console.WriteLine("Result is Nothing, as one of the inputs was Nothing")
		);</description>
    <releaseNotes>Migrated to Net Standart 1.0</releaseNotes>
    <tags>option, maybe, linq, fluent, functional</tags>
    <repository url="https://github.com/AndreyTsvetkov/Functional.Maybe" />
    <dependencies>
      <group targetFramework=".NETStandard1.0">
        <dependency id="NETStandard.Library" version="1.6.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>