﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/08/nuspec.xsd">
  <metadata>
    <id>IKVM.WINDWARD</id>
    <version>*******</version>
    <title>IKVM.NET.WINDWARD-FORK</title>
    <authors><PERSON><PERSON><PERSON></authors>
    <owners><PERSON><PERSON><PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <licenseUrl>http://sourceforge.net/apps/mediawiki/ikvm/index.php?title=License</licenseUrl>
    <projectUrl>https://github.com/wwrd/ikvm8</projectUrl>
    <description>IKVM.NET is an implementation of Java for Mono and the Microsoft .NET Framework.</description>
    <releaseNotes>The release notes can be found here: http://ikvm.net/release/*******</releaseNotes>
    <tags>IKVM.NET, Java, .NET</tags>
  </metadata>
</package>