﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>MaterialDesignColors</id>
    <version>2.0.6</version>
    <title>Material Design Colors XAML Resources</title>
    <authors><PERSON></authors>
    <owners><PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>images\MaterialDesignColors.Icon.png</icon>
    <projectUrl>https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit</projectUrl>
    <iconUrl>http://materialdesigninxaml.net/images/MD4XAML32.png</iconUrl>
    <description>ResourceDictionary instances containing standard Google Material Design swatches, for inclusion in a XAML application.</description>
    <releaseNotes>https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit/releases</releaseNotes>
    <copyright></copyright>
    <tags>WPF XAML Material Design Colour Color UI UX</tags>
    <dependencies>
      <group targetFramework=".NETFramework4.5.2" />
      <group targetFramework=".NETCoreApp3.1" />
    </dependencies>
  </metadata>
</package>