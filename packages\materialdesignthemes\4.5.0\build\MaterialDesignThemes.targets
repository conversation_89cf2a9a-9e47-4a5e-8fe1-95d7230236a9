<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <IncludeMaterialDesignFont Condition="$(IncludeMaterialDesignFont) == '' Or $(IncludeMaterialDesignFont) == '*Undefined*'">False</IncludeMaterialDesignFont>
    <MaterialDesignFontDirectory Condition="$(MaterialDesignFontDirectory) == '' Or $(MaterialDesignFontDirectory) == '*Undefined*'">Resources\Roboto\</MaterialDesignFontDirectory>
  </PropertyGroup>

  <ItemGroup Condition="'$(IncludeMaterialDesignFont)' == 'True'">
    <None Include="$(MSBuildThisFileDirectory)Resources\Roboto\*.ttf">
      <Link>$(MaterialDesignFontDirectory)%(FileName)%(Extension)</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
</Project>