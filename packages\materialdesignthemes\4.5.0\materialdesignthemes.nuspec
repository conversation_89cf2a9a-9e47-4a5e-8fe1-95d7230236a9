﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>MaterialDesignThemes</id>
    <version>4.5.0</version>
    <title>Material Design Themes XAML Resources</title>
    <authors><PERSON></authors>
    <owners><PERSON></owners>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>images\MaterialDesignThemes.Icon.png</icon>
    <projectUrl>https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit</projectUrl>
    <iconUrl>http://materialdesigninxaml.net/images/MD4XAML32.png</iconUrl>
    <description>ResourceDictionary instances containing Material Design templates and styles for WPF controls in .NET.</description>
    <releaseNotes>https://github.com/MaterialDesignInXAML/MaterialDesignInXamlToolkit/releases</releaseNotes>
    <copyright></copyright>
    <tags>WPF XAML Material Design Theme Colour Color UI UX</tags>
    <dependencies>
      <group targetFramework=".NETFramework4.5.2">
        <dependency id="MaterialDesignColors" version="[2.0.6, 3.0.0)" />
      </group>
      <group targetFramework=".NETCoreApp3.1">
        <dependency id="MaterialDesignColors" version="[2.0.6, 3.0.0)" />
      </group>
    </dependencies>
  </metadata>
</package>