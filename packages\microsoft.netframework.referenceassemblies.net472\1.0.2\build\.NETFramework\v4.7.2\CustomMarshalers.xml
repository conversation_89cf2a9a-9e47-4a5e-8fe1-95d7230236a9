﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>CustomMarshalers</name>
  </assembly>
  <members>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.CleanUpManagedData(System.Object)">
      <summary>Performs necessary cleanup of the managed data when it is no longer needed.</summary>
      <param name="pManagedObj">The managed object to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.CleanUpNativeData(System.IntPtr)">
      <summary>Performs necessary cleanup of the unmanaged data when it is no longer needed.</summary>
      <param name="pNativeData">A pointer to the unmanaged data to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.GetInstance(System.String)">
      <summary>Returns an instance of the custom marshaler.</summary>
      <param name="pstrCookie">String "cookie" parameter that can be used by the custom marshaler.</param>
      <returns>An instance of the custom marshaler.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.GetNativeDataSize">
      <summary>Returns the size in bytes of the unmanaged data to be marshaled.</summary>
      <returns>-1 to indicate the type this marshaler handles is not a value type.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.MarshalManagedToNative(System.Object)">
      <summary>Marshals an object from managed code to unmanaged code.</summary>
      <param name="pManagedObj">The managed object to be converted.</param>
      <returns>A pointer to the unmanaged object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pManagedObj" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler.MarshalNativeToManaged(System.IntPtr)">
      <summary>Marshals an object from unmanaged code to managed code.</summary>
      <param name="pNativeData">A pointer to the unmanaged object to be converted.</param>
      <returns>A managed object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pNativeData" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.CleanUpManagedData(System.Object)">
      <summary>Performs necessary cleanup of the managed data when it is no longer needed.</summary>
      <param name="pManagedObj">The managed object to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.CleanUpNativeData(System.IntPtr)">
      <summary>Performs necessary cleanup of the unmanaged data when it is no longer needed.</summary>
      <param name="pNativeData">A pointer to the unmanaged data to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.GetInstance(System.String)">
      <summary>Returns an instance of the custom marshaler.</summary>
      <param name="pstrCookie">String "cookie" parameter that can be used by the custom marshaler.</param>
      <returns>An instance of the custom marshaler.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.GetNativeDataSize">
      <summary>Returns the size in bytes of the unmanaged data to be marshaled.</summary>
      <returns>-1 to indicate the type this marshaler handles is not a value type.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.MarshalManagedToNative(System.Object)">
      <summary>Marshals an object from managed code to unmanaged code.</summary>
      <param name="pManagedObj">The managed object to be converted.</param>
      <returns>A pointer to the unmanaged object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pManagedObj" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler.MarshalNativeToManaged(System.IntPtr)">
      <summary>Marshals an object from unmanaged code to managed code.</summary>
      <param name="pNativeData">A pointer to the unmanaged object to be converted.</param>
      <returns>A managed object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pNativeData" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidCastException">The unmanaged object that <paramref name="pNativeData" /> points to could not be converted.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.CleanUpManagedData(System.Object)">
      <summary>Performs necessary cleanup of the managed data when it is no longer needed.</summary>
      <param name="pManagedObj">The managed object to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.CleanUpNativeData(System.IntPtr)">
      <summary>Performs necessary cleanup of the unmanaged data when it is no longer needed.</summary>
      <param name="pNativeData">A pointer to the unmanaged data to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.GetInstance(System.String)">
      <summary>Returns an instance of the custom marshaler.</summary>
      <param name="pstrCookie">String "cookie" parameter that can be used by the custom marshaler.</param>
      <returns>An instance of the custom marshaler.</returns>
      <exception cref="T:System.ArgumentException">
              <paramref name="pstrCookie" /> is invalid.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.GetNativeDataSize">
      <summary>Returns the size in bytes of the unmanaged data to be marshaled.</summary>
      <returns>-1 to indicate the type this marshaler handles is not a value type.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.MarshalManagedToNative(System.Object)">
      <summary>Marshals an object from managed code to unmanaged code.</summary>
      <param name="pManagedObj">The managed object to be converted.</param>
      <returns>A pointer to the unmanaged object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pManagedObj" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler.MarshalNativeToManaged(System.IntPtr)">
      <summary>Marshals an object from unmanaged code to managed code.</summary>
      <param name="pNativeData">A pointer to the unmanaged object to be converted.</param>
      <returns>A managed object. </returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pNativeData" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.CleanUpManagedData(System.Object)">
      <summary>Performs necessary cleanup of the managed data when it is no longer needed.</summary>
      <param name="pManagedObj">The managed object to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.CleanUpNativeData(System.IntPtr)">
      <summary>Performs necessary cleanup of the unmanaged data when it is no longer needed.</summary>
      <param name="pNativeData">A pointer to the unmanaged data to be destroyed.</param>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.GetInstance(System.String)">
      <summary>Returns an instance of the custom marshaler.</summary>
      <param name="pstrCookie">String "cookie" parameter that can be used by the custom marshaler.</param>
      <returns>An instance of the custom marshaler.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.GetNativeDataSize">
      <summary>Returns the size in bytes of the unmanaged data to be marshaled.</summary>
      <returns>-1 to indicate the type this marshaler handles is not a value type.</returns>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.MarshalManagedToNative(System.Object)">
      <summary>Marshals an object from managed code to unmanaged code.</summary>
      <param name="pManagedObj">The managed object to be converted.</param>
      <returns>A pointer to the unmanaged object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pManagedObj" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler.MarshalNativeToManaged(System.IntPtr)">
      <summary>Marshals an object from unmanaged code to managed code.</summary>
      <param name="pNativeData">A pointer to the unmanaged object to be converted.</param>
      <returns>A managed object.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="pNativeData" /> is <see langword="null" />.</exception>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomMarshalers.EnumerableToDispatchMarshaler">
      <summary>Marshals the COM <see langword="IDispatch" /> interface to the .NET Framework <see cref="T:System.Collections.IEnumerable" /> interface, and vice versa.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomMarshalers.EnumeratorToEnumVariantMarshaler">
      <summary>Marshals the COM <see langword="IEnumVARIANT" /> interface to the .NET Framework <see cref="T:System.Collections.IEnumerator" /> interface, and vice versa.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomMarshalers.ExpandoToDispatchExMarshaler">
      <summary>Marshals the COM <see langword="IDispatchEx" /> interface to either the .NET Framework <see cref="T:System.Runtime.InteropServices.Expando.IExpando" /> interface, or to the <see cref="T:System.Reflection.IReflect" /> interface, and vice versa.</summary>
    </member>
    <member name="T:System.Runtime.InteropServices.CustomMarshalers.TypeToTypeInfoMarshaler">
      <summary>Marshals the unmanaged <see langword="ITypeInfo" /> interface to the managed <see cref="T:System.Type" /> class, and marshals the managed <see cref="T:System.Type" /> class to the unmanaged <see langword="ITypeInfo" /> interface.</summary>
    </member>
  </members>
</doc>