﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.Build.Framework</name>
  </assembly>
  <members>
    <member name="E:Microsoft.Build.Framework.IEventSource.AnyEventRaised">
      <summary>Occurs when a build raises any other type of build event.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.BuildFinished">
      <summary>Occurs when a build finishes.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.BuildStarted">
      <summary>Occurs when a build starts.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.CustomEventRaised">
      <summary>Occurs when a build raises a custom event.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.ErrorRaised">
      <summary>Occurs when a build raises an error.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.MessageRaised">
      <summary>Occurs when a build raises a message.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.ProjectFinished">
      <summary>Occurs when a project finishes building.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.ProjectStarted">
      <summary>Occurs when a project starts building.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.StatusEventRaised">
      <summary>Occurs when a build raises a status event.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.TargetFinished">
      <summary>Occurs when a target finishes building.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.TargetStarted">
      <summary>Occurs when a target starts building.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.TaskFinished">
      <summary>Occurs when a task completes execution.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.TaskStarted">
      <summary>Occurs when a task begins execution.</summary>
    </member>
    <member name="E:Microsoft.Build.Framework.IEventSource.WarningRaised">
      <summary>Occurs when a build raises a warning.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidNodeId">
      <summary>The node ID of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidProjectContextId">
      <summary>The project ID of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidProjectInstanceId">
      <summary>The project instance ID of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidSubmissionId">
      <summary>The submission ID of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidTargetId">
      <summary>The name of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.BuildEventContext.InvalidTaskId">
      <summary>The task ID of the target the event was in when the event was triggered.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.ProjectStartedEventArgs.InvalidProjectId">
      <summary>A value representing an invalid project ID.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEngineResult.#ctor(System.Boolean,System.Collections.Generic.List{System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Framework.ITaskItem[]}})">
      <summary>Constructor that takes the result of the build and a list of the target outputs per project.</summary>
      <param name="result">The result of the build.</param>
      <param name="targetOutputsPerProject">The list of outputs per project.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildErrorEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildErrorEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildErrorEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildErrorEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.SubCategory" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.Code" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.File" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.LineNumber" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.ColumnNumber" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.EndLineNumber" />, <see cref="P:Microsoft.Build.Framework.BuildErrorEventArgs.EndColumnNumber" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> values.</summary>
      <param name="subcategory">The custom subcategory of the event.</param>
      <param name="code">The error code of the event.</param>
      <param name="file">The name of the file associated with the event.</param>
      <param name="lineNumber">The line number that corresponds to the beginning of the section of code that raised the event.</param>
      <param name="columnNumber">The column number that corresponds to the beginning of the section of code that raised the event.</param>
      <param name="endLineNumber">The line number that corresponds to the end of the section of code that raised the event.</param>
      <param name="endColumnNumber">The column number that corresponds to the end of the section of code that raised the event.</param>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildErrorEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime)">
      <summary>This constructor which allows a timestamp to be set</summary>
      <param name="subcategory">event sub-category</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">Timestamp when event was created</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildErrorEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>This constructor which allows a timestamp to be set</summary>
      <param name="subcategory">event sub-category</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">Timestamp when event was created</param>
      <param name="messageArgs">message arguments</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventArgs.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventArgs.#ctor(System.String,System.String,System.String,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildEventArgs" /> class.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="eventTimestamp">The date and time to associate with the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildEventContext" /> class.</summary>
      <param name="nodeId">The node ID of the build event.</param>
      <param name="targetId">The target ID of the build event.</param>
      <param name="projectContextId">The project ID of the build event.</param>
      <param name="taskId">The task ID of the build event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Constructs a BuildEventContext with a specified project instance id.</summary>
      <param name="nodeId">The node ID of the build event.</param>
      <param name="projectInstanceId">The project instance ID of the build event.</param>
      <param name="projectContextId">The project ID of the build event.</param>
      <param name="targetId">The target ID of the build event.</param>
      <param name="taskId">The task ID of the build event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.#ctor(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Constructs a BuildEventContext with a specific submission id</summary>
      <param name="submissionId">The submission ID of the build event.</param>
      <param name="nodeId">The node ID of the build event.</param>
      <param name="projectInstanceId">The project instance ID of the build event.</param>
      <param name="projectContextId">The project ID of the build event.</param>
      <param name="targetId">The target ID of the build event.</param>
      <param name="taskId">The task ID of the build event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.Equals(System.Object)">
      <summary>Gets whether the references are the same.</summary>
      <param name="obj">The reference to compare.</param>
      <returns>
          <see langword="true" /> if the references are equal, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.GetHashCode">
      <summary>Gets a code comprised of the <see cref="P:Microsoft.Build.Framework.BuildEventContext.ProjectContextId" /> and the <see cref="P:Microsoft.Build.Framework.BuildEventContext.NodeId" />.</summary>
      <returns>An integer value representing the hash code.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.op_Equality(Microsoft.Build.Framework.BuildEventContext,Microsoft.Build.Framework.BuildEventContext)">
      <summary>Override == so the equals comparison using this operator will be the same as .Equals</summary>
      <param name="left">Left hand side operand</param>
      <param name="right">Right hand side operand</param>
      <returns>True if the object values are identical, false if they are not identical</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildEventContext.op_Inequality(Microsoft.Build.Framework.BuildEventContext,Microsoft.Build.Framework.BuildEventContext)">
      <summary>Override != so the equals comparison using this operator will be the same as ! Equals</summary>
      <param name="left">Left hand side operand</param>
      <param name="right">Right hand side operand</param>
      <returns>True if the object values are not identical, false if they are identical</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildFinishedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildFinishedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildFinishedEventArgs.#ctor(System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildFinishedEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildFinishedEventArgs.Succeeded" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the build succeeded; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildFinishedEventArgs.#ctor(System.String,System.String,System.Boolean,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildFinishedEventArgs" /> class.</summary>
      <param name="message">Text message.</param>
      <param name="helpKeyword">Help keyword.</param>
      <param name="succeeded">True if the event succeeded.</param>
      <param name="eventTimestamp">Message for the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildFinishedEventArgs.#ctor(System.String,System.String,System.Boolean,System.DateTime,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildFinishedEventArgs" /> class.</summary>
      <param name="message">Text message.</param>
      <param name="helpKeyword">Help keyword.</param>
      <param name="succeeded">True if the event succeeded.</param>
      <param name="eventTimestamp">Date and time associated with the event.</param>
      <param name="messageArgs">Message for the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildMessageEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildMessageEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" />, and <see cref="P:Microsoft.Build.Framework.BuildMessageEventArgs.Importance" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="importance">A <see cref="T:Microsoft.Build.Framework.MessageImportance" /> value indicating the importance of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance,System.DateTime)">
      <summary>Initializes the <see cref="T:Microsoft.Build.Framework.BuildMessageEventArgs" /> object.</summary>
      <param name="message">The message text.</param>
      <param name="helpKeyword">The keyword to link to a help topic.</param>
      <param name="senderName">The name of the event sender.</param>
      <param name="importance">The importance of the message</param>
      <param name="eventTimestamp">The data and time associated with the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance,System.DateTime,System.Object[])">
      <summary>Initializes the <see cref="T:Microsoft.Build.Framework.BuildMessageEventArgs" /> object.</summary>
      <param name="message">The message text.</param>
      <param name="helpKeyword">The keyword to link to a help topic.</param>
      <param name="senderName">The name of the event sender.</param>
      <param name="importance">The importance of the message</param>
      <param name="eventTimestamp">The data and time associated with the event.</param>
      <param name="messageArgs">The message arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance)">
      <summary>This constructor allows all event data to be initialized</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="importance">importance of the message</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance,System.DateTime)">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="importance">importance of the message</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,Microsoft.Build.Framework.MessageImportance,System.DateTime,System.Object[])">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="importance">importance of the message</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
      <param name="messageArgs">message arguments</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStartedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStartedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStartedEventArgs.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStartedEventArgs" /> class with the specified message and Help keyword.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStartedEventArgs.#ctor(System.String,System.String,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Constructor to initialize all parameters.</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="environmentOfBuild">A dictionary which lists the environment of the build when the build is started.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStartedEventArgs.#ctor(System.String,System.String,System.DateTime)">
      <summary>Constructor to allow timestamp to be set</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="eventTimestamp">Timestamp when the event was created</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStartedEventArgs.#ctor(System.String,System.String,System.DateTime,System.Object[])">
      <summary>Constructor to allow timestamp to be set</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="eventTimestamp">Timestamp when the event was created</param>
      <param name="messageArgs">message args</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStatusEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStatusEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStatusEventArgs.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStatusEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStatusEventArgs.#ctor(System.String,System.String,System.String,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStatusEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.TimeStamp" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="eventTimestamp">The time the event was raised.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildStatusEventArgs.#ctor(System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildStatusEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.TimeStamp" /> and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="eventTimestamp">The time the event was raised.</param>
      <param name="messageArgs">The event message.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildWarningEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildWarningEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildWarningEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.BuildWarningEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.SubCategory" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.Code" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.File" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.LineNumber" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.ColumnNumber" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.EndLineNumber" />, <see cref="P:Microsoft.Build.Framework.BuildWarningEventArgs.EndColumnNumber" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> values.</summary>
      <param name="subcategory">The custom subcategory of the event.</param>
      <param name="code">The warning code of the event.</param>
      <param name="file">The name of the file associated with the event.</param>
      <param name="lineNumber">The line number that corresponds to the beginning of the section of code that raised the event.</param>
      <param name="columnNumber">The column number that corresponds to the beginning of the section of code that raised the event.</param>
      <param name="endLineNumber">The line number that corresponds to the end of the section of code that raised the event.</param>
      <param name="endColumnNumber">The column number that corresponds to the end of the section of code that raised the event.</param>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildWarningEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime)">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
    </member>
    <member name="M:Microsoft.Build.Framework.BuildWarningEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
      <param name="messageArgs">message arguments</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CriticalBuildMessageEventArgs.#ctor">
      <summary>Default constructor</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.CriticalBuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String)">
      <summary>This constructor allows all event data to be initialized</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CriticalBuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime)">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CriticalBuildMessageEventArgs.#ctor(System.String,System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>This constructor allows timestamp to be set</summary>
      <param name="subcategory">event subcategory</param>
      <param name="code">event code</param>
      <param name="file">file associated with the event</param>
      <param name="lineNumber">line number (0 if not applicable)</param>
      <param name="columnNumber">column number (0 if not applicable)</param>
      <param name="endLineNumber">end line number (0 if not applicable)</param>
      <param name="endColumnNumber">end column number (0 if not applicable)</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="senderName">name of event sender</param>
      <param name="eventTimestamp">custom timestamp for the event</param>
      <param name="messageArgs">message arguments</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CustomBuildEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.CustomBuildEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.CustomBuildEventArgs.#ctor(System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.CustomBuildEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, and <see cref="P:Microsoft.Build.Framework.BuildEventArgs.SenderName" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CustomBuildEventArgs.#ctor(System.String,System.String,System.String,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.CustomBuildEventArgs" /> class.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="eventTimestamp">The date and time to associate with the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.CustomBuildEventArgs.#ctor(System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.CustomBuildEventArgs" /> class.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="eventTimestamp">The date and time to associate with the event.</param>
      <param name="messageArgs">The arguments for the message.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs" /> class, with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate that the project built successfully; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.Boolean,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs" /> class, with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate that the project built successfully; otherwise false.</param>
      <param name="eventTimestamp">The date and time the event was created.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectStartedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectStartedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs" /> class, with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="targetNames">The names of the targets to build in the external project. This can be a null reference (<see langword="Nothing" /> in Visual Basic) if the external project is being built with the default targets.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ExternalProjectStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs" /> class, with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="senderName">The source of the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="targetNames">The names of the targets to build in the external project. This can be a null reference (<see langword="Nothing" /> in Visual Basic) if the external project is being built with the default targets.</param>
      <param name="eventTimestamp">The date and time to associate with the event.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine.BuildProjectFile(System.String,System.String[],System.Collections.IDictionary,System.Collections.IDictionary)">
      <summary>Initiates a build of a project file. If the build is successful, the outputs, if any, of the specified targets are returned.</summary>
      <param name="projectFileName">The name of the project file to build.</param>
      <param name="targetNames">The names of the target in the project to build. Separate multiple targets with a semicolon (;).</param>
      <param name="globalProperties">An <see cref="T:System.Collections.IDictionary" /> of additional global properties to apply to the project. The key and value must be <see langword="String" /> data types.</param>
      <param name="targetOutputs">The outputs of each specified target.</param>
      <returns>
          <see langword="true" /> if the build was successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine.LogCustomEvent(Microsoft.Build.Framework.CustomBuildEventArgs)">
      <summary>Raises a custom event to all registered loggers.</summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine.LogErrorEvent(Microsoft.Build.Framework.BuildErrorEventArgs)">
      <summary>Raises an error event to all registered loggers.</summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine.LogMessageEvent(Microsoft.Build.Framework.BuildMessageEventArgs)">
      <summary>Raises a message event to all registered loggers.</summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine.LogWarningEvent(Microsoft.Build.Framework.BuildWarningEventArgs)">
      <summary>Raises a warning event to all registered loggers.</summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine2.BuildProjectFile(System.String,System.String[],System.Collections.IDictionary,System.Collections.IDictionary,System.String)">
      <summary>Initiates a build of a project file. If the build is successful, the outputs of the specified targets are returned, if any exist.</summary>
      <param name="projectFileName">The name of the project file to build.</param>
      <param name="targetNames">The names of the target in the project to build. Separate multiple targets with a semicolon (;). <see langword="Null" /> values are acceptable.</param>
      <param name="globalProperties">An <see cref="T:System.Collections.IDictionary" /> of additional global properties to apply to the project. The key and value must be <see langword="String" /> data types.</param>
      <param name="targetOutputs">The outputs of each specified target. <see langword="Null" /> values are acceptable.</param>
      <param name="toolsVersion">Default value = "3.5." Specifies the Toolset version for the build to use. <see langword="Null" /> values are acceptable.</param>
      <returns>
          <see langword="true" /> if the build was successful; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine2.BuildProjectFilesInParallel(System.String[],System.String[],System.Collections.IDictionary[],System.Collections.IDictionary[],System.String[],System.Boolean,System.Boolean)">
      <summary>Allows tasks to initiate parallel builds of the specified project files on systems with multiple processors or multiple core processors. If the builds are successful, the outputs of the specified targets are returned, if any exist.</summary>
      <param name="projectFileNames">The names of the project files to build.</param>
      <param name="targetNames">The names of the targets in the project to build. Separate multiple targets with a semicolon (;). <see langword="Null" /> values are acceptable.</param>
      <param name="globalProperties">An <see cref="T:System.Collections.IDictionary" /> of additional global properties to apply to the project. The key and value must be <see langword="String" /> data types. Array entries can be <see langword="null" />.</param>
      <param name="targetOutputsPerProject">The outputs of each specified target. <see langword="Null" /> values are acceptable.</param>
      <param name="toolsVersion">Default value = "3.5." Specifies the Toolset version for the build to use. <see langword="Null" /> values are acceptable.</param>
      <param name="useResultsCache">If <see langword="true" />, the operation runs only if the cache does not already contain the result. After the operation the result is stored in the cache.</param>
      <param name="unloadProjectsOnCompletion">If <see langword="true" /> the project is unloaded once the operation completes.</param>
      <returns>
          <see langword="True" /> if the build succeeded, <see langword="false" /> if otherwise.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine3.BuildProjectFilesInParallel(System.String[],System.String[],System.Collections.IDictionary[],System.Collections.Generic.IList{System.String}[],System.String[],System.Boolean)">
      <summary>Allows tasks to initiate a build on a project file</summary>
      <param name="projectFileNames">The project to build.</param>
      <param name="targetNames">The targets in the project to build (can be null).</param>
      <param name="globalProperties">An array of <see cref="T:System.Collections.IDictionary" /> of additional global properties to apply to the child project (array entries can be null). The keys and should both be strings.</param>
      <param name="removeGlobalProperties">A list of global properties which should be removed.</param>
      <param name="toolsVersion">A tools version recognized by the Engine that will be used during this build (can be null).</param>
      <param name="returnTargetOutputs">Should the target outputs be returned in the BuildEngineResult.</param>
      <returns>If the build is successful, the outputs of the specified targets are returned.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine3.Reacquire">
      <summary>Waits to reacquire control after yielding.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine3.Yield">
      <summary>Informs the system that this task has a long-running out-of-process component and other work can be done in the build while that work completes.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine4.GetRegisteredTaskObject(System.Object,Microsoft.Build.Framework.RegisteredTaskObjectLifetime)">
      <summary>Retrieves a previously registered task object stored with the specified key.</summary>
      <param name="key">The key used to retrieve the object.</param>
      <param name="lifetime">The lifetime of the object.</param>
      <returns>Returns a registered object, or null if there is no object registered under that key or if the object has been discarded through early collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine4.RegisterTaskObject(System.Object,System.Object,Microsoft.Build.Framework.RegisteredTaskObjectLifetime,System.Boolean)">
      <summary>Registers an object with the system that will be disposed of at some specified time in the future.</summary>
      <param name="key">The key used to retrieve the object.</param>
      <param name="obj">The object to be held for later disposal.</param>
      <param name="lifetime">The lifetime of the object.</param>
      <param name="allowEarlyCollection">Flag that determines whether the object can be disposed earlier than the requested time.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IBuildEngine4.UnregisterTaskObject(System.Object,Microsoft.Build.Framework.RegisteredTaskObjectLifetime)">
      <summary>Unregisters a previously registered task object.</summary>
      <param name="key">The key used to retrieve the object.</param>
      <param name="lifetime">The lifetime of the object.</param>
      <returns>Returns a previously registered object, or null if there is no object registered under that key or if the object has been discarded through early collection.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ICancelableTask.Cancel">
      <summary>Instructs the task to exit as soon as possible, or to immediately exit if Execute is invoked after this method.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.IEventRedirector.ForwardEvent(Microsoft.Build.Framework.BuildEventArgs)">
      <summary>Called by the node loggers to forward build events to the central logger.</summary>
      <param name="buildEvent">The <see cref="T:Microsoft.Build.Framework.BuildEventArgs" /> to forward.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.IGeneratedTask.GetPropertyValue(Microsoft.Build.Framework.TaskPropertyInfo)">
      <summary>Gets the property value.</summary>
      <param name="property">The name of the property.</param>
      <returns>The value of the named property.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.IGeneratedTask.SetPropertyValue(Microsoft.Build.Framework.TaskPropertyInfo,System.Object)">
      <summary>Sets a property value.</summary>
      <param name="property">The name of the property.</param>
      <param name="value">The value to use.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ILogger.Initialize(Microsoft.Build.Framework.IEventSource)">
      <summary>Subscribes loggers to specific events. This method is called when the logger is registered with the build engine, before any events are raised.</summary>
      <param name="eventSource">The events available to loggers.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ILogger.Shutdown">
      <summary>Releases the resources allocated to the logger at the time of initialization or during the build. This method is called when the logger is unregistered from the engine, after all events are raised. A host of MSBuild typically unregisters loggers immediately before quitting.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.INodeLogger.Initialize(Microsoft.Build.Framework.IEventSource,System.Int32)">
      <summary>Initializes the logger.</summary>
      <param name="eventSource">An <see cref="T:Microsoft.Build.Framework.IEventSource" /> object representing the build event source.</param>
      <param name="nodeCount">The number of CPUs.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ITask.Execute">
      <summary>Executes a task.</summary>
      <returns>
          <see langword="true" /> if the task executed successfully; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory.CleanupTask(Microsoft.Build.Framework.ITask)">
      <summary>Cleans up any context or state that may have been built up for a given task.</summary>
      <param name="task">The task to clean up.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory.CreateTask(Microsoft.Build.Framework.IBuildEngine)">
      <summary>Creates an instance of the task to be used in a batch. The task factory logging host will log messages in the context of the task.</summary>
      <param name="taskFactoryLoggingHost">The logging host for the task factory.</param>
      <returns>Returns an instance of the task to be used in a batch.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory.GetTaskParameters">
      <summary>Get the descriptions for all the task's parameters.</summary>
      <returns>A non-null array of property descriptions.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory.Initialize(System.String,System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Framework.TaskPropertyInfo},System.String,Microsoft.Build.Framework.IBuildEngine)">
      <summary>Initializes this factory for instantiating tasks with a particular inline task block.</summary>
      <param name="taskName">Name of the task.</param>
      <param name="parameterGroup">The parameter group.</param>
      <param name="taskBody">The task body.</param>
      <param name="taskFactoryLoggingHost">The task factory logging host.</param>
      <returns>A value indicating whether initialization was successful.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory2.CreateTask(Microsoft.Build.Framework.IBuildEngine,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Create an instance of the task to be used, with an optional set of "special" parameters set on the individual task invocation using the MSBuildRuntime and MSBuildArchitecture default task parameters. MSBuild provides an implementation of this interface, TaskHostFactory, that uses "MSBuildRuntime", with values "CLR2", "CLR4", "CurrentRuntime", and "*" (Any); and "MSBuildArchitecture", with values "x86", "x64", "CurrentArchitecture", and "*" (Any). An implementer of ITaskFactory2 can choose to use these pre-defined MSBuildRuntime and MSBuildArchitecture values, or can specify new values for these parameters.</summary>
      <param name="taskFactoryLoggingHost">The task factory logging host will log messages in the context of the task.</param>
      <param name="taskIdentityParameters">Special parameters that the task factory can use to modify how it executes tasks, such as Runtime and Architecture. </param>
      <returns>The generated task, or null if the task failed to be created.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskFactory2.Initialize(System.String,System.Collections.Generic.IDictionary{System.String,System.String},System.Collections.Generic.IDictionary{System.String,Microsoft.Build.Framework.TaskPropertyInfo},System.String,Microsoft.Build.Framework.IBuildEngine)">
      <summary>Initializes this factory for instantiating tasks with a particular inline task block and a set of UsingTask parameters. MSBuild provides an implementation of this interface, TaskHostFactory, that uses "Runtime", with values "CLR2", "CLR4", "CurrentRuntime", and "*" (Any); and "Architecture", with values "x86", "x64", "CurrentArchitecture", and "*" (Any). An implementer of ITaskFactory2 can choose to use these pre-defined Runtime and Architecture values, or can specify new values for these parameters.</summary>
      <param name="taskName">Name of the task.</param>
      <param name="factoryIdentityParameters">Special parameters that the task factory can use to modify how it executes tasks, such as Runtime and Architecture. The key is the name of the parameter and the value is the parameter's value. This is the set of parameters that was set on the UsingTask using e.g. the UsingTask Runtime and Architecture parameters.</param>
      <param name="parameterGroup">The parameter group.</param>
      <param name="taskBody">The task body.</param>
      <param name="taskFactoryLoggingHost">The task factory logging host.</param>
      <returns>A value indicating whether initialization was successful.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem.CloneCustomMetadata">
      <summary>Gets the collection of custom metadata.</summary>
      <returns>The collection of custom metadata.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem.CopyMetadataTo(Microsoft.Build.Framework.ITaskItem)">
      <summary>Copies the custom metadata entries to another item.</summary>
      <param name="destinationItem">The item to copy the metadata entries to.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem.GetMetadata(System.String)">
      <summary>Gets the value of the specified metadata entry.</summary>
      <param name="metadataName">The name of the metadata entry.</param>
      <returns>The value of the <paramref name="attributeName" /> metadata.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem.RemoveMetadata(System.String)">
      <summary>Removes the specified metadata entry from the item.</summary>
      <param name="metadataName">The name of the metadata entry to remove.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem.SetMetadata(System.String,System.String)">
      <summary>Adds or changes a custom metadata entry to the item.</summary>
      <param name="metadataName">The name of the metadata entry.</param>
      <param name="metadataValue">The value of the metadata entry.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem2.CloneCustomMetadataEscaped">
      <summary>ITaskItem2 implementation which returns a clone of the metadata on this object.  Values returned are in their original escaped form.</summary>
      <returns>Returns the cloned metadata, with values' escaping preserved.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem2.GetMetadataValueEscaped(System.String)">
      <summary>Allows the values of metadata on the item to be queried.</summary>
      <param name="metadataName">The metadata to be queried.</param>
      <returns>Returns the escaped metadata value.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.ITaskItem2.SetMetadataValueLiteral(System.String,System.String)">
      <summary>Allows a piece of custom metadata to be set on the item. Assumes that the value passed in is unescaped, and escapes the value as necessary in order to maintain its value.</summary>
      <param name="metadataName">The metadata name.</param>
      <param name="metadataValue">The metadata value.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LazyFormattedBuildEventArgs.#ctor">
      <summary>Default constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.LazyFormattedBuildEventArgs.#ctor(System.String,System.String,System.String)">
      <summary>This constuctor allows all event data to be initialized.</summary>
      <param name="message">text message.</param>
      <param name="helpKeyword">help keyword.</param>
      <param name="senderName">name of event sender.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LazyFormattedBuildEventArgs.#ctor(System.String,System.String,System.String,System.DateTime,System.Object[])">
      <summary>This constuctor that allows message arguments that are lazily formatted.</summary>
      <param name="message">text message.</param>
      <param name="helpKeyword">help keyword.</param>
      <param name="senderName">name of event sender.</param>
      <param name="eventTimestamp">Timestamp when event was created.</param>
      <param name="messageArgs">Message arguments.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LoadInSeparateAppDomainAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoadInSeparateAppDomainAttribute" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoggerException" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoggerException" /> class with the values specified.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoggerException" /> class with the specified values.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoggerException" /> class with the values specified.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerException">The exception that is the cause of the current excpetion. If the <paramref name="innerException" /> parameter is not a null reference (<see langword="Nothing" /> in Visual Basic), the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.#ctor(System.String,System.Exception,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.LoggerException" /> class with the values specified.</summary>
      <param name="message">The error message that explains the reason for the exception.</param>
      <param name="innerException">The exception that is the cause of the current excpetion. If the <paramref name="innerException" /> parameter is not a null reference (<see langword="Nothing" /> in Visual Basic), the current exception is raised in a <see langword="catch" /> block that handles the inner exception.</param>
      <param name="errorCode">The error code associated with the exception's message.</param>
      <param name="helpKeyword">The Help keyword for the error.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.LoggerException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo" /> with information about the exception.</summary>
      <param name="info">The <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that holds the serialized object data about the exception being thrown.</param>
      <param name="context">The <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains contextual information about the source or destination.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.OutputAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.OutputAttribute" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectFinishedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectFinishedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectFinishedEventArgs.#ctor(System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectFinishedEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, <see cref="P:Microsoft.Build.Framework.ProjectFinishedEventArgs.ProjectFile" />, and <see cref="P:Microsoft.Build.Framework.ProjectFinishedEventArgs.Succeeded" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file associated with the event.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the build succeeded; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectFinishedEventArgs.#ctor(System.String,System.String,System.String,System.Boolean,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectFinishedEventArgs" /> class.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file associated with the event.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the build succeeded; otherwise, false.</param>
      <param name="eventTimestamp">The date and time the event was created.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectStartedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor(System.Int32,System.String,System.String,System.String,System.String,System.Collections.IEnumerable,System.Collections.IEnumerable,Microsoft.Build.Framework.BuildEventContext)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectStartedEventArgs" /> class with the specified values.</summary>
      <param name="projectId">The unique <see cref="P:Microsoft.Build.Framework.ProjectStartedEventArgs.ProjectId" /> identifier for the project.</param>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="targetNames">The targets to be built.</param>
      <param name="properties">A list of properties.</param>
      <param name="items">A list of items.</param>
      <param name="parentBuildEventContext">A <see cref="T:Microsoft.Build.Framework.BuildEventContext" /> value that defines location contextual information. This is attached to logging events to define where they are located in relation to the process, engine, project, target, or task that is executing.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor(System.Int32,System.String,System.String,System.String,System.String,System.Collections.IEnumerable,System.Collections.IEnumerable,Microsoft.Build.Framework.BuildEventContext,System.Collections.Generic.IDictionary{System.String,System.String},System.String)">
      <summary>This constructor allows event data to be initialized. Sender is assumed to be "MSBuild".</summary>
      <param name="projectId">project id</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="projectFile">project name</param>
      <param name="targetNames">targets we are going to build (empty indicates default targets)</param>
      <param name="properties">list of properties</param>
      <param name="items">list of items</param>
      <param name="parentBuildEventContext">event context info for the parent project</param>
      <param name="globalProperties">A list of global properties</param>
      <param name="toolsVersion">The tools version</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor(System.Int32,System.String,System.String,System.String,System.String,System.Collections.IEnumerable,System.Collections.IEnumerable,Microsoft.Build.Framework.BuildEventContext,System.DateTime)">
      <summary>This constructor allows event data to be initialized. Sender is assumed to be "MSBuild".</summary>
      <param name="projectId">project id</param>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="projectFile">project name</param>
      <param name="targetNames">targets we are going to build (empty indicates default targets)</param>
      <param name="properties">list of properties</param>
      <param name="items">list of items</param>
      <param name="parentBuildEventContext">event context info for the parent project</param>
      <param name="eventTimestamp">time the event occurred</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.Collections.IEnumerable,System.Collections.IEnumerable)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.ProjectStartedEventArgs" /> class with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="targetNames">The targets to be built.</param>
      <param name="properties">A list of properties</param>
      <param name="items">A list of items.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.ProjectStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.Collections.IEnumerable,System.Collections.IEnumerable,System.DateTime)">
      <summary>This constructor allows event data to be initialized. Also the timestamp can be set Sender is assumed to be "MSBuild".</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="projectFile">project name</param>
      <param name="targetNames">targets we are going to build (empty indicates default targets)</param>
      <param name="properties">list of properties</param>
      <param name="items">list of items</param>
      <param name="eventTimestamp">time the event occurred</param>
    </member>
    <member name="M:Microsoft.Build.Framework.RequiredAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.RequiredAttribute" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.RequiredRuntimeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.RequiredRuntimeAttribute" /> class.</summary>
      <param name="runtimeVersion">The version of the CLR runtime required prefaced with a "v," such as "v2.0."</param>
    </member>
    <member name="M:Microsoft.Build.Framework.RunInMTAAttribute.#ctor">
      <summary>Default constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.RunInSTAAttribute.#ctor">
      <summary>Default constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetFinishedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TargetFinishedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TargetFinishedEventArgs" /> class with the specified <see cref="P:Microsoft.Build.Framework.BuildEventArgs.Message" />, <see cref="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword" />, <see cref="P:Microsoft.Build.Framework.TargetFinishedEventArgs.TargetName" />, <see cref="P:Microsoft.Build.Framework.TargetFinishedEventArgs.ProjectFile" />, and <see cref="P:Microsoft.Build.Framework.TargetFinishedEventArgs.Succeeded" /> values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="targetName">The name of the target associated with the event.</param>
      <param name="projectFile">The name of the project file associated with the event.</param>
      <param name="targetFile">The name of the target file.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the build succeeded; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.Boolean,System.Collections.IEnumerable)">
      <summary>This constructor allows event data to be initialized. Sender is assumed to be "MSBuild".</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="targetName">target name</param>
      <param name="projectFile">project file</param>
      <param name="targetFile">file in which the target is defined</param>
      <param name="succeeded">true if target built successfully</param>
      <param name="targetOutputs">Target output items for the target. If batching will be null for everything except for the last target in the batch</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.Boolean,System.DateTime,System.Collections.IEnumerable)">
      <summary>This constructor allows event data to be initialized including the timestamp when the event was created. Sender is assumed to be "MSBuild".</summary>
      <param name="message">text message</param>
      <param name="helpKeyword">help keyword </param>
      <param name="targetName">target name</param>
      <param name="projectFile">project file</param>
      <param name="targetFile">file in which the target is defined</param>
      <param name="succeeded">true if target built successfully</param>
      <param name="eventTimestamp">Timestamp when the event was created</param>
      <param name="targetOutputs">Target output items for the target. If batching will be null for everything except for the last target in the batch</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetStartedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TargetStartedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String)">
      <summary>Initalizes a new instance of the <see cref="T:Microsoft.Build.Framework.TargetStartedEventArgs" /> class with the valudes provided.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="targetName">The name of the target associated with the event.</param>
      <param name="projectFile">The name of the project file associated with the event.</param>
      <param name="targetFile">The name of the target file.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TargetStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.DateTime)">
      <summary>Represents the arguments for target started events.</summary>
      <param name="message">The text message.</param>
      <param name="helpKeyword">The help keyword.</param>
      <param name="targetName">The name of the target event.</param>
      <param name="projectFile">The project file.</param>
      <param name="targetFile">The target file.</param>
      <param name="parentTarget">The parent target.</param>
      <param name="eventTimestamp">The date and time the event was created.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskCommandLineEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskCommandLineEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskCommandLineEventArgs.#ctor(System.String,System.String,Microsoft.Build.Framework.MessageImportance)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskCommandLineEventArgs" /> class with the specified command line and <see cref="T:Microsoft.Build.Framework.MessageImportance" />.</summary>
      <param name="commandLine">The command line used by the task to run the underlying program.</param>
      <param name="taskName">The name of the task raising the event.</param>
      <param name="importance">The <see cref="T:Microsoft.Build.Framework.MessageImportance" /> of the message.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskCommandLineEventArgs.#ctor(System.String,System.String,Microsoft.Build.Framework.MessageImportance,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskCommandLineEventArgs" /> class with the specified command line, task name and <see cref="T:Microsoft.Build.Framework.MessageImportance" />.</summary>
      <param name="commandLine">The command line used by the task to run the underlying program.</param>
      <param name="taskName">The name of the task raising the event.</param>
      <param name="importance">The <see cref="T:Microsoft.Build.Framework.MessageImportance" /> of the message.</param>
      <param name="eventTimestamp">The time the event occurred.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskFinishedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskFinishedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskFinishedEventArgs" /> class with the specified values.</summary>
      <param name="message">The text for the message.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="taskFile">The file in which the task is defined.</param>
      <param name="taskName">The name of the task.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the task succeeded; otherwise, <see langword="false." /></param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskFinishedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.Boolean,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskFinishedEventArgs" /> class with the specified values.</summary>
      <param name="message">The text for the message.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The project file name.</param>
      <param name="taskFile">The file in which the task is defined.</param>
      <param name="taskName">The name of the task.</param>
      <param name="succeeded">
            <see langword="true" /> to indicate the task succeeded; otherwise, <see langword="false." /></param>
      <param name="eventTimestamp">When the event finished.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskPropertyInfo.#ctor(System.String,System.Type,System.Boolean,System.Boolean)">
      <summary>Encapsulates a list of parameters declared in the task.</summary>
      <param name="name">The name of the parameter</param>
      <param name="typeOfParameter">The type of the parameter</param>
      <param name="output">True if the parameter is both an output and an input parameter. False if the parameter is only an input parameter.</param>
      <param name="required">True if the parameter must be supplied to each invocation of the task.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskStartedEventArgs.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskStartedEventArgs" /> class.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskStartedEventArgs" /> class with the specified values.</summary>
      <param name="message">The text of the event.</param>
      <param name="helpKeyword">The Help keyword associated with the event.</param>
      <param name="projectFile">The name of the project file associated with the event.</param>
      <param name="taskFile">The file in which the task is defined.</param>
      <param name="taskName">The name of the task.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.TaskStartedEventArgs.#ctor(System.String,System.String,System.String,System.String,System.String,System.DateTime)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TaskStartedEventArgs" /> class.</summary>
      <param name="message">The text message about the event.</param>
      <param name="helpKeyword">The help keyword.</param>
      <param name="projectFile">The project file.</param>
      <param name="taskFile">The file that defines the task.</param>
      <param name="taskName">The name of the task.</param>
      <param name="eventTimestamp">The date and time the task was created.</param>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Argument.#ctor">
      <summary>Default constructor required for XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Argument.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Argument.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.BaseProperty.#ctor">
      <summary>Default constructor. Required for deserializtion from a persisted format.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.BaseProperty.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.BaseProperty.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.BoolProperty.#ctor">
      <summary>The default constructor for a Boolean property.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Category.#ctor">
      <summary>Default constructor. Called during deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Category.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Category.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.CategorySchema.#ctor">
      <summary>The default constructor for a category schema.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.#ctor">
      <summary>Constructor</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.BeginInit">
      <summary>Begins initialization. See ISupportInitialize.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.EndInit">
      <summary>Ends initialization.  See ISupportInitialize.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.GetMetadata(System.String)">
      <summary>Gets the value of the given metadata name.</summary>
      <param name="metadataName">The name of the metadata whose value is to be retrieved.</param>
      <returns>Returns the value of the given metadata name. Returns null if the name isn't found in the metadata.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ContentType.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.DataSource.#ctor">
      <summary>Default constructor. Required for correct XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.DataSource.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.DataSource.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.DynamicEnumProperty.#ctor">
      <summary>The default constructor for a dynamic enum property.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.EnumProperty.#ctor">
      <summary>The default enum property constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.EnumProperty.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.EnumValue.#ctor">
      <summary>Default constructor. Required for XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.FileExtension.#ctor">
      <summary>Constructor</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.FileExtension.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.FileExtension.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.IntProperty.#ctor">
      <summary>The default constructor for an integer property.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.IntProperty.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.IProjectSchemaNode.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.IProjectSchemaNode.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ItemType.#ctor">
      <summary>Constructor.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ItemType.BeginInit">
      <summary>Begins initialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ItemType.EndInit">
      <summary>Ends initialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ItemType.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ItemType.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.NameValuePair.#ctor">
      <summary>Default constructor of a name-value pair.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ProjectSchemaDefinitions.#ctor">
      <summary>The default constructor of a schema definition.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ProjectSchemaDefinitions.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if there are no objects of the given type provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ProjectSchemaDefinitions.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.#ctor">
      <summary>Default constructor. Required for deserialization from a persisted format.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.GetPropertiesByCategory">
      <summary>Returns a dictionary of all properties partitioned into categories. </summary>
      <returns>Returns a dictionary whose keys are the <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" /> names and whose values are the list of properties in that category. The returned value is never <see langword="null" />.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.GetPropertiesInCategory(System.String)">
      <summary>Returns the list of properties in a <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />. </summary>
      <param name="categoryName">The name of the category whose properties are to be returned.</param>
      <returns>Returns the list of properties in a <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />. Returns <see langword="null" /> if this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> does not contain this category.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.GetProperty(System.String)">
      <summary>Gets the property with the specified name.</summary>
      <param name="propertyName">The name of the property.</param>
      <returns>Returns the property with the specified name, or null if no property with a matching name is found. </returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.Rule.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleBag.#ctor">
      <summary>Default constructor. Required for XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleBag.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleBag.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleBag.GetSchemaObjects(System.Type)">
      <summary>Returns an enumerator over all instances of static data that have the given type. </summary>
      <param name="type">The type of static data to retrieve.</param>
      <returns>Returns an enumerator over all instances of static data that have the given type. Returns <see langword="Null" /> or Empty list if no objects of the given type are provided by this node.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleBag.GetSchemaObjectTypes">
      <summary>Returns an enumerator over all types of static data for the data-driven features that this node contains.</summary>
      <returns>Returns an enumerator over all types of static data for the data-driven features that this node contains.</returns>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.RuleSchema.#ctor">
      <summary>The default constructor of a rule schema.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.StringListProperty.#ctor">
      <summary>Default constructor. Needed for property XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.StringProperty.#ctor">
      <summary>Default constructor for a string property.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ValueEditor.#ctor">
      <summary>Default constructor needed for XAML deserialization.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ValueEditor.BeginInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.BeginInit" />.</summary>
    </member>
    <member name="M:Microsoft.Build.Framework.XamlTypes.ValueEditor.EndInit">
      <summary>See <see cref="M:System.ComponentModel.ISupportInitialize.EndInit" />.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEngineResult.Result">
      <summary>Returns true if the build succeeded.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEngineResult.TargetOutputsPerProject">
      <summary>Outputs of the targets per project.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.Code">
      <summary>Gets the error code of the event.</summary>
      <returns>The error code of the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.ColumnNumber">
      <summary>Gets the column number that corresponds to the beginning of the section of code that raised the event.</summary>
      <returns>The column number that corresponds to the beginning of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.EndColumnNumber">
      <summary>Gets the column number that corresponds to the end of the section of code that raised the event.</summary>
      <returns>The column number that corresponds to the end of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.EndLineNumber">
      <summary>Gets the line number that corresponds to the end of the section of code that raised the event.</summary>
      <returns>The line number that corresponds to the end of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.File">
      <summary>Gets the name of the file that raised the event.</summary>
      <returns>The name of the file that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.LineNumber">
      <summary>Gets the line number that corresponds to the beginning of the section of code that raised the event.</summary>
      <returns>The line number that corresponds to the beginning of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.ProjectFile">
      <summary>Gets or set the name of the project file which issued this event.</summary>
      <returns>Returns the name of the project file which issued this event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildErrorEventArgs.Subcategory">
      <summary>Gets the custom subtype of the event.</summary>
      <returns>The custom subtype of the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.BuildEventContext">
      <summary>Gets or sets location contextual information. This is attached to logging events to define where they are located in relation to the process, engine, project, target, or task that is executing.</summary>
      <returns>A <see cref="T:Microsoft.Build.Framework.BuildEventContext" /> object representing the location contextual information.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.HelpKeyword">
      <summary>Gets the Help keyword for the event.</summary>
      <returns>The Help keyword for the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.Message">
      <summary>Gets the message for the event.</summary>
      <returns>The message for the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.SenderName">
      <summary>Gets the name of the <see cref="T:System.Object" /> raising the event.</summary>
      <returns>The name of the <see cref="T:System.Object" /> raising the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.ThreadId">
      <summary>Gets an integer identifier for the thread that raised the event.</summary>
      <returns>An integer identifier for the thread that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventArgs.Timestamp">
      <summary>Gets the time the event was raised as a <see cref="T:System.DateTime" />.</summary>
      <returns>The time the event was raised as a <see cref="T:System.DateTime" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.BuildRequestId">
      <summary>Retrieves the BuildRequest id. Note that this is not the same as the global request id on a BuildRequest or BuildResult.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.Invalid">
      <summary>Gets whether the context where the event occurred is valid.</summary>
      <returns>
          <see langword="true" /> if the context is valid; <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.NodeId">
      <summary>Gets the node ID where the event occurred.</summary>
      <returns>An integer value representing the node ID.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.ProjectContextId">
      <summary>Gets the ID of the project where the event occurred.</summary>
      <returns>An integer value representing the project ID.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.ProjectInstanceId">
      <summary>Retrieves the project instance id.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.SubmissionId">
      <summary>Retrieves the Submission id.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.TargetId">
      <summary>Gets the ID of the target where the event occurred.</summary>
      <returns>An integer value representing the target ID.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildEventContext.TaskId">
      <summary>Gets the ID of the task where the event occurred.</summary>
      <returns>An integer value representing the task ID.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildFinishedEventArgs.Succeeded">
      <summary>Gets or sets a value indicating whether the build succeeded.</summary>
      <returns>
          <see langword="true" /> to indicate the build succeeded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.Code">
      <summary>Code associated with event.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.ColumnNumber">
      <summary>Column number of interest in associated file.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.EndColumnNumber">
      <summary>Ending column number of interest in associated file.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.EndLineNumber">
      <summary>Ending line number of interest in associated file.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.File">
      <summary>File associated with event.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.Importance">
      <summary>Gets the importance of the event.</summary>
      <returns>A <see cref="T:Microsoft.Build.Framework.MessageImportance" /> value indicating the importance of the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.LineNumber">
      <summary>Line number of interest in associated file.</summary>
      <returns>Returns <see cref="T:System.Int32" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.ProjectFile">
      <summary>The project which was building when the message was issued.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildMessageEventArgs.Subcategory">
      <summary>The custom sub-type of the event.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildStartedEventArgs.BuildEnvironment">
      <summary>Gets the environment dictionary which is used at the start of the build.</summary>
      <returns>Returns the environment dictionary which is used at the start of the build.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.Code">
      <summary>Gets the warning code of the event.</summary>
      <returns>The warning code of the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.ColumnNumber">
      <summary>Gets the column number that corresponds to the beginning of the section of code that raised the event.</summary>
      <returns>The column number that corresponds to the beginning of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.EndColumnNumber">
      <summary>Gets the column number that corresponds to the end of the section of code that raised the event.</summary>
      <returns>The column number that corresponds to the end of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.EndLineNumber">
      <summary>Gets the line number that corresponds to the end of the section of code that raised the event.</summary>
      <returns>The line number that corresponds to the end of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.File">
      <summary>Gets the name of the file that raised the event.</summary>
      <returns>The name of the file that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.LineNumber">
      <summary>Gets the line number that corresponds to the beginning of the section of code that raised the event.</summary>
      <returns>The line number that corresponds to the beginning of the section of code that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.ProjectFile">
      <summary>Gets or sets the project which was building when the message was issued.</summary>
      <returns>Returns the project which was building when the message was issued.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.BuildWarningEventArgs.Subcategory">
      <summary>Gets the custom subcategory of the event.</summary>
      <returns>The custom subcategory of the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs.ProjectFile">
      <summary>Gets the project file name that raised the event.</summary>
      <returns>The project file name that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs.Succeeded">
      <summary>Gets a value indicating whether the build succeeded.</summary>
      <returns>
          <see langword="true" /> if the build succeeded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ExternalProjectStartedEventArgs.ProjectFile">
      <summary>Gets the project file that raised the event.</summary>
      <returns>The project file that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ExternalProjectStartedEventArgs.TargetNames">
      <summary>Gets the target names that are being built in the project.</summary>
      <returns>The names of the targets being built in the external project. This can be a null reference (<see langword="Nothing" /> in Visual Basic) if the external project is being built with the default target.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IBuildEngine.ColumnNumberOfTaskNode">
      <summary>Gets the line number of the task node within the project file that called it.</summary>
      <returns>The line number of the task node within the project file that called it.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IBuildEngine.ContinueOnError">
      <summary>Returns <see langword="true" /> if the ContinueOnError flag was set to <see langword="true" /> for this particular task in the project file.</summary>
      <returns>
          <see langword="true" /> if the ContinueOnError flag was set to <see langword="true" /> for this particular task in the project file.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IBuildEngine.LineNumberOfTaskNode">
      <summary>Gets the line number of the task node within the project file that called it.</summary>
      <returns>The line number of the task node within the project file that called it.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IBuildEngine.ProjectFileOfTaskNode">
      <summary>Gets the full path to the project file that contained the call to this task.</summary>
      <returns>The full path to the project file that contained the call to this task.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IBuildEngine2.IsRunningMultipleNodes">
      <summary>Returns whether the system is running in single- or multi-processor mode.</summary>
      <returns>
          <see langword="true" /> if the system is running in multi-processor mode, <see langword="false" /> otherwise.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IForwardingLogger.BuildEventRedirector">
      <summary>Get or set by the build engine to allow node loggers to forward messages to the central logger.</summary>
      <returns>An <see cref="T:Microsoft.Build.Framework.IEventRedirector" /> representing the event redirector to forward messages to.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.IForwardingLogger.NodeId">
      <summary>Get or set by the build engine or node to inform the forwarding logger on which node it is running.</summary>
      <returns>An integer representing the node ID on which the build engine is running.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ILogger.Parameters">
      <summary>Gets or sets the user-defined parameters of the logger. </summary>
      <returns>The logger parameters.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ILogger.Verbosity">
      <summary>Gets or sets the level of detail to show in the event log.</summary>
      <returns>One of the enumeration values. The default is <see cref="F:Microsoft.Build.Framework.LoggerVerbosity.Normal" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITask.BuildEngine">
      <summary>Gets or sets the build engine associated with the task.</summary>
      <returns>The build engine associated with the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITask.HostObject">
      <summary>Gets or sets any host object that is associated with the task.</summary>
      <returns>The host object associated with the task.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskFactory.FactoryName">
      <summary>Gets the name of the factory.</summary>
      <returns>The name of the factory.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskFactory.TaskType">
      <summary>Gets the type of the task this factory will instantiate.</summary>
      <returns>The type of task.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskItem.ItemSpec">
      <summary>Gets or sets the item specification.</summary>
      <returns>The item specification.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskItem.MetadataCount">
      <summary>Gets the number of metadata entries associated with the item.</summary>
      <returns>The number of metadata entries associated with the item.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskItem.MetadataNames">
      <summary>Gets the names of the metadata entries associated with the item.</summary>
      <returns>The names of the metadata entries associated with the item.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ITaskItem2.EvaluatedIncludeEscaped">
      <summary>Gets or sets the item include value e.g. for disk-based items this would be the file path.</summary>
      <returns>Returns the item include value.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.LazyFormattedBuildEventArgs.Message">
      <summary>Gets the formatted message.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.LoggerException.ErrorCode">
      <summary>Gets the error code associated with this exception's message.</summary>
      <returns>The error code associated with this exception's message.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.LoggerException.HelpKeyword">
      <summary>Gets the F1-help keyword associated with this error.</summary>
      <returns>The F1-help keyword for the error.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectFinishedEventArgs.ProjectFile">
      <summary>Gets the project file associated with the event.</summary>
      <returns>The project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectFinishedEventArgs.Succeeded">
      <summary>Gets a value indicating whether the build succeeded.</summary>
      <returns>The value indicating whether the build succeeded.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.GlobalProperties">
      <summary>Gets the set of global properties used to evaluate this project.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.IDictionary`2" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.Items">
      <summary>Gets the list of items for the event.</summary>
      <returns>The list of items for the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.ParentProjectBuildEventContext">
      <summary>Provides contextual location information, including the project if any that invoked the build of this project.</summary>
      <returns>A <see cref="T:Microsoft.Build.Framework.BuildEventContext" /> object representing the list of items for the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.ProjectFile">
      <summary>Gets the project file associated with the event.</summary>
      <returns>The project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.ProjectId">
      <summary>Gets the project file associated with the event.</summary>
      <returns>An integer representing the ID of the project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.Properties">
      <summary>Gets the list of properties for the event.</summary>
      <returns>The list of properties for the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.TargetNames">
      <summary>Gets the list of targets to be built in the project.</summary>
      <returns>The list of targets to be built in the project.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.ProjectStartedEventArgs.ToolsVersion">
      <summary>Gets the tools version used to evaluate this project.</summary>
      <returns>Returns <see cref="T:System.String" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.RequiredRuntimeAttribute.RuntimeVersion">
      <summary>Gets the CLR runtime version the attribute was constructed with, such as "v2.0."</summary>
      <returns>A string containing the CLR runtime version required.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetFinishedEventArgs.ProjectFile">
      <summary>Gets the name of the project file associated with the event.</summary>
      <returns>the name of the project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetFinishedEventArgs.Succeeded">
      <summary>Gets a value indicating whether the build of the target succeeded.</summary>
      <returns>The value indicating whether the build of the target succeeded.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetFinishedEventArgs.TargetFile">
      <summary>Gets the name of the file that contains the implementation of this target.</summary>
      <returns>The name of the file that contains the implementation of this target.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetFinishedEventArgs.TargetName">
      <summary>Gets the name of the target associated with the event.</summary>
      <returns>The name of the target associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetFinishedEventArgs.TargetOutputs">
      <summary>Gets or sets an enumerator over all target outputs.</summary>
      <returns>Returns an enumerator over all target outputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetStartedEventArgs.ParentTarget">
      <summary>Represents the target which caused this target to build.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetStartedEventArgs.ProjectFile">
      <summary>Gets the name of the project file associated with the event.</summary>
      <returns>The name of the project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetStartedEventArgs.TargetFile">
      <summary>Gets the name of the file that contains the implementation of this target.</summary>
      <returns>The name of the file that contains the implementation of this target.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TargetStartedEventArgs.TargetName">
      <summary>Gets the name of the target associated with the event.</summary>
      <returns>The name of the target associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskCommandLineEventArgs.CommandLine">
      <summary>Gets the command line used by the task to run the underlying program.</summary>
      <returns>The command line used by the task to run the underlying program.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskCommandLineEventArgs.TaskName">
      <summary>Gets the name of the task that raised the event.</summary>
      <returns>The name of the task that raised the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskFinishedEventArgs.ProjectFile">
      <summary>Gets the name of the project file associated with the event.</summary>
      <returns>The name of the project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskFinishedEventArgs.Succeeded">
      <summary>Gets a value indicating whether the execution of the task succeeded.</summary>
      <returns>The value indicating whether the execution of the task succeeded.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskFinishedEventArgs.TaskFile">
      <summary>Gets the MSBuild file where the task is defined.</summary>
      <returns>The MSBuild file where the task is defined.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskFinishedEventArgs.TaskName">
      <summary>Gets the name of the task associated with the event.</summary>
      <returns>The name of the task associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskPropertyInfo.Name">
      <summary>Name of the property.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskPropertyInfo.Output">
      <summary>True of output properties</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskPropertyInfo.PropertyType">
      <summary>The type of the property</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskPropertyInfo.Required">
      <summary>True if the property is required.</summary>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskStartedEventArgs.ProjectFile">
      <summary>Gets the name of the project file associated with the event.</summary>
      <returns>The name of the project file associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskStartedEventArgs.TaskFile">
      <summary>Gets the MSBuild file where the task was defined.</summary>
      <returns>The MSBuild file where the task was defined.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.TaskStartedEventArgs.TaskName">
      <summary>Gets the name of the task associated with the event.</summary>
      <returns>The name of the task associated with the event.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Argument.IsRequired">
      <summary>Indicates whether the <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> pointed to by <see cref="P:Microsoft.Build.Framework.XamlTypes.Argument.Property" /> must be defined so that the definition of the <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> that owns this argument makes sense.</summary>
      <returns>Returns <see langword="true" /> if the BaseProperty must be defined; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Argument.Property">
      <summary>Gets or sets the name of the <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> that this argument refers to.</summary>
      <returns>Returns the name of the <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> that this argument refers to.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Argument.Separator">
      <summary>Gets or sets the string used to separate the argument value from the parent <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> switch in the command line.</summary>
      <returns>Returns the string used to separate the argument value from the parent <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> switch.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Arguments">
      <summary>Gets or sets a list of arguments for this property.</summary>
      <returns>Returns the list of arguments for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Category">
      <summary>Gets or sets the name of the category to which this property belongs.</summary>
      <returns>Returns the name of the category to which this property belongs.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.ContainingRule">
      <summary>Gets or sets the <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> that contains this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
      <returns>Returns the rule that contains this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.DataSource">
      <summary>Gets or sets the data source where the current value of this property is stored.</summary>
      <returns>Returns the data source where the current value of this property is stored.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Default">
      <summary>Gets or sets the default value for this property.</summary>
      <returns>Returns the default value for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Description">
      <summary>Gets or sets a description of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> for use by a prospective UI client.</summary>
      <returns>Returns the description of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.DisplayName">
      <summary>Gets or sets a name that can be used by a prospective UI client to display this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
      <returns>Returns the display name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.F1Keyword">
      <summary>Gets or sets the keyword that is used to open the help page for this property.</summary>
      <returns>Returns the keyword that is used to open the help page for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.HelpContext">
      <summary>Gets or sets the Help context to use when the user presses F1.</summary>
      <returns>Returns the Help context to use when the user presses F1.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.HelpFile">
      <summary>Gets or sets the Help file to use when the user presses F1.</summary>
      <returns>Returns the Help file to use when the user presses F1.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.HelpUrl">
      <summary>Gets or sets the URL of the Help page for this property. The page is opened when the user presses F1.</summary>
      <returns>Returns the URL of the Help page for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.IncludeInCommandLine">
      <summary>Gets or sets a hint to the command-line constructor whether to include this property in the command line.</summary>
      <returns>Returns the hint to the command-line constructor.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.IsRequired">
      <summary>Gets or sets a flag that indicates whether this property is required to have a value set.</summary>
      <returns>Returns a flag that indicates whether this property is required to have a value set.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Metadata">
      <summary>Gets or sets additional attributes of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
      <returns>Returns additional attributes of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.MultipleValuesAllowed">
      <summary>Gets or sets a switch that specifies whether this property allows multiple values to be supplied/selected simultaneously.</summary>
      <returns>Returns a switch that specifies whether this property allows multiple values to be supplied/selected simultaneously. If true, multiple values may be selected.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Name">
      <summary>Gets or sets the name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
      <returns>Returns the name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.ReadOnly">
      <summary>Gets or sets a flag that indicates whether this property is read-only.</summary>
      <returns>Returns <see langword="true" /> if this is a read-only property; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Separator">
      <summary>Gets or sets the token used to separate a switch from its value.</summary>
      <returns>Returns the token used to separate a switch from its value.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Subcategory">
      <summary>Gets or sets the subcategory this property belongs to.</summary>
      <returns>Returns the subcategory this property belongs to.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Switch">
      <summary>Gets or sets the switch representation of this property when the property represents a tool parameter.</summary>
      <returns>Returns the switch representation of this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.SwitchPrefix">
      <summary>Gets or sets the prefix for the switch representation of this property when this property represents a tool parameter.</summary>
      <returns>Returns the prefix for the switch representation of this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.ValueEditors">
      <summary>Gets or sets the list of value editors for this property.</summary>
      <returns>Returns the list of value editors for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BaseProperty.Visible">
      <summary>Gets or sets a hint to the UI client that tells it whether to display this property.</summary>
      <returns>Returns a hint to the UI client that tells it whether to display this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.BoolProperty.ReverseSwitch">
      <summary>Gets or sets a flag that forces the logical negation of the value of a Boolean switch.</summary>
      <returns>Returns <see langword="true" /> if the value is logically negated; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Category.Description">
      <summary>Gets or sets the description of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />.</summary>
      <returns>Returns the description of this category.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Category.DisplayName">
      <summary>Gets or sets a name that can be used by a prospective UI client to display this <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />.</summary>
      <returns>Returns the display name of this category.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Category.HelpString">
      <summary>Gets or sets Help information for this <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />.</summary>
      <returns>Returns Help information for this category.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Category.Name">
      <summary>Gets or sets the name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />.</summary>
      <returns>Returns the name of this category.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Category.Subtype">
      <summary>Gets or sets the subtype of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" />. </summary>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.DefaultContentTypeForItemType">
      <summary>Gets or sets a flag that determines whether default content type is available.</summary>
      <returns>Returns true if default content type is available.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.DisplayName">
      <summary>Serializes IContentType.DisplayName.</summary>
      <returns>Returns the serialized display name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.ItemGroupName">
      <summary>Serializes IContentType.DefaultContentTypeForItemType</summary>
      <returns>Returns the serialized default content type for the item type.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.ItemType">
      <summary>Serializes IContentType.ItemType</summary>
      <returns>Returns the serialized item type.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.Metadata">
      <summary>Serializes content type's metadata. </summary>
      <returns>Returns the serialized metadata.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ContentType.Name">
      <summary>Serializes IContentType.Name.</summary>
      <returns>Returns the serialized name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.HasConfigurationCondition">
      <summary>Gets or sets a flag that determines whether a property is configuration-dependent, as indicated by the presence of a configuration condition that is attached to the property definition at its persistence location.</summary>
      <returns>Returns <see langword="true" /> if a property is configuration-dependent; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.ItemType">
      <summary>Gets or sets the item type of the data source.</summary>
      <returns>Returns the item type of the data source.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.Label">
      <summary>Gets or sets the label of the MSBuild property group/item definition group to which a property/item definition metadata belongs. </summary>
      <returns>Returns the label of the property or item group.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.MSBuildTarget">
      <summary>Gets or sets the semicolon-delimited list of MSBuild targets that must be executed before reading the read-only properties or items of this data source.</summary>
      <returns>Returns a semicolon-delimited list.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.PersistedName">
      <summary>Gets or sets the actual MSBuild property name used to read/write the value of this property.</summary>
      <returns>Returns the actual MSBuild property name used to read/write the value of this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.Persistence">
      <summary>Gets or sets the storage location for this data source.</summary>
      <returns>Returns the storage location for this data source.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.SourceOfDefaultValue">
      <summary>Gets or sets the value of a<see cref="T:Microsoft.Build.Framework.XamlTypes.DefaultValueSourceLocation" /> which indicates the location of the default value for this data source.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.XamlTypes.DefaultValueSourceLocation" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DataSource.SourceType">
      <summary>Gets or sets the data type of the source, which governs how the data is treated during build.</summary>
      <returns>Returns the data type.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DynamicEnumProperty.EnumProvider">
      <summary>Gets or sets the provider that produces the list of possible values for this property. </summary>
      <returns>Returns the provider.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.DynamicEnumProperty.ProviderSettings">
      <summary>Gets or sets a provider-specific set of options to pass to the provider.</summary>
      <returns>Returns the set of options.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumProperty.AdmissibleValues">
      <summary>Gets or sets the list of possible values for this property. </summary>
      <returns>Returns the list of possible values for this property.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.Arguments">
      <summary>Gets or sets the list of arguments for this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" />.</summary>
      <returns>Returns the list of arguments.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.Description">
      <summary>Gets or sets the description of this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> for use by a prospective UI client.</summary>
      <returns>Returns the description.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.DisplayName">
      <summary>Gets or sets the name that can be used by a prospective UI client to display this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" />.</summary>
      <returns>Returns the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.HelpString">
      <summary>Gets or sets Help information for this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" />.</summary>
      <returns>Returns the Help information.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.IsDefault">
      <summary>Gets or sets a flag that determines whether this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" /> is the default value for the associated <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumProperty" />.</summary>
      <returns>
          <see langword="True" /> if the enum value is the default value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.Metadata">
      <summary>Gets or sets additional attributes of this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" />.</summary>
      <returns>Returns the additional attributes.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.Name">
      <summary>Gets or sets the name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumValue" />.</summary>
      <returns>Returns the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.Switch">
      <summary>Gets or sets a switch representation of this property for the case when the parent <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumProperty" /> represents a tool parameter.</summary>
      <returns>Returns the switch representation.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.EnumValue.SwitchPrefix">
      <summary>Gets or sets the prefix for the switch representation of this value for the case when the parent <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumProperty" /> represents a tool parameter.</summary>
      <returns>Returns the switch prefix.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.FileExtension.ContentType">
      <summary>Gets or sets the corresponding content type.</summary>
      <returns>Returns the corresponding content type.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.FileExtension.Name">
      <summary>Gets or sets the name of the file extension.</summary>
      <returns>Returns the name of the file extension.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.IntProperty.MaxValue">
      <summary>Gets or sets the maximum allowed value for this property.</summary>
      <returns>Returns the maximum allowed value. Returns <see langword="null" /> when this property is not set.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.IntProperty.MinValue">
      <summary>Gets or sets the minimum allowed value for this property.</summary>
      <returns>Returns the minimum allowed value. Returns <see langword="null" /> when this property is not set.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ItemType.DefaultContentType">
      <summary>Serializes IItemType.ItemType.</summary>
      <returns>Returns the serialized default content type.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ItemType.DisplayName">
      <summary>Serializes IItemType.DisplayName</summary>
      <returns>Returns the serialized display name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ItemType.Name">
      <summary>Serializes IItemType.Name</summary>
      <returns>Returns the serialized item type name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ItemType.UpToDateCheckInput">
      <summary>Gets or sets a switch that specifies whether item type serialization is included in an up-to-date check.</summary>
      <returns>Returns a switch that specifies whether item type serialization is included in an up-to-date check. If true, the serialization is included.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.NameValuePair.Name">
      <summary>Gets or sets the name of a name-value pair.</summary>
      <returns>Returns the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.NameValuePair.Value">
      <summary>Gets or sets the value of a name-value pair.</summary>
      <returns>Gets the value.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ProjectSchemaDefinitions.Nodes">
      <summary>Gets or sets the list of nodes for this schema definition.</summary>
      <returns>Returns the list of nodes for this schema definition.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.AdditionalInputs">
      <summary>When this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a Build Customization, this field defines the semicolon-separated list of additional inputs to be evaluated for the Build Customization target.</summary>
      <returns>Returns a semicolon-separated list of additional inputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Categories">
      <summary>Gets or sets the list of <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" /> categories that the properties in this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> belong to.</summary>
      <returns>Returns the list of properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.CommandLine">
      <summary>When this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a Build Customization, this field represents the command-line template to be used by a Build Customization task to invoke the tool.</summary>
      <returns>Returns the command-line template.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.DataSource">
      <summary>The <see cref="P:Microsoft.Build.Framework.XamlTypes.Rule.DataSource" /> for the properties in this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" />. This is overridden by any data source that is defined locally for a property.</summary>
      <returns>Returns the data source.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Description">
      <summary>Gets or sets a description of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> for use by a prospective UI client.</summary>
      <returns>Returns the description.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.DisplayName">
      <summary>Gets or sets a name that can be used by a prospective UI client to display this <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
      <returns>Returns the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.EvaluatedCategories">
      <summary>Returns the union of XAML specified <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" /> categories and auto-generated <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" /> categories. The latter are created from any missing categories that are referred to by the properties in this Rule. The auto-generated <see cref="T:Microsoft.Build.Framework.XamlTypes.Category" /> categories only have their names set.</summary>
      <returns>Returns the union of categories.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.ExecutionDescription">
      <summary>When this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a Build Customization, this field represents the message to be displayed before a Build Customization is executed during the build.</summary>
      <returns>Returns the message.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.FileExtension">
      <summary>When this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a Build Customization, this field represents the file name extension to associate.</summary>
      <returns>Returns the file name extension.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.HelpString">
      <summary>Gets or sets Help information for this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" />.</summary>
      <returns>Returns the Help information.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Metadata">
      <summary>Gets or sets arbitrary metadata that may be set on a rule.</summary>
      <returns>Returns <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Name">
      <summary>The name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" />.</summary>
      <returns>Returns the name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Order">
      <summary>Gets or sets a suggestion to a prospective UI client for the relative location of this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> compared to all other Rules in the system.</summary>
      <returns>Returns the suggestion.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Outputs">
      <summary>When this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a Build Customization, this field defines the semicolon-separated list of outputs that are to be evaluated for the Build Customization target.</summary>
      <returns>Returns the outputs.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.OverrideMode">
      <summary>Gets or sets the method to use when multiple rules with the same name appear in the project to reconcile the rules into one instance.</summary>
      <returns>Returns <see cref="T:Microsoft.Build.Framework.XamlTypes.RuleOverrideMode" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.PageTemplate">
      <summary>Gets or sets the UI rendering template that is used to display this Rule.</summary>
      <returns>Returns the UI template.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Properties">
      <summary>Gets or sets the list of properties in this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" />. </summary>
      <returns>Returns the list of properties.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.PropertyPagesHidden">
      <summary>Gets or sets a flag indicating whether property pages for this rule are hidden.</summary>
      <returns>Returns flag value.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.Separator">
      <summary>Gets or sets a token that is used to separate a property switch from its value.</summary>
      <returns>Returns the token separator.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.ShowOnlyRuleProperties">
      <summary>Gets or sets a flag that determines whether to hide the command-line category. </summary>
      <returns>Returns <see langword="true" /> if command-line categories are hidden; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.SupportsFileBatching">
      <summary>Gets or sets a flag that determines whether multiple files can be batched on one command-line invocation.</summary>
      <returns>Returns <see langword="true" /> if multiple files can be batched; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.SwitchPrefix">
      <summary>Gets or sets the prefix to use for all property switches in this <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> for the case when this property <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> represents a tool.</summary>
      <returns>Returns the prefix.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.Rule.ToolName">
      <summary>Gets or sets the name of the tool when this rule represents a tool.</summary>
      <returns>Returns the name of the tool.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.RuleBag.Rules">
      <summary>The list of <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> instances that this <see cref="T:Microsoft.Build.Framework.XamlTypes.RuleBag" /> instance contains.</summary>
      <returns>Returns the list of rules. Always returns at least one rule.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.StringListProperty.CommandLineValueSeparator">
      <summary>Gets or sets the separator to use in delineating individual values of this string list property</summary>
      <returns>Returns the separator.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.StringListProperty.RendererValueSeparator">
      <summary>Gets or sets the UI rendering separator. This property is deprecated.</summary>
      <returns>Returns the UI rendering separator. This property is deprecated.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.StringListProperty.Subtype">
      <summary>Qualifies this string property to give it a more specific classification.</summary>
      <returns>Returns the property subtype.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.StringProperty.Subtype">
      <summary>Qualifies this string property to give it a more specific classification.</summary>
      <returns>Returns the subtype.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ValueEditor.DisplayName">
      <summary>Gets or sets the UI display name for the editor.</summary>
      <returns>Returns the display name.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ValueEditor.EditorType">
      <summary>Gets of sets the name of this <see cref="T:Microsoft.Build.Framework.XamlTypes.ValueEditor" />. </summary>
      <returns>Returns the name of the editor.</returns>
    </member>
    <member name="P:Microsoft.Build.Framework.XamlTypes.ValueEditor.Metadata">
      <summary>Gets or sets additional attributes of the editor that are not generic enough to be made properties on this class. </summary>
      <returns>Returns the additional attributes.</returns>
    </member>
    <member name="T:Microsoft.Build.Framework.AnyEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.AnyEventRaised" /> event of a build.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildEngineResult">
      <summary>Contains the result of the build and the target outputs.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildErrorEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.ErrorRaised" /> event.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildErrorEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.ErrorRaised" /> event of a build.</summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildErrorEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.AnyEventRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildEventContext">
      <summary>Provides contextual location information for build events.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildFinishedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.BuildFinished" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildFinishedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.BuildFinished" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildFinishedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildMessageEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.MessageRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildMessageEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.MessageRaised" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildMessageEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildStartedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.BuildStarted" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildStartedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.BuildStarted" /> event of a build.  </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildStartedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildStatusEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.StatusEventRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildStatusEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.StatusEventRaised" /> event of a build.  </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildStatusEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildWarningEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.WarningRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.BuildWarningEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.WarningRaised" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.BuildWarningEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.CriticalBuildMessageEventArgs">
      <summary>Contains arguments for critical message events. These always have high importance.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.CustomBuildEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.CustomEventRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.CustomBuildEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.CustomEventRaised" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.CustomBuildEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.ExternalProjectFinishedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.CustomEventRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ExternalProjectStartedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.CustomEventRaised" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IBuildEngine">
      <summary>Provides a way for task authors to use a subset of the functionality of the MSBuild engine. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IBuildEngine2">
      <summary>Provides a way for task authors to use a subset of the functionality of the MSBuild engine.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IBuildEngine3">
      <summary>This interface extends IBuildEngine to provide a method allowing building project files in parallel.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IBuildEngine4">
      <summary>This interface extends IBuildEngine to provide a mechanism allowing tasks to share data between task invocations.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ICancelableTask">
      <summary>Interface for tasks which can be cancelled. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IEventRedirector">
      <summary>Forwards build events to other loggers.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IEventSource">
      <summary>Defines the events raised by the build engine. Loggers receive an object implementing this interface in their <see cref="M:Microsoft.Build.Framework.ILogger.Initialize(Microsoft.Build.Framework.IEventSource)" /> method and use it to subscribe to the events they are interested in receiving.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IForwardingLogger">
      <summary>Extends the <see cref="T:Microsoft.Build.Framework.ILogger" /> interface to provide properties to forward events to a logger running in a different process. It can also be used create filtering loggers.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.IGeneratedTask">
      <summary>An interface implemented by tasks that are generated by <see cref="ITaskFactory" /> instances.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ILogger">
      <summary>Defines an MSBuild logger, which subscribes to build system events.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.INodeLogger">
      <summary>Defines a "parallel aware logger" in the build system.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITask">
      <summary>Defines a task, which is a unit of executable code used by MSBuild to perform build operations.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITaskFactory">
      <summary>Interface that a task factory Instance should implement.  </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITaskFactory2">
      <summary>Describes and extended <see cref="T:Microsoft.Build.Framework.ITaskFactory" /> interface that a task factory instance should implement.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITaskHost">
      <summary>Passes host objects from an integrated development environment (IDE) to individual tasks.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITaskItem">
      <summary>Defines an MSBuild item that can be consumed and emitted by tasks. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ITaskItem2">
      <summary>This interface adds escaping support to the ITaskItem interface.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.LazyFormattedBuildEventArgs">
      <summary>Stores strings for parts of a message delaying the formatting until it needs to be shown.  </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.LoadInSeparateAppDomainAttribute">
      <summary>Defines the metadata attribute that MSBuild uses to identify tasks that must be executed in their own application domains. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.LoggerException">
      <summary>Allows a logger to force the build to stop in an explicit way. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.LoggerVerbosity">
      <summary>Specifies the available verbosity levels of a <see cref="T:Microsoft.Build.Utilities.Logger" />. </summary>
    </member>
    <member name="F:Microsoft.Build.Framework.LoggerVerbosity.Quiet">
      <summary>Quiet verbosity, which displays a build summary.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.LoggerVerbosity.Minimal">
      <summary>Minimal verbosity, which displays errors, warnings, messages with <see cref="T:Microsoft.Build.Framework.MessageImportance" /> values of <see cref="F:Microsoft.Build.Framework.MessageImportance.High" />, and a build summary.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.LoggerVerbosity.Normal">
      <summary>Normal verbosity, which displays errors, warnings, messages with <see cref="T:Microsoft.Build.Framework.MessageImportance" /> values of <see cref="F:Microsoft.Build.Framework.MessageImportance.High" />, some status events, and a build summary.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.LoggerVerbosity.Detailed">
      <summary>Detailed verbosity, which displays errors, warnings, messages with <see cref="T:Microsoft.Build.Framework.MessageImportance" /> values of <see cref="F:Microsoft.Build.Framework.MessageImportance.High" /> or <see cref="F:Microsoft.Build.Framework.MessageImportance.Normal" />, all status events, and a build summary.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.LoggerVerbosity.Diagnostic">
      <summary>Diagnostic verbosity, which displays all errors, warnings, messages, status events, and a build summary.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.MessageImportance">
      <summary>Specifies constants that define the importance of a build message. </summary>
    </member>
    <member name="F:Microsoft.Build.Framework.MessageImportance.High">
      <summary>A high importance message.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.MessageImportance.Normal">
      <summary>A normal importance message.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.MessageImportance.Low">
      <summary>A low importance message.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.OutputAttribute">
      <summary>Defines the metadata attribute that task authors use to identify task properties that output data from the task. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ProjectFinishedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.ProjectFinished" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ProjectFinishedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.ProjectFinished" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.ProjectFinishedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.ProjectStartedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.ProjectStarted" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.ProjectStartedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.ProjectStarted" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.ProjectStartedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.RegisteredTaskObjectLifetime">
      <summary>Defines the lifetime of a registered task object.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.RegisteredTaskObjectLifetime.Build">
      <summary>The registered object will be disposed when the build ends.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.RegisteredTaskObjectLifetime.AppDomain">
      <summary>The registered object will be disposed when the <see langword="AppDomain" /> in which the build was launched is unloaded</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.RequiredAttribute">
      <summary>Defines the metadata attribute that task authors use to identify required task properties. Task properties with this attribute must have a set value when the task is run. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.RequiredRuntimeAttribute">
      <summary>Defines the metadata attribute that task authors use to identify whether the task requires a specific version of the CLR runtime. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.RunInMTAAttribute">
      <summary>This attribute is used to mark a task class as explicitly not being required to run in the STA for COM. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.RunInSTAAttribute">
      <summary>This attribute is used to mark a task class as being required to run in a Single Threaded Apartment for COM.  </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TargetFinishedEventArgs">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.Build.Framework.TargetFinishedEventArgs" /> class. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TargetFinishedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.TargetFinished" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.TargetFinishedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.TargetStartedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.TargetStarted" /> event.  </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TargetStartedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.TargetStarted" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.TargetStartedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskCommandLineEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.MessageRaised" /> event.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskFinishedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.TaskFinished" /> event. </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskFinishedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.TaskFinished" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.TargetFinishedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskPropertyInfo">
      <summary>Represents the parameter information from the using task as a strongly typed class.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskStartedEventArgs">
      <summary>Provides data for the <see cref="E:Microsoft.Build.Framework.IEventSource.TaskStarted" /> event.  </summary>
    </member>
    <member name="T:Microsoft.Build.Framework.TaskStartedEventHandler">
      <summary>Represents the method that will handle the <see cref="E:Microsoft.Build.Framework.IEventSource.TaskStarted" /> event of a build. </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A <see cref="T:Microsoft.Build.Framework.TaskStartedEventArgs" /> that contains the event data.</param>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.Argument">
      <summary>Represents an argument to a <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.BaseProperty">
      <summary>Represents a <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.BoolProperty">
      <summary>Represents the schema of a Boolean property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.Category">
      <summary>Represents a category to which a <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" /> can belong.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.CategorySchema">
      <summary>The CategorySchema provides a strongly-typed identity handle to the underlying schema data model.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.ContentType">
      <summary>Used to deserialize the content type information</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.DataSource">
      <summary>Represents the location and grouping for a <see cref="T:Microsoft.Build.Framework.XamlTypes.BaseProperty" />.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.DefaultValueSourceLocation">
      <summary>Determines whether the default value for a property is set at the bottom or the top of the project file. </summary>
    </member>
    <member name="F:Microsoft.Build.Framework.XamlTypes.DefaultValueSourceLocation.BeforeContext">
      <summary>The default value for a property is set at the top of the project file (usually via an import of a .props file).</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.XamlTypes.DefaultValueSourceLocation.AfterContext">
      <summary>The default value for a property is set at the bottom of the project file (usually via an import of a .targets file, where the property definition is conditional on whether the property has not already been defined.)</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.DynamicEnumProperty">
      <summary>Represents the schema of an enumeration property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.EnumProperty">
      <summary>Represents the schema of an enumeration property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.EnumValue">
      <summary>Represents an admissible value of an <see cref="T:Microsoft.Build.Framework.XamlTypes.EnumProperty" />.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.FileExtension">
      <summary>A simple class that deserializes content type data.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.IntProperty">
      <summary>Represent the schema of an integer property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.IProjectSchemaNode">
      <summary>Defines an interface that all root classes from project schema XAML files must implement.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.ItemType">
      <summary>Used to deserialize the item type information.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.NameValuePair">
      <summary>Represents a name-value pair. The name cannot be <see langword="null" /> or empty.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.ProjectSchemaDefinitions">
      <summary>Represents a data-driven XAML script project schema.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.Rule">
      <summary>Used to represent the schema information for a Tool, a Custom Build Rule, a PropertyPage, and so on.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.RuleBag">
      <summary>A container for <see cref="T:Microsoft.Build.Framework.XamlTypes.Rule" /> instances.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.RuleOverrideMode">
      <summary>Describes a subsequent definition for a rule with the same name. </summary>
    </member>
    <member name="F:Microsoft.Build.Framework.XamlTypes.RuleOverrideMode.Replace">
      <summary>A subsequent definition for a rule (with the same name) that entirely overrides a previous definition.</summary>
    </member>
    <member name="F:Microsoft.Build.Framework.XamlTypes.RuleOverrideMode.Extend">
      <summary>A subsequent definition for a rule (with the same name) that adds properties to a previous definition.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.RuleSchema">
      <summary>The RuleSchema provides a strongly-typed identity handle to the underlying schema data model.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.StringListProperty">
      <summary>Represents the schema of a list-of-strings property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.StringProperty">
      <summary>Represents the schema of a string property.</summary>
    </member>
    <member name="T:Microsoft.Build.Framework.XamlTypes.ValueEditor">
      <summary>Represents a value editor.</summary>
    </member>
  </members>
</doc>