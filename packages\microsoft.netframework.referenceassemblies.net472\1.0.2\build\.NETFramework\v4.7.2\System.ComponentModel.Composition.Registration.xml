﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.ComponentModel.Composition.Registration</name>
  </assembly>
  <members>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Registration.ExportBuilder" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.AddMetadata(System.String,System.Func{System.Type,System.Object})">
      <summary>Specifies a metadata key/value pair to be added to the export.</summary>
      <param name="name">The metadata key.</param>
      <param name="itemFunc">A function that takes a type and returns the metadata value.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.AddMetadata(System.String,System.Object)">
      <summary>Specifies a metadata key/value pair to be added to the export.</summary>
      <param name="name">The metadata key.</param>
      <param name="value">The metadata value.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.AsContractName(System.String)">
      <summary>Specifies that the export should be under the given contract name.</summary>
      <param name="contractName">The contract name.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="contractName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="contractName" /> is an empty string.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.AsContractType(System.Type)">
      <summary>Specifies that the export should use the given contract type.</summary>
      <param name="type">The contract type to use.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="type" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.AsContractType``1">
      <summary>Specifies that the export should use the given contract type.</summary>
      <typeparam name="T">The type of contract to use.</typeparam>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ExportBuilder.Inherited">
      <summary>Specifies that this export should be inherited.</summary>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Registration.ImportBuilder" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AllowDefault">
      <summary>Specifies that the import should allow a default value.</summary>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AllowRecomposition">
      <summary>Specifies that the import should be recomposable.</summary>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AsContractName(System.String)">
      <summary>Specifies that the import should be under the given contract name.</summary>
      <param name="contractName">The contract name.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="contractName" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="contractName" /> is an empty string.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AsContractType(System.Type)">
      <summary>Specifies that the import should use the given contract type.</summary>
      <param name="type">The contract type to use.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="type" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AsContractType``1">
      <summary>Specifies that the import should use the given contract type.</summary>
      <typeparam name="T">The type of contract to use.</typeparam>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.AsMany(System.Boolean)">
      <summary>Specifies the import's cardinality.</summary>
      <param name="isMany">
            <see langword="true" /> to specify a multiple import; <see langword="false" /> to specify a single import. The default is <see langword="true" />.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.RequiredCreationPolicy(System.ComponentModel.Composition.CreationPolicy)">
      <summary>Specifies the import's required creation policy.</summary>
      <param name="requiredCreationPolicy">The creation policy the import requires.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ImportBuilder.Source(System.ComponentModel.Composition.ImportSource)">
      <summary>Specifies a value that specifies the scopes this import can be satisfied from.</summary>
      <param name="source">A value that specifies the scopes the import can be satisfied from.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ParameterImportBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Registration.ParameterImportBuilder" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ParameterImportBuilder.Import``1">
      <summary>Defines the specified parameter as an imported parameter.</summary>
      <typeparam name="T">The type of the parameter to define.</typeparam>
      <returns>Default of <paramref name="T" />, but this method should only be referenced and never invoked.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.ParameterImportBuilder.Import``1(System.Action{System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Defines the specified parameter as an imported parameter that has the specified configuration.</summary>
      <param name="configure">An object that contains configuration information for the imported parameter.</param>
      <typeparam name="T">The type of the parameter to define.</typeparam>
      <returns>Default of <paramref name="T" />, but this method should only be referenced and never invoked.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="configure" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.AddMetadata(System.String,System.Func{System.Type,System.Object})">
      <summary>Adds metadata to the matching types.</summary>
      <param name="name">The metadata key.</param>
      <param name="itemFunc">A function that returns the metadata value, given the target type.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.AddMetadata(System.String,System.Object)">
      <summary>Adds metadata to the matching types.</summary>
      <param name="name">The metadata key.</param>
      <param name="value">The metadata value.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.Export">
      <summary>Specifies that matching types should be exported.</summary>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.Export(System.Action{System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies that matching types should be exported, and provides configuration information for the resulting exports.</summary>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.Export``1">
      <summary>Specifies that matching types should be exported by using the specified contract type.</summary>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.Export``1(System.Action{System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies that matching types should be exported by using the specified contract type, and provides configuration information for the resulting exports.</summary>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportInterfaces">
      <summary>Specifies that all the interfaces of the matching types should be exported.</summary>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportInterfaces(System.Predicate{System.Type})">
      <summary>Specifies a set of interfaces on the matching types that should be exported.</summary>
      <param name="interfaceFilter">A predicate that specifies a set of interfaces.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="interfaceFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportInterfaces(System.Predicate{System.Type},System.Action{System.Type,System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies a set of interfaces on the matching types that should be exported, and provides configuration information for the resulting exports.</summary>
      <param name="interfaceFilter">A predicate that specifies a set of interfaces.</param>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="interfaceFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportProperties(System.Predicate{System.Reflection.PropertyInfo})">
      <summary>Specifies a set of properties that should be exported on the matching types.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportProperties(System.Predicate{System.Reflection.PropertyInfo},System.Action{System.Reflection.PropertyInfo,System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies a set of properties on the matching types that should be exported, and provides configuration information for those exports.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportProperties``1(System.Predicate{System.Reflection.PropertyInfo})">
      <summary>Specifies a set of properties on the matching types that should be exported, by using the specified contract type.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ExportProperties``1(System.Predicate{System.Reflection.PropertyInfo},System.Action{System.Reflection.PropertyInfo,System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies a set of properties on the matching types that should be exported by using the specified contract type, and provides configuration information for the resulting exports.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ImportProperties(System.Predicate{System.Reflection.PropertyInfo})">
      <summary>Specifies a set of properties on the matching types that should be imported.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ImportProperties(System.Predicate{System.Reflection.PropertyInfo},System.Action{System.Reflection.PropertyInfo,System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Specifies a set of properties on the matching types that should be marked as imports, and provides configuration information for the resulting imports.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <param name="importConfiguration">An object that contains configuration information about the import. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ImportProperties``1(System.Predicate{System.Reflection.PropertyInfo})">
      <summary>Specifies a set of properties on the matching types that should be marked as imports using the specified contract type.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.ImportProperties``1(System.Predicate{System.Reflection.PropertyInfo},System.Action{System.Reflection.PropertyInfo,System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Specifies a set of properties on the matching types that should be marked as imports by using the specified contract type, and provides configuration information for the resulting imports.</summary>
      <param name="propertyFilter">A predicate that specifies a set of properties.</param>
      <param name="importConfiguration">An object that contains configuration information about the import. The default value is <see langword="null" />.</param>
      <typeparam name="T">The type of contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.SelectConstructor(System.Func{System.Reflection.ConstructorInfo[],System.Reflection.ConstructorInfo})">
      <summary>Specifies the constructor that matching types should use as an importing constructor.</summary>
      <param name="constructorFilter">A function that chooses one constructor from a set of constructors.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="constructorFilter" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="constructorFilter" /> returns <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
              <paramref name="constructorFilter" /> returns a constructor that is not associated with the target type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.SelectConstructor(System.Func{System.Reflection.ConstructorInfo[],System.Reflection.ConstructorInfo},System.Action{System.Reflection.ParameterInfo,System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Specifies the constructor that matching types should use as an importing constructor, and provides configuration information for the resulting import.</summary>
      <param name="constructorFilter">A function that chooses one constructor from a set of constructors.</param>
      <param name="importConfiguration">An object that contains configuration information for the importing constructor. The default is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="constructorFilter" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="constructorFilter" /> returns <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">
              <paramref name="constructorFilter" /> returns a constructor that is not associated with the target type.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder.SetCreationPolicy(System.ComponentModel.Composition.CreationPolicy)">
      <summary>Specifies the creation policy for matching types.</summary>
      <param name="creationPolicy">The creation policy.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ExportProperty(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
      <summary>Specifies a property that should be exported.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ExportProperty(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Action{System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies a property that should be exported, and provides configuration information for the resulting export.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ExportProperty``1(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
      <summary>Specifies a property that should be exported as the specified contract type.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <typeparam name="TContract">The type of the contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ExportProperty``1(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Action{System.ComponentModel.Composition.Registration.ExportBuilder})">
      <summary>Specifies a property that should be exported as the specified contract type, and provides configuration information for the resulting export.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <param name="exportConfiguration">An object that contains configuration information about the export. The default value is <see langword="null" />.</param>
      <typeparam name="TContract">The type of the contract.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ImportProperty(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
      <summary>Specifies that a property should be imported.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ImportProperty(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Action{System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Specifies that a property should be imported, and provides configuration information for the resulting import.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <param name="importConfiguration">An object that contains configuration information about the import. The default value is <see langword="null" />.</param>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ImportProperty``1(System.Linq.Expressions.Expression{System.Func{`0,System.Object}})">
      <summary>Specifies that a property should be imported using the specified contract name.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <typeparam name="TContract">The contract type to use.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.ImportProperty``1(System.Linq.Expressions.Expression{System.Func{`0,System.Object}},System.Action{System.ComponentModel.Composition.Registration.ImportBuilder})">
      <summary>Specifies that a property should be imported using the specified contract name, and provides configuration information for the resulting import.</summary>
      <param name="propertyFilter">An expression that results in a reference to the target property.</param>
      <param name="importConfiguration">An object that contains configuration information about the import. The default value is <see langword="null" />.</param>
      <typeparam name="TContract">The contract name to use.</typeparam>
      <returns>The current object, to allow chaining.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="propertyFilter" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.PartBuilder`1.SelectConstructor(System.Linq.Expressions.Expression{System.Func{System.ComponentModel.Composition.Registration.ParameterImportBuilder,`0}})">
      <summary>Specifies the constructor that should be used as an importing constructor.</summary>
      <param name="constructorFilter">An expression that constructs <paramref name="T" /> by using the desired constructor. It is used only for reference, not actually invoked.</param>
      <returns>The current object, to allow chaining.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.ComponentModel.Composition.Registration.RegistrationBuilder" /> class.</summary>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForType(System.Type)">
      <summary>Gets an object that represents a rule that applies to the specified type.</summary>
      <param name="type">The type.</param>
      <returns>An object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForType``1">
      <summary>Gets a strongly typed object that represents a rule that applies to the specified type.</summary>
      <typeparam name="T">The type.</typeparam>
      <returns>A strongly typed object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForTypesDerivedFrom(System.Type)">
      <summary>Gets an object that represents a rule applicable to all types that implement the specified type, but not applicable to the specified type.</summary>
      <param name="type">
            The type.</param>
      <returns>An object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForTypesDerivedFrom``1">
      <summary>Gets a strongly typed object that represents a rule applicable to all types that implement the specified type, but not applicable to the specified type.</summary>
      <typeparam name="T">The type.</typeparam>
      <returns>A strongly typed object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForTypesMatching(System.Predicate{System.Type})">
      <summary>Gets an object that represents a rule applicable to all types that match the specified predicate.</summary>
      <param name="typeFilter">The predicate to match.</param>
      <returns>An object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.ForTypesMatching``1(System.Predicate{System.Type})">
      <summary>Gets a strongly typed object that represents a rule applicable to all types that match the specified predicate.</summary>
      <param name="typeFilter">The predicate to match.</param>
      <typeparam name="T">The type of <see cref="T:System.ComponentModel.Composition.Registration.PartBuilder`1" /> object to return.</typeparam>
      <returns>A strongly typed object that represents the rule.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.GetCustomAttributes(System.Reflection.MemberInfo,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>Provides a list of custom attributes for the specified member, as represented in this reflection context.</summary>
      <param name="member">The member whose custom attributes will be returned.</param>
      <param name="declaredAttributes">A collection of the member's attributes in its current context.</param>
      <returns>A collection that represents the custom attributes of the specified member in this reflection context.</returns>
    </member>
    <member name="M:System.ComponentModel.Composition.Registration.RegistrationBuilder.GetCustomAttributes(System.Reflection.ParameterInfo,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>Provides a list of custom attributes for the specified parameter, as represented in this reflection context.</summary>
      <param name="parameter">The parameter whose custom attributes will be returned.</param>
      <param name="declaredAttributes">A collection of the parameter's attributes in its current context.</param>
      <returns>A collection that represents the custom attributes of the specified parameter in this reflection context.</returns>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.ExportBuilder">
      <summary>Represents configuration information for a rule-based export.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.ImportBuilder">
      <summary>Represents configuration information for a rule-based import.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.ParameterImportBuilder">
      <summary>Represents configuration for a rule-based constructor parameter import.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.PartBuilder">
      <summary>Represents a rule for decorating a set of matching types with Managed Extensibility Framework (MEF) attributes.</summary>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.PartBuilder`1">
      <summary>Extends the <see cref="T:System.ComponentModel.Composition.Registration.PartBuilder" /> class to allow the use of expressions to select members.</summary>
      <typeparam name="T">The type of the target.</typeparam>
    </member>
    <member name="T:System.ComponentModel.Composition.Registration.RegistrationBuilder">
      <summary>Represents a reflection context that has associated conventions that describe rules for decorating entities with Managed Extensibility Framework (MEF) attributes.</summary>
    </member>
  </members>
</doc>