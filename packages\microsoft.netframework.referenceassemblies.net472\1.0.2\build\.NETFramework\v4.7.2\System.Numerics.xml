﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Numerics</name>
  </assembly>
  <members>
    <member name="F:System.Numerics.Complex.ImaginaryOne">
      <summary>Returns a new <see cref="T:System.Numerics.Complex" /> instance with a real number equal to zero and an imaginary number equal to one.</summary>
    </member>
    <member name="F:System.Numerics.Complex.One">
      <summary>Returns a new <see cref="T:System.Numerics.Complex" /> instance with a real number equal to one and an imaginary number equal to zero.</summary>
    </member>
    <member name="F:System.Numerics.Complex.Zero">
      <summary>Returns a new <see cref="T:System.Numerics.Complex" /> instance with a real number equal to zero and an imaginary number equal to zero.</summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M11">
      <summary>The first element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M12">
      <summary>The second element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M21">
      <summary>The first element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M22">
      <summary>The second element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M31">
      <summary>The first element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix3x2.M32">
      <summary>The second element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M11">
      <summary>The first element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M12">
      <summary>The second element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M13">
      <summary>The third element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M14">
      <summary>The fourth element of the first row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M21">
      <summary>The first element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M22">
      <summary>The second element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M23">
      <summary>The third element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M24">
      <summary>The fourth element of the second row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M31">
      <summary>The first element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M32">
      <summary>The second element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M33">
      <summary>The third element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M34">
      <summary>The fourth element of the third row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M41">
      <summary>The first element of the fourth row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M42">
      <summary>The second element of the fourth row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M43">
      <summary>The third element of the fourth row. </summary>
    </member>
    <member name="F:System.Numerics.Matrix4x4.M44">
      <summary>The fourth element of the fourth row. </summary>
    </member>
    <member name="F:System.Numerics.Plane.D">
      <summary>The distance of the plane along its normal from the origin. </summary>
    </member>
    <member name="F:System.Numerics.Plane.Normal">
      <summary>The normal vector of the plane. </summary>
    </member>
    <member name="F:System.Numerics.Quaternion.W">
      <summary> The rotation component of the quaternion. </summary>
    </member>
    <member name="F:System.Numerics.Quaternion.X">
      <summary>The X value of the vector component of the quaternion. </summary>
    </member>
    <member name="F:System.Numerics.Quaternion.Y">
      <summary>The Y value of the vector component of the quaternion. </summary>
    </member>
    <member name="F:System.Numerics.Quaternion.Z">
      <summary>The Z value of the vector component of the quaternion. </summary>
    </member>
    <member name="F:System.Numerics.Vector2.X">
      <summary>The X component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector2.Y">
      <summary>The Y component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector3.X">
      <summary>The X component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector3.Y">
      <summary>The Y component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector3.Z">
      <summary>The Z component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector4.W">
      <summary>The W component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector4.X">
      <summary>The X component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector4.Y">
      <summary>The Y component of the vector. </summary>
    </member>
    <member name="F:System.Numerics.Vector4.Z">
      <summary>The Z component of the vector. </summary>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Byte[])">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using the values in a byte array.</summary>
      <param name="value">An array of byte values in little-endian order.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Decimal)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using a <see cref="T:System.Decimal" /> value.</summary>
      <param name="value">A decimal number.</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using a double-precision floating-point value.</summary>
      <param name="value">A double-precision floating-point value.</param>
      <exception cref="T:System.OverflowException">The value of <paramref name="value" /> is <see cref="F:System.Double.NaN" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Double.NegativeInfinity" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Double.PositiveInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using a 32-bit signed integer value.</summary>
      <param name="value">A 32-bit signed integer.</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Int64)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using a 64-bit signed integer value.</summary>
      <param name="value">A 64-bit signed integer.</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.Single)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using a single-precision floating-point value.</summary>
      <param name="value">A single-precision floating-point value.</param>
      <exception cref="T:System.OverflowException">The value of <paramref name="value" /> is <see cref="F:System.Single.NaN" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Single.NegativeInfinity" />.-or-The value of <paramref name="value" /> is <see cref="F:System.Single.PositiveInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.UInt32)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure using an unsigned 32-bit integer value.</summary>
      <param name="value">An unsigned 32-bit integer value.</param>
    </member>
    <member name="M:System.Numerics.BigInteger.#ctor(System.UInt64)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.BigInteger" /> structure with an unsigned 64-bit integer value.</summary>
      <param name="value">An unsigned 64-bit integer.</param>
    </member>
    <member name="M:System.Numerics.BigInteger.Abs(System.Numerics.BigInteger)">
      <summary>Gets the absolute value of a <see cref="T:System.Numerics.BigInteger" /> object.</summary>
      <param name="value">A number.</param>
      <returns>The absolute value of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Add(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Adds two <see cref="T:System.Numerics.BigInteger" /> values and returns the result.</summary>
      <param name="left">The first value to add.</param>
      <param name="right">The second value to add.</param>
      <returns>The sum of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Compare(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Compares two <see cref="T:System.Numerics.BigInteger" /> values and returns an integer that indicates whether the first value is less than, equal to, or greater than the second value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>A signed integer that indicates the relative values of <paramref name="left" /> and <paramref name="right" />, as shown in the following table.ValueConditionLess than zero
                  <paramref name="left" /> is less than <paramref name="right" />.Zero
                  <paramref name="left" /> equals <paramref name="right" />.Greater than zero
                  <paramref name="left" /> is greater than <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.Int64)">
      <summary>Compares this instance to a signed 64-bit integer and returns an integer that indicates whether the value of this instance is less than, equal to, or greater than the value of the signed 64-bit integer.</summary>
      <param name="other">The signed 64-bit integer to compare.</param>
      <returns>A signed integer value that indicates the relationship of this instance to <paramref name="other" />, as shown in the following table.Return valueDescriptionLess than zeroThe current instance is less than <paramref name="other" />.ZeroThe current instance equals <paramref name="other" />.Greater than zeroThe current instance is greater than <paramref name="other" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.Numerics.BigInteger)">
      <summary>Compares this instance to a second <see cref="T:System.Numerics.BigInteger" /> and returns an integer that indicates whether the value of this instance is less than, equal to, or greater than the value of the specified object.</summary>
      <param name="other">The object to compare.</param>
      <returns>A signed integer value that indicates the relationship of this instance to <paramref name="other" />, as shown in the following table.Return valueDescriptionLess than zeroThe current instance is less than <paramref name="other" />.ZeroThe current instance equals <paramref name="other" />.Greater than zeroThe current instance is greater than <paramref name="other" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.Object)">
      <summary>Compares this instance to a specified object and returns an integer that indicates whether the value of this instance is less than, equal to, or greater than the value of the specified object.</summary>
      <param name="obj">The object to compare.</param>
      <returns>A signed integer that indicates the relationship of the current instance to the <paramref name="obj" /> parameter, as shown in the following table.Return valueDescriptionLess than zeroThe current instance is less than <paramref name="obj" />.ZeroThe current instance equals <paramref name="obj" />.Greater than zeroThe current instance is greater than <paramref name="obj" />, or the <paramref name="obj" /> parameter is <see langword="null" />. </returns>
      <exception cref="T:System.ArgumentException">
              <paramref name="obj" /> is not a <see cref="T:System.Numerics.BigInteger" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.CompareTo(System.UInt64)">
      <summary>Compares this instance to an unsigned 64-bit integer and returns an integer that indicates whether the value of this instance is less than, equal to, or greater than the value of the unsigned 64-bit integer.</summary>
      <param name="other">The unsigned 64-bit integer to compare.</param>
      <returns>A signed integer that indicates the relative value of this instance and <paramref name="other" />, as shown in the following table.Return valueDescriptionLess than zeroThe current instance is less than <paramref name="other" />.ZeroThe current instance equals <paramref name="other" />.Greater than zeroThe current instance is greater than <paramref name="other" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Divide(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Divides one <see cref="T:System.Numerics.BigInteger" /> value by another and returns the result.</summary>
      <param name="dividend">The value to be divided.</param>
      <param name="divisor">The value to divide by.</param>
      <returns>The quotient of the division.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.DivRem(System.Numerics.BigInteger,System.Numerics.BigInteger,System.Numerics.BigInteger@)">
      <summary>Divides one <see cref="T:System.Numerics.BigInteger" /> value by another, returns the result, and returns the remainder in an output parameter.</summary>
      <param name="dividend">The value to be divided.</param>
      <param name="divisor">The value to divide by.</param>
      <param name="remainder">When this method returns, contains a <see cref="T:System.Numerics.BigInteger" /> value that represents the remainder from the division. This parameter is passed uninitialized.</param>
      <returns>The quotient of the division.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Int64)">
      <summary>Returns a value that indicates whether the current instance and a signed 64-bit integer have the same value.</summary>
      <param name="other">The signed 64-bit integer value to compare.</param>
      <returns>
          <see langword="true" /> if the signed 64-bit integer and the current instance have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether the current instance and a specified <see cref="T:System.Numerics.BigInteger" /> object have the same value.</summary>
      <param name="other">The object to compare.</param>
      <returns>
          <see langword="true" /> if this <see cref="T:System.Numerics.BigInteger" /> object and <paramref name="other" /> have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current instance and a specified object have the same value.</summary>
      <param name="obj">The object to compare. </param>
      <returns>
          <see langword="true" /> if the <paramref name="obj" /> argument is a <see cref="T:System.Numerics.BigInteger" /> object, and its value is equal to the value of the current <see cref="T:System.Numerics.BigInteger" /> instance; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Equals(System.UInt64)">
      <summary>Returns a value that indicates whether the current instance and an unsigned 64-bit integer have the same value.</summary>
      <param name="other">The unsigned 64-bit integer to compare.</param>
      <returns>
          <see langword="true" /> if the current instance and the unsigned 64-bit integer have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:System.Numerics.BigInteger" /> object.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.GreatestCommonDivisor(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Finds the greatest common divisor of two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value.</param>
      <param name="right">The second value.</param>
      <returns>The greatest common divisor of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Log(System.Numerics.BigInteger)">
      <summary>Returns the natural (base <see langword="e" />) logarithm of a specified number.</summary>
      <param name="value">The number whose logarithm is to be found.</param>
      <returns>The natural (base <see langword="e" />) logarithm of <paramref name="value" />, as shown in the table in the Remarks section.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The natural log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Log(System.Numerics.BigInteger,System.Double)">
      <summary>Returns the logarithm of a specified number in a specified base.</summary>
      <param name="value">A number whose logarithm is to be found.</param>
      <param name="baseValue">The base of the logarithm.</param>
      <returns>The base <paramref name="baseValue" /> logarithm of <paramref name="value" />, as shown in the table in the Remarks section.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Log10(System.Numerics.BigInteger)">
      <summary>Returns the base 10 logarithm of a specified number.</summary>
      <param name="value">A number whose logarithm is to be found.</param>
      <returns>The base 10 logarithm of <paramref name="value" />, as shown in the table in the Remarks section.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The base 10 log of <paramref name="value" /> is out of range of the <see cref="T:System.Double" /> data type.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Max(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns the larger of two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>The <paramref name="left" /> or <paramref name="right" /> parameter, whichever is larger.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Min(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns the smaller of two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>The <paramref name="left" /> or <paramref name="right" /> parameter, whichever is smaller.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ModPow(System.Numerics.BigInteger,System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Performs modulus division on a number raised to the power of another number.</summary>
      <param name="value">The number to raise to the <paramref name="exponent" /> power.</param>
      <param name="exponent">The exponent to raise <paramref name="value" /> by.</param>
      <param name="modulus">The number by which to divide <paramref name="value" /> raised to the <paramref name="exponent" /> power.</param>
      <returns>The remainder after dividing <paramref name="value" />exponent by <paramref name="modulus" />.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="modulus" /> is zero.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="exponent" /> is negative.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Multiply(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns the product of two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first number to multiply.</param>
      <param name="right">The second number to multiply.</param>
      <returns>The product of the <paramref name="left" /> and <paramref name="right" /> parameters.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Negate(System.Numerics.BigInteger)">
      <summary>Negates a specified <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to negate.</param>
      <returns>The result of the <paramref name="value" /> parameter multiplied by negative one (-1).</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Addition(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Adds the values of two specified <see cref="T:System.Numerics.BigInteger" /> objects.</summary>
      <param name="left">The first value to add.</param>
      <param name="right">The second value to add.</param>
      <returns>The sum of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_BitwiseAnd(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Performs a bitwise <see langword="And" /> operation on two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value.</param>
      <param name="right">The second value.</param>
      <returns>The result of the bitwise <see langword="And" /> operation.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_BitwiseOr(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Performs a bitwise <see langword="Or" /> operation on two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value.</param>
      <param name="right">The second value.</param>
      <returns>The result of the bitwise <see langword="Or" /> operation.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Decrement(System.Numerics.BigInteger)">
      <summary>Decrements a <see cref="T:System.Numerics.BigInteger" /> value by 1.</summary>
      <param name="value">The value to decrement.</param>
      <returns>The value of the <paramref name="value" /> parameter decremented by 1.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Division(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Divides a specified <see cref="T:System.Numerics.BigInteger" /> value by another specified <see cref="T:System.Numerics.BigInteger" /> value by using integer division.</summary>
      <param name="dividend">The value to be divided.</param>
      <param name="divisor">The value to divide by.</param>
      <returns>The integral result of the division.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a signed long integer value and a <see cref="T:System.Numerics.BigInteger" /> value are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value and a signed long integer value are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether the values of two <see cref="T:System.Numerics.BigInteger" /> objects are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value and an unsigned long integer value are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Equality(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether an unsigned long integer value and a <see cref="T:System.Numerics.BigInteger" /> value are equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_ExclusiveOr(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Performs a bitwise exclusive <see langword="Or" /> (<see langword="XOr" />) operation on two <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value.</param>
      <param name="right">The second value.</param>
      <returns>The result of the bitwise <see langword="Or" /> operation.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Decimal)~System.Numerics.BigInteger">
      <summary>Defines an explicit conversion of a <see cref="T:System.Decimal" /> object to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Double)~System.Numerics.BigInteger">
      <summary>Defines an explicit conversion of a <see cref="T:System.Double" /> value to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is <see cref="F:System.Double.NaN" />.-or-
              <paramref name="value" /> is <see cref="F:System.Double.PositiveInfinity" />.-or-
              <paramref name="value" /> is <see cref="F:System.Double.NegativeInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Byte">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to an unsigned byte value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Byte" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.Byte.MinValue" />. -or-
              <paramref name="value" /> is greater than <see cref="F:System.Byte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Decimal">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a <see cref="T:System.Decimal" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Decimal" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.Decimal.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.Decimal.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Double">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a <see cref="T:System.Double" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Double" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int16">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a 16-bit signed integer value.</summary>
      <param name="value">The value to convert to a 16-bit signed integer.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.Int16.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.Int16.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int32">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a 32-bit signed integer value.</summary>
      <param name="value">The value to convert to a 32-bit signed integer. </param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.Int32.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Int64">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a 64-bit signed integer value.</summary>
      <param name="value">The value to convert to a 64-bit signed integer.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.Int64.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.Int64.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.SByte">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a signed 8-bit value.</summary>
      <param name="value">The value to convert to a signed 8-bit value.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.SByte.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.SByte.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.Single">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to a single-precision floating-point value.</summary>
      <param name="value">The value to convert to a single-precision floating-point value.</param>
      <returns>An object that contains the closest possible representation of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt16">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to an unsigned 16-bit integer value.</summary>
      <param name="value">The value to convert to an unsigned 16-bit integer.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.UInt16.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.UInt16.MaxValue" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt32">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to an unsigned 32-bit integer value.</summary>
      <param name="value">The value to convert to an unsigned 32-bit integer.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.UInt32.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.UInt32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Numerics.BigInteger)~System.UInt64">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> object to an unsigned 64-bit integer value.</summary>
      <param name="value">The value to convert to an unsigned 64-bit integer.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is less than <see cref="F:System.UInt64.MinValue" />.-or-
              <paramref name="value" /> is greater than <see cref="F:System.UInt64.MaxValue" />. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Explicit(System.Single)~System.Numerics.BigInteger">
      <summary>Defines an explicit conversion of a <see cref="T:System.Single" /> object to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.OverflowException">
              <paramref name="value" /> is <see cref="F:System.Single.NaN" />.-or-
              <paramref name="value" /> is <see cref="F:System.Single.PositiveInfinity" />.-or-
              <paramref name="value" /> is <see cref="F:System.Single.NegativeInfinity" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit signed integer is greater than a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> is greater than a 64-bit signed integer value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than another <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than a 64-bit unsigned integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThan(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than a 64-bit unsigned integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit signed integer is greater than or equal to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than or equal to a 64-bit signed integer value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than or equal to another <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is greater than or equal to a 64-bit unsigned integer value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_GreaterThanOrEqual(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit unsigned integer is greater than or equal to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is greater than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Byte)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of an unsigned byte to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int16)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a signed 16-bit integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int32)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a signed 32-bit integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.Int64)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a signed 64-bit integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.SByte)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of an 8-bit signed integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt16)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a 16-bit unsigned integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt32)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a 32-bit unsigned integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Implicit(System.UInt64)~System.Numerics.BigInteger">
      <summary>Defines an implicit conversion of a 64-bit unsigned integer to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">The value to convert to a <see cref="T:System.Numerics.BigInteger" />.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Increment(System.Numerics.BigInteger)">
      <summary>Increments a <see cref="T:System.Numerics.BigInteger" /> value by 1.</summary>
      <param name="value">The value to increment.</param>
      <returns>The value of the <paramref name="value" /> parameter incremented by 1.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit signed integer and a <see cref="T:System.Numerics.BigInteger" /> value are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value and a 64-bit signed integer are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether two <see cref="T:System.Numerics.BigInteger" /> objects have different values.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value and a 64-bit unsigned integer are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Inequality(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit unsigned integer and a <see cref="T:System.Numerics.BigInteger" /> value are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LeftShift(System.Numerics.BigInteger,System.Int32)">
      <summary>Shifts a <see cref="T:System.Numerics.BigInteger" /> value a specified number of bits to the left.</summary>
      <param name="value">The value whose bits are to be shifted.</param>
      <param name="shift">The number of bits to shift <paramref name="value" /> to the left.</param>
      <returns>A value that has been shifted to the left by the specified number of bits.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit signed integer is less than a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than a 64-bit signed integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than another <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than a 64-bit unsigned integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThan(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit unsigned integer is less than a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Int64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit signed integer is less than or equal to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than or equal to <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.Int64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than or equal to a 64-bit signed integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than or equal to <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than or equal to another <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than or equal to <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.Numerics.BigInteger,System.UInt64)">
      <summary>Returns a value that indicates whether a <see cref="T:System.Numerics.BigInteger" /> value is less than or equal to a 64-bit unsigned integer.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than or equal to <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_LessThanOrEqual(System.UInt64,System.Numerics.BigInteger)">
      <summary>Returns a value that indicates whether a 64-bit unsigned integer is less than or equal to a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> is less than or equal to <paramref name="right" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Modulus(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Returns the remainder that results from division with two specified <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="dividend">The value to be divided.</param>
      <param name="divisor">The value to divide by.</param>
      <returns>The remainder that results from the division.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Multiply(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Multiplies two specified <see cref="T:System.Numerics.BigInteger" /> values.</summary>
      <param name="left">The first value to multiply.</param>
      <param name="right">The second value to multiply.</param>
      <returns>The product of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_OnesComplement(System.Numerics.BigInteger)">
      <summary>Returns the bitwise one's complement of a <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="value">An integer value.</param>
      <returns>The bitwise one's complement of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_RightShift(System.Numerics.BigInteger,System.Int32)">
      <summary>Shifts a <see cref="T:System.Numerics.BigInteger" /> value a specified number of bits to the right.</summary>
      <param name="value">The value whose bits are to be shifted.</param>
      <param name="shift">The number of bits to shift <paramref name="value" /> to the right.</param>
      <returns>A value that has been shifted to the right by the specified number of bits.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_Subtraction(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Subtracts a <see cref="T:System.Numerics.BigInteger" /> value from another <see cref="T:System.Numerics.BigInteger" /> value.</summary>
      <param name="left">The value to subtract from (the minuend).</param>
      <param name="right">The value to subtract (the subtrahend).</param>
      <returns>The result of subtracting <paramref name="right" /> from <paramref name="left" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_UnaryNegation(System.Numerics.BigInteger)">
      <summary>Negates a specified BigInteger value. </summary>
      <param name="value">The value to negate.</param>
      <returns>The result of the <paramref name="value" /> parameter multiplied by negative one (-1).</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.op_UnaryPlus(System.Numerics.BigInteger)">
      <summary>Returns the value of the <see cref="T:System.Numerics.BigInteger" /> operand. (The sign of the operand is unchanged.)</summary>
      <param name="value">An integer value.</param>
      <returns>The value of the <paramref name="value" /> operand.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String)">
      <summary>Converts the string representation of a number to its <see cref="T:System.Numerics.BigInteger" /> equivalent.</summary>
      <param name="value">A string that contains the number to convert.</param>
      <returns>A value that is equivalent to the number specified in the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
              <paramref name="value" /> is not in the correct format.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.Globalization.NumberStyles)">
      <summary>Converts the string representation of a number in a specified style to its <see cref="T:System.Numerics.BigInteger" /> equivalent.</summary>
      <param name="value">A string that contains a number to convert. </param>
      <param name="style">A bitwise combination of the enumeration values that specify the permitted format of <paramref name="value" />.</param>
      <returns>A value that is equivalent to the number specified in the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.ArgumentException">
              <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-
              <paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
              <paramref name="value" /> does not comply with the input pattern specified by <see cref="T:System.Globalization.NumberStyles" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.Globalization.NumberStyles,System.IFormatProvider)">
      <summary>Converts the string representation of a number in a specified style and culture-specific format to its <see cref="T:System.Numerics.BigInteger" /> equivalent.</summary>
      <param name="value">A string that contains a number to convert.</param>
      <param name="style">A bitwise combination of the enumeration values that specify the permitted format of <paramref name="value" />.</param>
      <param name="provider">An object that provides culture-specific formatting information about <paramref name="value" />.</param>
      <returns>A value that is equivalent to the number specified in the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.ArgumentException">
              <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-
              <paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
              <paramref name="value" /> does not comply with the input pattern specified by <paramref name="style" />.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Parse(System.String,System.IFormatProvider)">
      <summary>Converts the string representation of a number in a specified culture-specific format to its <see cref="T:System.Numerics.BigInteger" /> equivalent.</summary>
      <param name="value">A string that contains a number to convert.</param>
      <param name="provider">An object that provides culture-specific formatting information about <paramref name="value" />.</param>
      <returns>A value that is equivalent to the number specified in the <paramref name="value" /> parameter.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">
              <paramref name="value" /> is not in the correct format.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Pow(System.Numerics.BigInteger,System.Int32)">
      <summary>Raises a <see cref="T:System.Numerics.BigInteger" /> value to the power of a specified value.</summary>
      <param name="value">The number to raise to the <paramref name="exponent" /> power.</param>
      <param name="exponent">The exponent to raise <paramref name="value" /> by.</param>
      <returns>The result of raising <paramref name="value" /> to the <paramref name="exponent" /> power.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The value of the <paramref name="exponent" /> parameter is negative.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Remainder(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Performs integer division on two <see cref="T:System.Numerics.BigInteger" /> values and returns the remainder.</summary>
      <param name="dividend">The value to be divided.</param>
      <param name="divisor">The value to divide by.</param>
      <returns>The remainder after dividing <paramref name="dividend" /> by <paramref name="divisor" />.</returns>
      <exception cref="T:System.DivideByZeroException">
              <paramref name="divisor" /> is 0 (zero).</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.Subtract(System.Numerics.BigInteger,System.Numerics.BigInteger)">
      <summary>Subtracts one <see cref="T:System.Numerics.BigInteger" /> value from another and returns the result.</summary>
      <param name="left">The value to subtract from (the minuend).</param>
      <param name="right">The value to subtract (the subtrahend).</param>
      <returns>The result of subtracting <paramref name="right" /> from <paramref name="left" />.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToByteArray">
      <summary>Converts a <see cref="T:System.Numerics.BigInteger" /> value to a byte array.</summary>
      <returns>The value of the current <see cref="T:System.Numerics.BigInteger" /> object converted to an array of bytes.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString">
      <summary>Converts the numeric value of the current <see cref="T:System.Numerics.BigInteger" /> object to its equivalent string representation.</summary>
      <returns>The string representation of the current <see cref="T:System.Numerics.BigInteger" /> value.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.IFormatProvider)">
      <summary>Converts the numeric value of the current <see cref="T:System.Numerics.BigInteger" /> object to its equivalent string representation by using the specified culture-specific formatting information.</summary>
      <param name="provider">An object that supplies culture-specific formatting information.</param>
      <returns>The string representation of the current <see cref="T:System.Numerics.BigInteger" /> value in the format specified by the <paramref name="provider" /> parameter.</returns>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.String)">
      <summary>Converts the numeric value of the current <see cref="T:System.Numerics.BigInteger" /> object to its equivalent string representation by using the specified format.</summary>
      <param name="format">A standard or custom numeric format string.</param>
      <returns>The string representation of the current <see cref="T:System.Numerics.BigInteger" /> value in the format specified by the <paramref name="format" /> parameter.</returns>
      <exception cref="T:System.FormatException">
              <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.ToString(System.String,System.IFormatProvider)">
      <summary>Converts the numeric value of the current <see cref="T:System.Numerics.BigInteger" /> object to its equivalent string representation by using the specified format and culture-specific format information.</summary>
      <param name="format">A standard or custom numeric format string.</param>
      <param name="provider">An object that supplies culture-specific formatting information.</param>
      <returns>The string representation of the current <see cref="T:System.Numerics.BigInteger" /> value as specified by the <paramref name="format" /> and <paramref name="provider" /> parameters.</returns>
      <exception cref="T:System.FormatException">
              <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.BigInteger.TryParse(System.String,System.Globalization.NumberStyles,System.IFormatProvider,System.Numerics.BigInteger@)">
      <summary>Tries to convert the string representation of a number in a specified style and culture-specific format to its <see cref="T:System.Numerics.BigInteger" /> equivalent, and returns a value that indicates whether the conversion succeeded.</summary>
      <param name="value">The string representation of a number. The string is interpreted using the style specified by <paramref name="style" />.</param>
      <param name="style">A bitwise combination of enumeration values that indicates the style elements that can be present in <paramref name="value" />. A typical value to specify is <see cref="F:System.Globalization.NumberStyles.Integer" />.</param>
      <param name="provider">An object that supplies culture-specific formatting information about <paramref name="value" />.</param>
      <param name="result">When this method returns, contains the <see cref="T:System.Numerics.BigInteger" /> equivalent to the number that is contained in <paramref name="value" />, or <see cref="P:System.Numerics.BigInteger.Zero" /> if the conversion failed. The conversion fails if the <paramref name="value" /> parameter is <see langword="null" /> or is not in a format that is compliant with <paramref name="style" />. This parameter is passed uninitialized.</param>
      <returns>
          <see langword="true" /> if the <paramref name="value" /> parameter was converted successfully; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentException">
              <paramref name="style" /> is not a <see cref="T:System.Globalization.NumberStyles" /> value.-or-
              <paramref name="style" /> includes the <see cref="F:System.Globalization.NumberStyles.AllowHexSpecifier" /> or <see cref="F:System.Globalization.NumberStyles.HexNumber" /> flag along with another value. </exception>
    </member>
    <member name="M:System.Numerics.BigInteger.TryParse(System.String,System.Numerics.BigInteger@)">
      <summary>Tries to convert the string representation of a number to its <see cref="T:System.Numerics.BigInteger" /> equivalent, and returns a value that indicates whether the conversion succeeded.</summary>
      <param name="value">The string representation of a number.</param>
      <param name="result">When this method returns, contains the <see cref="T:System.Numerics.BigInteger" /> equivalent to the number that is contained in <paramref name="value" />, or zero (0) if the conversion fails. The conversion fails if the <paramref name="value" /> parameter is <see langword="null" /> or is not of the correct format. This parameter is passed uninitialized.</param>
      <returns>
          <see langword="true" /> if <paramref name="value" /> was converted successfully; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Numerics.Complex.#ctor(System.Double,System.Double)">
      <summary>Initializes a new instance of the <see cref="T:System.Numerics.Complex" /> structure using the specified real and imaginary values.</summary>
      <param name="real">The real part of the complex number.</param>
      <param name="imaginary">The imaginary part of the complex number.</param>
    </member>
    <member name="M:System.Numerics.Complex.Abs(System.Numerics.Complex)">
      <summary>Gets the absolute value (or magnitude) of a complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The absolute value of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Acos(System.Numerics.Complex)">
      <summary>Returns the angle that is the arc cosine of the specified complex number.</summary>
      <param name="value">A complex number that represents a cosine.</param>
      <returns>The angle, measured in radians, which is the arc cosine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Add(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Adds two complex numbers and returns the result.</summary>
      <param name="left">The first complex number to add.</param>
      <param name="right">The second complex number to add.</param>
      <returns>The sum of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Asin(System.Numerics.Complex)">
      <summary>Returns the angle that is the arc sine of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The angle which is the arc sine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Atan(System.Numerics.Complex)">
      <summary>Returns the angle that is the arc tangent of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The angle that is the arc tangent of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Conjugate(System.Numerics.Complex)">
      <summary>Computes the conjugate of a complex number and returns the result.</summary>
      <param name="value">A complex number.</param>
      <returns>The conjugate of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Cos(System.Numerics.Complex)">
      <summary>Returns the cosine of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The cosine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Cosh(System.Numerics.Complex)">
      <summary>Returns the hyperbolic cosine of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The hyperbolic cosine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Divide(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Divides one complex number by another and returns the result.</summary>
      <param name="dividend">The complex number to be divided.</param>
      <param name="divisor">The complex number to divide by.</param>
      <returns>The quotient of the division.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Equals(System.Numerics.Complex)">
      <summary>Returns a value that indicates whether the current instance and a specified complex number have the same value.</summary>
      <param name="value">The complex number to compare.</param>
      <returns>
          <see langword="true" /> if this complex number and <paramref name="value" /> have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Equals(System.Object)">
      <summary>Returns a value that indicates whether the current instance and a specified object have the same value. </summary>
      <param name="obj">The object to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="obj" /> parameter is a <see cref="T:System.Numerics.Complex" /> object or a type capable of implicit conversion to a <see cref="T:System.Numerics.Complex" /> object, and its value is equal to the current <see cref="T:System.Numerics.Complex" /> object; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Exp(System.Numerics.Complex)">
      <summary>Returns <see langword="e" /> raised to the power specified by a complex number.</summary>
      <param name="value">A complex number that specifies a power.</param>
      <returns>The number <see langword="e" /> raised to the power <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.FromPolarCoordinates(System.Double,System.Double)">
      <summary>Creates a complex number from a point's polar coordinates.</summary>
      <param name="magnitude">The magnitude, which is the distance from the origin (the intersection of the x-axis and the y-axis) to the number.</param>
      <param name="phase">The phase, which is the angle from the line to the horizontal axis, measured in radians.</param>
      <returns>A complex number.</returns>
    </member>
    <member name="M:System.Numerics.Complex.GetHashCode">
      <summary>Returns the hash code for the current <see cref="T:System.Numerics.Complex" /> object.</summary>
      <returns>A 32-bit signed integer hash code.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Log(System.Numerics.Complex)">
      <summary>Returns the natural (base <see langword="e" />) logarithm of a specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The natural (base <see langword="e" />) logarithm of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Log(System.Numerics.Complex,System.Double)">
      <summary>Returns the logarithm of a specified complex number in a specified base.</summary>
      <param name="value">A complex number.</param>
      <param name="baseValue">The base of the logarithm.</param>
      <returns>The logarithm of <paramref name="value" /> in base <paramref name="baseValue" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Log10(System.Numerics.Complex)">
      <summary>Returns the base-10 logarithm of a specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The base-10 logarithm of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Multiply(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Returns the product of two complex numbers.</summary>
      <param name="left">The first complex number to multiply.</param>
      <param name="right">The second complex number to multiply.</param>
      <returns>The product of the <paramref name="left" /> and <paramref name="right" /> parameters.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Negate(System.Numerics.Complex)">
      <summary>Returns the additive inverse of a specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The result of the <see cref="P:System.Numerics.Complex.Real" /> and <see cref="P:System.Numerics.Complex.Imaginary" /> components of the <paramref name="value" /> parameter multiplied by -1.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Addition(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Adds two complex numbers.</summary>
      <param name="left">The first value to add.</param>
      <param name="right">The second value to add.</param>
      <returns>The sum of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Division(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Divides a specified complex number by another specified complex number.</summary>
      <param name="left">The value to be divided.</param>
      <param name="right">The value to divide by.</param>
      <returns>The result of dividing <paramref name="left" /> by <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Equality(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Returns a value that indicates whether two complex numbers are equal.</summary>
      <param name="left">The first complex number to compare.</param>
      <param name="right">The second complex number to compare.</param>
      <returns>
          <see langword="true" /> if the <paramref name="left" /> and <paramref name="right" /> parameters have the same value; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Explicit(System.Decimal)~System.Numerics.Complex">
      <summary>Defines an explicit conversion of a <see cref="T:System.Decimal" /> value to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>A complex number that has a real component equal to <paramref name="value" /> and an imaginary component equal to zero. </returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Explicit(System.Numerics.BigInteger)~System.Numerics.Complex">
      <summary>Defines an explicit conversion of a <see cref="T:System.Numerics.BigInteger" /> value to a complex number. </summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>A complex number that has a real component equal to <paramref name="value" /> and an imaginary component equal to zero. </returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Byte)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of an unsigned byte to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Double)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a double-precision floating-point number to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int16)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 16-bit signed integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int32)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 32-bit signed integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Int64)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 64-bit signed integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.SByte)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a signed byte to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.Single)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a single-precision floating-point number to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt16)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 16-bit unsigned integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt32)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 32-bit unsigned integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Implicit(System.UInt64)~System.Numerics.Complex">
      <summary>Defines an implicit conversion of a 64-bit unsigned integer to a complex number.</summary>
      <param name="value">The value to convert to a complex number.</param>
      <returns>An object that contains the value of the <paramref name="value" /> parameter as its real part and zero as its imaginary part.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Inequality(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Returns a value that indicates whether two complex numbers are not equal.</summary>
      <param name="left">The first value to compare.</param>
      <param name="right">The second value to compare.</param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Multiply(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Multiplies two specified complex numbers.</summary>
      <param name="left">The first value to multiply.</param>
      <param name="right">The second value to multiply.</param>
      <returns>The product of <paramref name="left" /> and <paramref name="right" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_Subtraction(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Subtracts a complex number from another complex number.</summary>
      <param name="left">The value to subtract from (the minuend).</param>
      <param name="right">The value to subtract (the subtrahend).</param>
      <returns>The result of subtracting <paramref name="right" /> from <paramref name="left" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.op_UnaryNegation(System.Numerics.Complex)">
      <summary>Returns the additive inverse of a specified complex number.</summary>
      <param name="value">The value to negate.</param>
      <returns>The result of the <see cref="P:System.Numerics.Complex.Real" /> and <see cref="P:System.Numerics.Complex.Imaginary" /> components of the <paramref name="value" /> parameter multiplied by -1.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Pow(System.Numerics.Complex,System.Double)">
      <summary>Returns a specified complex number raised to a power specified by a double-precision floating-point number.</summary>
      <param name="value">A complex number to be raised to a power.</param>
      <param name="power">A double-precision floating-point number that specifies a power.</param>
      <returns>The complex number <paramref name="value" /> raised to the power <paramref name="power" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Pow(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Returns a specified complex number raised to a power specified by a complex number.</summary>
      <param name="value">A complex number to be raised to a power.</param>
      <param name="power">A complex number that specifies a power.</param>
      <returns>The complex number <paramref name="value" /> raised to the power <paramref name="power" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Reciprocal(System.Numerics.Complex)">
      <summary>Returns the multiplicative inverse of a complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The reciprocal of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Sin(System.Numerics.Complex)">
      <summary>Returns the sine of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The sine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Sinh(System.Numerics.Complex)">
      <summary>Returns the hyperbolic sine of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The hyperbolic sine of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Sqrt(System.Numerics.Complex)">
      <summary>Returns the square root of a specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The square root of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Subtract(System.Numerics.Complex,System.Numerics.Complex)">
      <summary>Subtracts one complex number from another and returns the result.</summary>
      <param name="left">The value to subtract from (the minuend).</param>
      <param name="right">The value to subtract (the subtrahend).</param>
      <returns>The result of subtracting <paramref name="right" /> from <paramref name="left" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Tan(System.Numerics.Complex)">
      <summary>Returns the tangent of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The tangent of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.Tanh(System.Numerics.Complex)">
      <summary>Returns the hyperbolic tangent of the specified complex number.</summary>
      <param name="value">A complex number.</param>
      <returns>The hyperbolic tangent of <paramref name="value" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.ToString">
      <summary>Converts the value of the current complex number to its equivalent string representation in Cartesian form.</summary>
      <returns>The string representation of the current instance in Cartesian form.</returns>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.IFormatProvider)">
      <summary>Converts the value of the current complex number to its equivalent string representation in Cartesian form by using the specified culture-specific formatting information.</summary>
      <param name="provider">An object that supplies culture-specific formatting information.</param>
      <returns>The string representation of the current instance in Cartesian form, as specified by <paramref name="provider" />.</returns>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.String)">
      <summary>Converts the value of the current complex number to its equivalent string representation in Cartesian form by using the specified format for its real and imaginary parts.</summary>
      <param name="format">A standard or custom numeric format string.</param>
      <returns>The string representation of the current instance in Cartesian form.</returns>
      <exception cref="T:System.FormatException">
              <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.Complex.ToString(System.String,System.IFormatProvider)">
      <summary>Converts the value of the current complex number to its equivalent string representation in Cartesian form by using the specified format and culture-specific format information for its real and imaginary parts.</summary>
      <param name="format">A standard or custom numeric format string.</param>
      <param name="provider">An object that supplies culture-specific formatting information.</param>
      <returns>The string representation of the current instance in Cartesian form, as specified by <paramref name="format" /> and <paramref name="provider" />.</returns>
      <exception cref="T:System.FormatException">
              <paramref name="format" /> is not a valid format string.</exception>
    </member>
    <member name="M:System.Numerics.Matrix3x2.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a 3x2 matrix from the specified components. </summary>
      <param name="m11">The value to assign to the first element in the first row. </param>
      <param name="m12">The value to assign to the second element in the first row. </param>
      <param name="m21">The value to assign to the first element in the second row. </param>
      <param name="m22">The value to assign to the second element in the second row. </param>
      <param name="m31">The value to assign to the first element in the third row. </param>
      <param name="m32">The value to assign to the second element in the third row. </param>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Add(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Adds each element in one matrix with its corresponding element in a second matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix that contains the summed values of <paramref name="value1" /> and <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateRotation(System.Single)">
      <summary>Creates a rotation matrix using the given rotation in radians. </summary>
      <param name="radians">The amount of rotation, in radians. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateRotation(System.Single,System.Numerics.Vector2)">
      <summary>Creates a rotation matrix using the specified rotation in radians and a center point. </summary>
      <param name="radians">The amount of rotation, in radians. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Numerics.Vector2)">
      <summary>Creates a scaling matrix from the specified vector scale. </summary>
      <param name="scales">The scale to use. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Creates a scaling matrix from the specified vector scale with an offset from the specified center point. </summary>
      <param name="scales">The scale to use. </param>
      <param name="centerPoint">The center offset. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Single)">
      <summary>Creates a scaling matrix that scales uniformly with the given scale. </summary>
      <param name="scale">The uniform scale to use. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Single,System.Numerics.Vector2)">
      <summary>Creates a scaling matrix that scales uniformly with the specified scale with an offset from the specified center. </summary>
      <param name="scale">The uniform scale to use. </param>
      <param name="centerPoint">The center offset. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Single,System.Single)">
      <summary>Creates a scaling matrix from the specified X and Y components. </summary>
      <param name="xScale">The value to scale by on the X axis. </param>
      <param name="yScale">The value to scale by on the Y axis. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateScale(System.Single,System.Single,System.Numerics.Vector2)">
      <summary>Creates a scaling matrix that is offset by a given center point. </summary>
      <param name="xScale">The value to scale by on the X axis. </param>
      <param name="yScale">The value to scale by on the Y axis. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateSkew(System.Single,System.Single)">
      <summary>Creates a skew matrix from the specified angles in radians. </summary>
      <param name="radiansX">The X angle, in radians. </param>
      <param name="radiansY">The Y angle, in radians. </param>
      <returns>The skew matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateSkew(System.Single,System.Single,System.Numerics.Vector2)">
      <summary>Creates a skew matrix from the specified angles in radians and a center point. </summary>
      <param name="radiansX">The X angle, in radians. </param>
      <param name="radiansY">The Y angle, in radians. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The skew matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateTranslation(System.Numerics.Vector2)">
      <summary>Creates a translation matrix from the specified 2-dimensional vector. </summary>
      <param name="position">The translation position. </param>
      <returns>The translation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.CreateTranslation(System.Single,System.Single)">
      <summary>Creates a translation matrix from the specified X and Y components. </summary>
      <param name="xPosition">The X position. </param>
      <param name="yPosition">The Y position. </param>
      <returns>The translation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Equals(System.Numerics.Matrix3x2)">
      <summary>Returns a value that indicates whether this instance and another 3x2 matrix are equal. </summary>
      <param name="other">The other matrix. </param>
      <returns>
          <see langword="true" /> if the two matrices are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal. </summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.GetDeterminant">
      <summary>Calculates the determinant for this matrix. </summary>
      <returns>The determinant. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Invert(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2@)">
      <summary>Inverts the specified matrix. The return value indicates whether the operation succeeded. </summary>
      <param name="matrix">The matrix to invert. </param>
      <param name="result">When this method returns, contains the inverted matrix if the operation succeeded. </param>
      <returns>
          <see langword="true" /> if <paramref name="matrix" /> was converted successfully; otherwise,  <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Lerp(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2,System.Single)">
      <summary>Performs a linear interpolation from one matrix to a second matrix based on a value that specifies the weighting of the second matrix. </summary>
      <param name="matrix1">The first matrix. </param>
      <param name="matrix2">The second matrix. </param>
      <param name="amount">The relative weighting of <paramref name="matrix2" />. </param>
      <returns>The interpolated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Multiply(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Returns the matrix that results from multiplying two matrices together. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The product matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Multiply(System.Numerics.Matrix3x2,System.Single)">
      <summary>Returns the matrix that results from scaling all the elements of a specified matrix by a scalar factor. </summary>
      <param name="value1">The matrix to scale. </param>
      <param name="value2">The scaling value to use. </param>
      <returns>The scaled matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Negate(System.Numerics.Matrix3x2)">
      <summary>Negates the specified matrix by multiplying all its values by -1. </summary>
      <param name="value">The matrix to negate. </param>
      <returns>The negated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Addition(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Adds each element in one matrix with its corresponding element in a second matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix that contains the summed values. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Equality(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Returns a value that indicates whether the specified matrices are equal. </summary>
      <param name="value1">The first matrix to compare. </param>
      <param name="value2">The second matrix to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Inequality(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Returns a value that indicates whether the specified matrices are not equal. </summary>
      <param name="value1">The first matrix to compare. </param>
      <param name="value2">The second matrix to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Multiply(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Returns the matrix that results from multiplying two matrices together. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The product matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Multiply(System.Numerics.Matrix3x2,System.Single)">
      <summary>Returns the matrix that results from scaling all the elements of a specified matrix by a scalar factor. </summary>
      <param name="value1">The matrix to scale. </param>
      <param name="value2">The scaling value to use. </param>
      <returns>The scaled matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_Subtraction(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Subtracts each element in a second matrix from its corresponding element in a first matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.op_UnaryNegation(System.Numerics.Matrix3x2)">
      <summary>Negates the specified matrix by multiplying all its values by -1. </summary>
      <param name="value">The matrix to negate. </param>
      <returns>The negated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.Subtract(System.Numerics.Matrix3x2,System.Numerics.Matrix3x2)">
      <summary>Subtracts each element in a second matrix from its corresponding element in a first matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix3x2.ToString">
      <summary>Returns a string that represents this matrix. </summary>
      <returns>The string representation of this matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.#ctor(System.Numerics.Matrix3x2)">
      <summary>Creates a <see cref="T:System.Numerics.Matrix4x4" /> object from a specified <see cref="T:System.Numerics.Matrix3x2" /> object. </summary>
      <param name="value">A 3x2 matrix. </param>
    </member>
    <member name="M:System.Numerics.Matrix4x4.#ctor(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a 4x4 matrix from the specified components. </summary>
      <param name="m11">The value to assign to the first element in the first row. </param>
      <param name="m12">The value to assign to the second element in the first row. </param>
      <param name="m13">The value to assign to the third element in the first row. </param>
      <param name="m14">The value to assign to the fourth element in the first row. </param>
      <param name="m21">The value to assign to the first element in the second row. </param>
      <param name="m22">The value to assign to the second element in the second row. </param>
      <param name="m23">The value to assign to the third element in the second row. </param>
      <param name="m24">The value to assign to the third element in the second row. </param>
      <param name="m31">The value to assign to the first element in the third row.</param>
      <param name="m32">The value to assign to the second element in the third row. </param>
      <param name="m33">The value to assign to the third element in the third row. </param>
      <param name="m34">The value to assign to the fourth element in the third row. </param>
      <param name="m41">The value to assign to the first element in the fourth row. </param>
      <param name="m42">The value to assign to the second element in the fourth row. </param>
      <param name="m43">The value to assign to the third element in the fourth row. </param>
      <param name="m44">The value to assign to the fourth element in the fourth row. </param>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Add(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Adds each element in one matrix with its corresponding element in a second matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix that contains the summed values of <paramref name="value1" /> and <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateBillboard(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a spherical billboard that rotates around a specified object position. </summary>
      <param name="objectPosition">The position of the object that the billboard will rotate around. </param>
      <param name="cameraPosition">The position of the camera. </param>
      <param name="cameraUpVector">The up vector of the camera. </param>
      <param name="cameraForwardVector">The forward vector of the camera. </param>
      <returns>The created billboard. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateConstrainedBillboard(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a cylindrical billboard that rotates around a specified axis. </summary>
      <param name="objectPosition">The position of the object that the billboard will rotate around. </param>
      <param name="cameraPosition">The position of the camera. </param>
      <param name="rotateAxis">The axis to rotate the billboard around. </param>
      <param name="cameraForwardVector">The forward vector of the camera. </param>
      <param name="objectForwardVector">The forward vector of the object. </param>
      <returns>The billboard matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateFromAxisAngle(System.Numerics.Vector3,System.Single)">
      <summary>Creates a matrix that rotates around an arbitrary vector. </summary>
      <param name="axis">The axis to rotate around. </param>
      <param name="angle">The angle to rotate around <paramref name="axis" />, in radians. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateFromQuaternion(System.Numerics.Quaternion)">
      <summary>Creates a rotation matrix from the specified Quaternion rotation value. </summary>
      <param name="quaternion">The source Quaternion. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">
      <summary>Creates a rotation matrix from the specified yaw, pitch, and roll. </summary>
      <param name="yaw">The angle of rotation, in radians, around the Y axis. </param>
      <param name="pitch">The angle of rotation, in radians, around the X axis. </param>
      <param name="roll">The angle of rotation, in radians, around the Z axis. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateLookAt(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a view matrix. </summary>
      <param name="cameraPosition">The position of the camera. </param>
      <param name="cameraTarget">The target towards which the camera is pointing. </param>
      <param name="cameraUpVector">The direction that is "up" from the camera's point of view. </param>
      <returns>The view matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateOrthographic(System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates an orthographic perspective matrix from the given view volume dimensions. </summary>
      <param name="width">The width of the view volume. </param>
      <param name="height">The height of the view volume. </param>
      <param name="zNearPlane">The minimum Z-value of the view volume. </param>
      <param name="zFarPlane">The maximum Z-value of the view volume. </param>
      <returns>The orthographic projection matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateOrthographicOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a customized orthographic projection matrix. </summary>
      <param name="left">The minimum X-value of the view volume. </param>
      <param name="right">The maximum X-value of the view volume. </param>
      <param name="bottom">The minimum Y-value of the view volume. </param>
      <param name="top">The maximum Y-value of the view volume. </param>
      <param name="zNearPlane">The minimum Z-value of the view volume. </param>
      <param name="zFarPlane">The maximum Z-value of the view volume. </param>
      <returns>The orthographic projection matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreatePerspective(System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a perspective projection matrix from the given view volume dimensions. </summary>
      <param name="width">The width of the view volume at the near view plane. </param>
      <param name="height">The height of the view volume at the near view plane. </param>
      <param name="nearPlaneDistance">The distance to the near view plane. </param>
      <param name="farPlaneDistance">The distance to the far view plane. </param>
      <returns>The perspective projection matrix. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="nearPlaneDistance" /> is less than or equal to zero.-or-
              <paramref name="farPlaneDistance" /> is less than or equal to zero. -or-
              <paramref name="nearPlaneDistance" /> is greater than or equal to <paramref name="farPlaneDistance" />. </exception>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreatePerspectiveFieldOfView(System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a perspective projection matrix based on a field of view, aspect ratio, and near and far view plane distances. </summary>
      <param name="fieldOfView">The field of view in the y direction, in radians. </param>
      <param name="aspectRatio">The aspect ratio, defined as view space width divided by height. </param>
      <param name="nearPlaneDistance">The distance to the near view plane. </param>
      <param name="farPlaneDistance">The distance to the far view plane. </param>
      <returns>The perspective projection matrix. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="fieldOfView" /> is less than or equal to zero. -or-
              <paramref name="fieldOfView" /> is greater than or equal to <see cref="F:System.Math.PI" />. 
              <paramref name="nearPlaneDistance" /> is less than or equal to zero.-or-
              <paramref name="farPlaneDistance" /> is less than or equal to zero. -or-
              <paramref name="nearPlaneDistance" /> is greater than or equal to <paramref name="farPlaneDistance" />. </exception>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreatePerspectiveOffCenter(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a customized perspective projection matrix. </summary>
      <param name="left">The minimum x-value of the view volume at the near view plane. </param>
      <param name="right">The maximum x-value of the view volume at the near view plane. </param>
      <param name="bottom">The minimum y-value of the view volume at the near view plane. </param>
      <param name="top">The maximum y-value of the view volume at the near view plane. </param>
      <param name="nearPlaneDistance">The distance to the near view plane. </param>
      <param name="farPlaneDistance">The distance to the far view plane. </param>
      <returns>The perspective projection matrix. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="nearPlaneDistance" /> is less than or equal to zero.-or-
              <paramref name="farPlaneDistance" /> is less than or equal to zero. -or-
              <paramref name="nearPlaneDistance" /> is greater than or equal to <paramref name="farPlaneDistance" />. </exception>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateReflection(System.Numerics.Plane)">
      <summary>Creates a matrix that reflects the coordinate system about a specified plane. </summary>
      <param name="value">The plane about which to create a reflection. </param>
      <returns>A new matrix expressing the reflection. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationX(System.Single)">
      <summary>Creates a matrix for rotating points around the X axis. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the X axis. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationX(System.Single,System.Numerics.Vector3)">
      <summary>Creates a matrix for rotating points around the X axis from a center point. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the X axis.</param>
      <param name="centerPoint">The center point. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationY(System.Single)">
      <summary>Creates a matrix for rotating points around the Y axis. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the Y-axis. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationY(System.Single,System.Numerics.Vector3)">
      <summary>The amount, in radians, by which to rotate around the Y axis from a center point. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the Y-axis. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationZ(System.Single)">
      <summary>Creates a matrix for rotating points around the Z axis. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the Z-axis. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateRotationZ(System.Single,System.Numerics.Vector3)">
      <summary>Creates a matrix for rotating points around the Z axis from a center point. </summary>
      <param name="radians">The amount, in radians, by which to rotate around the Z-axis. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The rotation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Numerics.Vector3)">
      <summary>Creates a scaling matrix from the specified vector scale. </summary>
      <param name="scales">The scale to use. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a scaling matrix with a center point. </summary>
      <param name="scales">The vector that contains the amount to scale on each axis. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Single)">
      <summary>Creates a uniform scaling matrix that scale equally on each axis. </summary>
      <param name="scale">The uniform scaling factor. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Single,System.Numerics.Vector3)">
      <summary>Creates a uniform scaling matrix that scales equally on each axis with a center point. </summary>
      <param name="scale">The uniform scaling factor. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Single,System.Single,System.Single)">
      <summary>Creates a scaling matrix from the specified X, Y, and Z components.</summary>
      <param name="xScale">The value to scale by on the X axis. </param>
      <param name="yScale">The value to scale by on the Y axis. </param>
      <param name="zScale">The value to scale by on the Z axis. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateScale(System.Single,System.Single,System.Single,System.Numerics.Vector3)">
      <summary>Creates a scaling matrix that is offset by a given center point. </summary>
      <param name="xScale">The value to scale by on the X axis. </param>
      <param name="yScale">The value to scale by on the Y axis. </param>
      <param name="zScale">The value to scale by on the Z axis. </param>
      <param name="centerPoint">The center point. </param>
      <returns>The scaling matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateShadow(System.Numerics.Vector3,System.Numerics.Plane)">
      <summary>Creates a matrix that flattens geometry into a specified plane as if casting a shadow from a specified light source. </summary>
      <param name="lightDirection">The direction from which the light that will cast the shadow is coming. </param>
      <param name="plane">The plane onto which the new matrix should flatten geometry so as to cast a shadow. </param>
      <returns>A new matrix that can be used to flatten geometry onto the specified plane from the specified direction. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateTranslation(System.Numerics.Vector3)">
      <summary>Creates a translation matrix from the specified 3-dimensional vector. </summary>
      <param name="position">The amount to translate in each axis. </param>
      <returns>The translation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateTranslation(System.Single,System.Single,System.Single)">
      <summary>Creates a translation matrix from the specified X, Y, and Z components. </summary>
      <param name="xPosition">The amount to translate on the X axis. </param>
      <param name="yPosition">The amount to translate on the Y axis. </param>
      <param name="zPosition">The amount to translate on the Z axis. </param>
      <returns>The translation matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.CreateWorld(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a world matrix with the specified parameters. </summary>
      <param name="position">The position of the object. </param>
      <param name="forward">The forward direction of the object. </param>
      <param name="up">The upward direction of the object. Its value is usually [0, 1, 0]. </param>
      <returns>The world matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Decompose(System.Numerics.Matrix4x4,System.Numerics.Vector3@,System.Numerics.Quaternion@,System.Numerics.Vector3@)">
      <summary>Attempts to extract the scale, translation, and rotation components from the given scale, rotation, or translation matrix. The return value indicates whether the operation succeeded. </summary>
      <param name="matrix">The source matrix. </param>
      <param name="scale">When this method returns, contains the scaling component of the transformation matrix if the operation succeeded. </param>
      <param name="rotation">When this method returns, contains the rotation component of the transformation matrix if the operation succeeded. </param>
      <param name="translation">When the method returns, contains the translation component of the transformation matrix if the operation succeeded. </param>
      <returns>
          <see langword="true" /> if <paramref name="matrix" /> was decomposed successfully; otherwise,  <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Equals(System.Numerics.Matrix4x4)">
      <summary>Returns a value that indicates whether this instance and another 4x4 matrix are equal. </summary>
      <param name="other">The other matrix. </param>
      <returns>
          <see langword="true" /> if the two matrices are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal. </summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.GetDeterminant">
      <summary>Calculates the determinant of the current 4x4 matrix. </summary>
      <returns>The determinant. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Invert(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4@)">
      <summary>Inverts the specified matrix. The return value indicates whether the operation succeeded. </summary>
      <param name="matrix">The matrix to invert. </param>
      <param name="result">When this method returns, contains the inverted matrix if the operation succeeded. </param>
      <returns>
          <see langword="true" /> if <paramref name="matrix" /> was converted successfully; otherwise,  <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Lerp(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4,System.Single)">
      <summary>Performs a linear interpolation from one matrix to a second matrix based on a value that specifies the weighting of the second matrix. </summary>
      <param name="matrix1">The first matrix. </param>
      <param name="matrix2">The second matrix. </param>
      <param name="amount">The relative weighting of <paramref name="matrix2" />. </param>
      <returns>The interpolated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Multiply(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Returns the matrix that results from multiplying two matrices together. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The product matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Multiply(System.Numerics.Matrix4x4,System.Single)">
      <summary>Returns the matrix that results from scaling all the elements of a specified matrix by a scalar factor. </summary>
      <param name="value1">The matrix to scale. </param>
      <param name="value2">The scaling value to use. </param>
      <returns>The scaled matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Negate(System.Numerics.Matrix4x4)">
      <summary>Negates the specified matrix by multiplying all its values by -1. </summary>
      <param name="value">The matrix to negate. </param>
      <returns>The negated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Addition(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Adds each element in one matrix with its corresponding element in a second matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix that contains the summed values. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Equality(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Returns a value that indicates whether the specified matrices are equal. </summary>
      <param name="value1">The first matrix to compare. </param>
      <param name="value2">The second matrix to care </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Inequality(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Returns a value that indicates whether the specified matrices are not equal. </summary>
      <param name="value1">The first matrix to compare. </param>
      <param name="value2">The second matrix to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Multiply(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Returns the matrix that results from multiplying two matrices together. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The product matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Multiply(System.Numerics.Matrix4x4,System.Single)">
      <summary>Returns the matrix that results from scaling all the elements of a specified matrix by a scalar factor. </summary>
      <param name="value1">The matrix to scale. </param>
      <param name="value2">The scaling value to use. </param>
      <returns>The scaled matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_Subtraction(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Subtracts each element in a second matrix from its corresponding element in a first matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.op_UnaryNegation(System.Numerics.Matrix4x4)">
      <summary>Negates the specified matrix by multiplying all its values by -1. </summary>
      <param name="value">The matrix to negate. </param>
      <returns>The negated matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Subtract(System.Numerics.Matrix4x4,System.Numerics.Matrix4x4)">
      <summary>Subtracts each element in a second matrix from its corresponding element in a first matrix. </summary>
      <param name="value1">The first matrix. </param>
      <param name="value2">The second matrix. </param>
      <returns>The matrix containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.ToString">
      <summary>Returns a string that represents this matrix. </summary>
      <returns>The string representation of this matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Transform(System.Numerics.Matrix4x4,System.Numerics.Quaternion)">
      <summary>Transforms the specified matrix by applying the specified Quaternion rotation. </summary>
      <param name="value">The matrix to transform. </param>
      <param name="rotation">The rotation t apply. </param>
      <returns>The transformed matrix. </returns>
    </member>
    <member name="M:System.Numerics.Matrix4x4.Transpose(System.Numerics.Matrix4x4)">
      <summary>Transposes the rows and columns of a matrix. </summary>
      <param name="matrix">The matrix to transpose. </param>
      <returns>The transposed matrix. </returns>
    </member>
    <member name="M:System.Numerics.Plane.#ctor(System.Numerics.Vector3,System.Single)">
      <summary>Creates a <see cref="T:System.Numerics.Plane" /> object from a specified normal and the distance along the normal from the origin. </summary>
      <param name="normal">The plane's normal vector. </param>
      <param name="d">The plane's distance from the origin along its normal vector. </param>
    </member>
    <member name="M:System.Numerics.Plane.#ctor(System.Numerics.Vector4)">
      <summary>Creates a <see cref="T:System.Numerics.Plane" /> object from a specified four-dimensional vector. </summary>
      <param name="value">A vector whose first three elements describe the normal vector, and whose <see cref="F:System.Numerics.Vector4.W" /> defines the distance along that normal from the origin. </param>
    </member>
    <member name="M:System.Numerics.Plane.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a <see cref="T:System.Numerics.Plane" /> object from the X, Y, and Z components of its normal, and its distance from the origin on that normal.  </summary>
      <param name="x">The X component of the normal. </param>
      <param name="y">The Y component of the normal. </param>
      <param name="z">The Z component of the normal. </param>
      <param name="d">The distance of the plane along its normal from the origin. </param>
    </member>
    <member name="M:System.Numerics.Plane.CreateFromVertices(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Creates a <see cref="T:System.Numerics.Plane" /> object that contains three specified points. </summary>
      <param name="point1">The first point defining the plane. </param>
      <param name="point2">The second point defining the plane. </param>
      <param name="point3">The third point defining the plane. </param>
      <returns>The plane containing the three points. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Dot(System.Numerics.Plane,System.Numerics.Vector4)">
      <summary>Calculates the dot product of a plane and a 4-dimensional vector. </summary>
      <param name="plane">The plane. </param>
      <param name="value">The four-dimensional vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Plane.DotCoordinate(System.Numerics.Plane,System.Numerics.Vector3)">
      <summary>Returns the dot product of a specified three-dimensional vector and the normal vector of this plane plus the distance (<see cref="F:System.Numerics.Plane.D" />) value of the plane.</summary>
      <param name="plane">The plane. </param>
      <param name="value">The 3-dimensional vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Plane.DotNormal(System.Numerics.Plane,System.Numerics.Vector3)">
      <summary>Returns the dot product of a specified three-dimensional vector and the <see cref="F:System.Numerics.Plane.Normal" /> vector of this plane.</summary>
      <param name="plane">The plane. </param>
      <param name="value">The three-dimensional vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Equals(System.Numerics.Plane)">
      <summary>Returns a value that indicates whether this instance and another plane object are equal.</summary>
      <param name="other">The other plane. </param>
      <returns>
          <see langword="true" /> if the two planes are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal. </summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Plane.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Normalize(System.Numerics.Plane)">
      <summary>Creates a new <see cref="T:System.Numerics.Plane" /> object whose normal vector is the source plane's normal vector normalized. </summary>
      <param name="value">The source plane. </param>
      <returns>The normalized plane. </returns>
    </member>
    <member name="M:System.Numerics.Plane.op_Equality(System.Numerics.Plane,System.Numerics.Plane)">
      <summary>Returns a value that indicates whether two planes are equal.  </summary>
      <param name="value1">The first plane to compare. </param>
      <param name="value2">The second plane to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Plane.op_Inequality(System.Numerics.Plane,System.Numerics.Plane)">
      <summary>Returns a value that indicates whether two planes are not equal.  </summary>
      <param name="value1">The first plane to compare. </param>
      <param name="value2">The second plane to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Plane.ToString">
      <summary>Returns the string representation of this plane object. </summary>
      <returns>A string that represents this <see cref="T:System.Numerics.Plane" /> object. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Transform(System.Numerics.Plane,System.Numerics.Matrix4x4)">
      <summary>Transforms a normalized plane by a 4x4 matrix. </summary>
      <param name="plane">The normalized plane to transform. </param>
      <param name="matrix">The transformation matrix to apply to <paramref name="plane" />. </param>
      <returns>The transformed plane. </returns>
    </member>
    <member name="M:System.Numerics.Plane.Transform(System.Numerics.Plane,System.Numerics.Quaternion)">
      <summary>Transforms a normalized plane by a Quaternion rotation. </summary>
      <param name="plane">The normalized plane to transform. </param>
      <param name="rotation">The Quaternion rotation to apply to the plane. </param>
      <returns>A new plane that results from applying the Quaternion rotation. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.#ctor(System.Numerics.Vector3,System.Single)">
      <summary>Creates a quaternion from the specified vector and rotation parts. </summary>
      <param name="vectorPart">The vector part of the quaternion. </param>
      <param name="scalarPart">The rotation part of the quaternion. </param>
    </member>
    <member name="M:System.Numerics.Quaternion.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>Constructs a quaternion from the specified components.</summary>
      <param name="x">The value to assign to the X component of the quaternion. </param>
      <param name="y">The value to assign to the Y component of the quaternion.</param>
      <param name="z">The value to assign to the Z component of the quaternion.</param>
      <param name="w">The value to assign to the W component of the quaternion.</param>
    </member>
    <member name="M:System.Numerics.Quaternion.Add(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Adds each element in one quaternion with its corresponding element in a second quaternion.</summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The quaternion that contains the summed values of <paramref name="value1" /> and <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Concatenate(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Concatenates two quaternions. </summary>
      <param name="value1">The first quaternion rotation in the series. </param>
      <param name="value2">The second quaternion rotation in the series. </param>
      <returns>A new quaternion representing the concatenation of the <paramref name="value1" /> rotation followed by the <paramref name="value2" /> rotation. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Conjugate(System.Numerics.Quaternion)">
      <summary>Returns the conjugate of a specified quaternion. </summary>
      <param name="value">The quaternion. </param>
      <returns>A new quaternion that is the conjugate of <see langword="value" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.CreateFromAxisAngle(System.Numerics.Vector3,System.Single)">
      <summary>Creates a quaternion from a vector and an angle to rotate about the vector. </summary>
      <param name="axis">The vector to rotate around. </param>
      <param name="angle">The angle, in radians, to rotate around the vector. </param>
      <returns>The newly created quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.CreateFromRotationMatrix(System.Numerics.Matrix4x4)">
      <summary>Creates a quaternion from the specified rotation matrix. </summary>
      <param name="matrix">The rotation matrix. </param>
      <returns>The newly created quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.CreateFromYawPitchRoll(System.Single,System.Single,System.Single)">
      <summary>Creates a new quaternion from the given yaw, pitch, and roll. </summary>
      <param name="yaw">The yaw angle, in radians, around the Y axis. </param>
      <param name="pitch">The pitch angle, in radians, around the X axis. </param>
      <param name="roll">The roll angle, in radians, around the Z axis. </param>
      <returns>The resulting quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Divide(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Divides one quaternion by a second quaternion. </summary>
      <param name="value1">The dividend. </param>
      <param name="value2">The divisor. </param>
      <returns>The quaternion that results from dividing <paramref name="value1" /> by <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Dot(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Calculates the dot product of two quaternions. </summary>
      <param name="quaternion1">The first quaternion. </param>
      <param name="quaternion2">The second quaternion. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Equals(System.Numerics.Quaternion)">
      <summary>Returns a value that indicates whether this instance and another quaternion are equal. </summary>
      <param name="other">The other quaternion. </param>
      <returns>
          <see langword="true" /> if the two quaternions are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal. </summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Inverse(System.Numerics.Quaternion)">
      <summary>Returns the inverse of a quaternion. </summary>
      <param name="value">The quaternion. </param>
      <returns>The inverted quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Length">
      <summary>Calculates the length of the quaternion. </summary>
      <returns>The computed length of the quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.LengthSquared">
      <summary>Calculates the squared length of the quaternion. </summary>
      <returns>The length squared of the quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Lerp(System.Numerics.Quaternion,System.Numerics.Quaternion,System.Single)">
      <summary>Performs a linear interpolation between two quaternions based on a value that specifies the weighting of the second quaternion. </summary>
      <param name="quaternion1">The first quaternion. </param>
      <param name="quaternion2">The second quaternion. </param>
      <param name="amount">The relative weight of <paramref name="quaternion2" /> in the interpolation. </param>
      <returns>The interpolated quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Multiply(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Returns the quaternion that results from multiplying two quaternions together. </summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The product quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Multiply(System.Numerics.Quaternion,System.Single)">
      <summary>Returns the quaternion that results from scaling all the components of a specified quaternion by a scalar factor. </summary>
      <param name="value1">The source quaternion. </param>
      <param name="value2">The scalar value. </param>
      <returns>The scaled quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Negate(System.Numerics.Quaternion)">
      <summary>Reverses the sign of each component of the quaternion. </summary>
      <param name="value">The quaternion to negate. </param>
      <returns>The negated quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Normalize(System.Numerics.Quaternion)">
      <summary>Divides each component of a specified <see cref="T:System.Numerics.Quaternion" /> by its length. </summary>
      <param name="value">The quaternion to normalize. </param>
      <returns>The normalized quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Addition(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Adds each element in one quaternion with its corresponding element in a second quaternion. </summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The quaternion that contains the summed values of <paramref name="value1" /> and <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Division(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Divides one quaternion by a second quaternion. </summary>
      <param name="value1">The dividend. </param>
      <param name="value2">The divisor. </param>
      <returns>The quaternion that results from dividing <paramref name="value1" /> by <paramref name="value2" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Equality(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Returns a value that indicates whether two quaternions are equal. </summary>
      <param name="value1">The first quaternion to compare. </param>
      <param name="value2">The second quaternion to compare. </param>
      <returns>
          <see langword="true" /> if the two quaternions are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Inequality(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Returns a value that indicates whether two quaternions are not equal. </summary>
      <param name="value1">The first quaternion to compare. </param>
      <param name="value2">The second quaternion to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="value1" /> and <paramref name="value2" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Multiply(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Returns the quaternion that results from multiplying two quaternions together. </summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The product quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Multiply(System.Numerics.Quaternion,System.Single)">
      <summary>Returns the quaternion that results from scaling all the components of a specified quaternion by a scalar factor. </summary>
      <param name="value1">The source quaternion. </param>
      <param name="value2">The scalar value. </param>
      <returns>The scaled quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_Subtraction(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Subtracts each element in a second quaternion from its corresponding element in a first quaternion. </summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The quaternion containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.op_UnaryNegation(System.Numerics.Quaternion)">
      <summary>Reverses the sign of each component of the quaternion. </summary>
      <param name="value">The quaternion to negate. </param>
      <returns>The negated quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Slerp(System.Numerics.Quaternion,System.Numerics.Quaternion,System.Single)">
      <summary>Interpolates between two quaternions, using spherical linear interpolation. </summary>
      <param name="quaternion1">The first quaternion. </param>
      <param name="quaternion2">The second quaternion. </param>
      <param name="amount">The relative weight of the second quaternion in the interpolation. </param>
      <returns>The interpolated quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.Subtract(System.Numerics.Quaternion,System.Numerics.Quaternion)">
      <summary>Subtracts each element in a second quaternion from its corresponding element in a first quaternion. </summary>
      <param name="value1">The first quaternion. </param>
      <param name="value2">The second quaternion. </param>
      <returns>The quaternion containing the values that result from subtracting each element in <paramref name="value2" /> from its corresponding element in <paramref name="value1" />. </returns>
    </member>
    <member name="M:System.Numerics.Quaternion.ToString">
      <summary>Returns a string that represents this quaternion. </summary>
      <returns>The string representation of this quaternion. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.#ctor(System.Single)">
      <summary>Creates a new <see cref="T:System.Numerics.Vector2" /> object whose two elements have the same value.</summary>
      <param name="value">The value to assign to both elements. </param>
    </member>
    <member name="M:System.Numerics.Vector2.#ctor(System.Single,System.Single)">
      <summary>Creates a vector whose elements have the specified values. </summary>
      <param name="x">The value to assign to the <see cref="F:System.Numerics.Vector2.X" /> field. </param>
      <param name="y">The value to assign to the <see cref="F:System.Numerics.Vector2.Y" /> field. </param>
    </member>
    <member name="M:System.Numerics.Vector2.Abs(System.Numerics.Vector2)">
      <summary>Returns a vector whose elements are the absolute values of each of the specified vector's elements. </summary>
      <param name="value">A vector. </param>
      <returns>The absolute value vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Add(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Clamp(System.Numerics.Vector2,System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Restricts a vector between a minimum and a maximum value. </summary>
      <param name="value1">The vector to restrict. </param>
      <param name="min">The minimum value. </param>
      <param name="max">The maximum value. </param>
      <returns>The restricted vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.CopyTo(System.Single[])">
      <summary>Copies the elements of the vector to a specified array. </summary>
      <param name="array">The destination array. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector2.CopyTo(System.Single[],System.Int32)">
      <summary>Copies the elements of the vector to a specified array starting at a specified index position.</summary>
      <param name="array">The destination array.</param>
      <param name="index">The index at which to copy the first element of the vector. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="index" /> is less than zero.-or-
              <paramref name="index" /> is greater than or equal to the array length. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector2.Distance(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Computes the Euclidean distance between the two given points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.DistanceSquared(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns the Euclidean distance squared between two specified points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Divide(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector resulting from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Divide(System.Numerics.Vector2,System.Single)">
      <summary>Divides the specified vector by a specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="divisor">The scalar value. </param>
      <returns>The vector that results from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Dot(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns the dot product of two vectors. </summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Equals(System.Numerics.Vector2)">
      <summary>Returns a value that indicates whether this instance and another vector are equal. </summary>
      <param name="other">The other vector. </param>
      <returns>
          <see langword="true" /> if the two vectors are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal.</summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Length">
      <summary>Returns the length of the vector. </summary>
      <returns>The vector's length. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.LengthSquared">
      <summary>Returns the length of the vector squared. </summary>
      <returns>The vector's length squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Lerp(System.Numerics.Vector2,System.Numerics.Vector2,System.Single)">
      <summary>Performs a linear interpolation between two vectors based on the given weighting. </summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <param name="amount">A value between 0 and 1 that indicates the weight of <paramref name="value2" />. </param>
      <returns>The interpolated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Max(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns a vector whose elements are the maximum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The maximized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Min(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns a vector whose elements are the minimum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The minimized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Multiply(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Multiply(System.Numerics.Vector2,System.Single)">
      <summary>Multiplies a vector by a specified scalar. </summary>
      <param name="left">The vector to multiply. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Multiply(System.Single,System.Numerics.Vector2)">
      <summary>Multiplies a scalar value by a specified vector.</summary>
      <param name="left">The scaled value. </param>
      <param name="right">The vector. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Negate(System.Numerics.Vector2)">
      <summary>Negates a specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Normalize(System.Numerics.Vector2)">
      <summary>Returns a vector with the same direction as the specified vector, but with a length of one. </summary>
      <param name="value">The vector to normalize. </param>
      <returns>The normalized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Addition(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Division(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from dividing <paramref name="left" /> by <paramref name="right" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Division(System.Numerics.Vector2,System.Single)">
      <summary>Divides the specified vector by a specified scalar value.</summary>
      <param name="value1">The vector. </param>
      <param name="value2">The scalar value. </param>
      <returns>The result of the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Equality(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns a value that indicates whether each pair of elements in two specified vectors is equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Inequality(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns a value that indicates whether two specified vectors are not equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Multiply(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Multiply(System.Numerics.Vector2,System.Single)">
      <summary>Multiples the specified vector by the specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Multiply(System.Single,System.Numerics.Vector2)">
      <summary>Multiples the scalar value by the specified vector. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_Subtraction(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from subtracting <paramref name="right" /> from <paramref name="left" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.op_UnaryNegation(System.Numerics.Vector2)">
      <summary>Negates the specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Reflect(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Returns the reflection of a vector off a surface that has the specified normal. </summary>
      <param name="vector">The source vector. </param>
      <param name="normal">The normal of the surface being reflected off. </param>
      <returns>The reflected vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.SquareRoot(System.Numerics.Vector2)">
      <summary>Returns a vector whose elements are the square root of each of a specified vector's elements.</summary>
      <param name="value">A vector. </param>
      <returns>The square root vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Subtract(System.Numerics.Vector2,System.Numerics.Vector2)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The difference vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.ToString">
      <summary>Returns the string representation of the current instance using default formatting. </summary>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.ToString(System.String)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements. </summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements.</param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.ToString(System.String,System.IFormatProvider)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements and the specified format provider to define culture-specific formatting.</summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements. </param>
      <param name="formatProvider">A format provider that supplies culture-specific formatting information. </param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Transform(System.Numerics.Vector2,System.Numerics.Matrix3x2)">
      <summary>Transforms a vector by a specified 3x2 matrix. </summary>
      <param name="position">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Transform(System.Numerics.Vector2,System.Numerics.Matrix4x4)">
      <summary>Transforms a vector by a specified 4x4 matrix. </summary>
      <param name="position">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.Transform(System.Numerics.Vector2,System.Numerics.Quaternion)">
      <summary>Transforms a vector by the specified Quaternion rotation value. </summary>
      <param name="value">The vector to rotate. </param>
      <param name="rotation">The rotation to apply. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.TransformNormal(System.Numerics.Vector2,System.Numerics.Matrix3x2)">
      <summary>Transforms a vector normal by the given 3x2 matrix. </summary>
      <param name="normal">The source vector. </param>
      <param name="matrix">The matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector2.TransformNormal(System.Numerics.Vector2,System.Numerics.Matrix4x4)">
      <summary>Transforms a vector normal by the given 4x4 matrix. </summary>
      <param name="normal">The source vector. </param>
      <param name="matrix">The matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.#ctor(System.Numerics.Vector2,System.Single)">
      <summary>Creates a   new <see cref="T:System.Numerics.Vector3" /> object from the specified <see cref="T:System.Numerics.Vector2" /> object and the specified value. </summary>
      <param name="value">The vector with two elements. </param>
      <param name="z">The additional value to assign to the <see cref="F:System.Numerics.Vector3.Z" /> field. </param>
    </member>
    <member name="M:System.Numerics.Vector3.#ctor(System.Single)">
      <summary>Creates a new <see cref="T:System.Numerics.Vector3" /> object whose three elements have the same value.</summary>
      <param name="value">The value to assign to all three elements. </param>
    </member>
    <member name="M:System.Numerics.Vector3.#ctor(System.Single,System.Single,System.Single)">
      <summary>Creates a vector whose elements have the specified values. </summary>
      <param name="x">The value to assign to the <see cref="F:System.Numerics.Vector3.X" /> field. </param>
      <param name="y">The value to assign to the <see cref="F:System.Numerics.Vector3.Y" /> field. </param>
      <param name="z">The value to assign to the <see cref="F:System.Numerics.Vector3.Z" /> field. </param>
    </member>
    <member name="M:System.Numerics.Vector3.Abs(System.Numerics.Vector3)">
      <summary>Returns a vector whose elements are the absolute values of each of the specified vector's elements. </summary>
      <param name="value">A vector. </param>
      <returns>The absolute value vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Add(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Clamp(System.Numerics.Vector3,System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Restricts a vector between a minimum and a maximum value. </summary>
      <param name="value1">The vector to restrict. </param>
      <param name="min">The minimum value. </param>
      <param name="max">The maximum value. </param>
      <returns>The restricted vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.CopyTo(System.Single[])">
      <summary>Copies the elements of the vector to a specified array. </summary>
      <param name="array">The destination array. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector3.CopyTo(System.Single[],System.Int32)">
      <summary>Copies the elements of the vector to a specified array starting at a specified index position. </summary>
      <param name="array">The destination array.</param>
      <param name="index">The index at which to copy the first element of the vector. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="index" /> is less than zero.-or-
              <paramref name="index" /> is greater than or equal to the array length. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector3.Cross(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Computes the cross product of two vectors. </summary>
      <param name="vector1">The first vector. </param>
      <param name="vector2">The second vector. </param>
      <returns>The cross product. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Distance(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Computes the Euclidean distance between the two given points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.DistanceSquared(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns the Euclidean distance squared between two specified points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Divide(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector resulting from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Divide(System.Numerics.Vector3,System.Single)">
      <summary>Divides the specified vector by a specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="divisor">The scalar value. </param>
      <returns>The vector that results from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Dot(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns the dot product of two vectors. </summary>
      <param name="vector1">The first vector. </param>
      <param name="vector2">The second vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Equals(System.Numerics.Vector3)">
      <summary>Returns a value that indicates whether this instance and another vector are equal. </summary>
      <param name="other">The other vector. </param>
      <returns>
          <see langword="true" /> if the two vectors are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal.</summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Length">
      <summary>Returns the length of this vector object. </summary>
      <returns>The vector's length. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.LengthSquared">
      <summary>Returns the length of the vector squared. </summary>
      <returns>The vector's length squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Lerp(System.Numerics.Vector3,System.Numerics.Vector3,System.Single)">
      <summary>Performs a linear interpolation between two vectors based on the given weighting. </summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <param name="amount">A value between 0 and 1 that indicates the weight of <paramref name="value2" />. </param>
      <returns>The interpolated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Max(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns a vector whose elements are the maximum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The maximized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Min(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns a vector whose elements are the minimum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The minimized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Multiply(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Multiply(System.Numerics.Vector3,System.Single)">
      <summary>Multiplies a vector by a specified scalar. </summary>
      <param name="left">The vector to multiply. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Multiply(System.Single,System.Numerics.Vector3)">
      <summary>Multiplies a scalar value by a specified vector.</summary>
      <param name="left">The scaled value. </param>
      <param name="right">The vector. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Negate(System.Numerics.Vector3)">
      <summary>Negates a specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Normalize(System.Numerics.Vector3)">
      <summary>Returns a vector with the same direction as the specified vector, but with a length of one. </summary>
      <param name="value">The vector to normalize. </param>
      <returns>The normalized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Addition(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Division(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from dividing <paramref name="left" /> by <paramref name="right" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Division(System.Numerics.Vector3,System.Single)">
      <summary>Divides the specified vector by a specified scalar value.</summary>
      <param name="value1">The vector. </param>
      <param name="value2">The scalar value. </param>
      <returns>The result of the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Equality(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns a value that indicates whether each pair of elements in two specified vectors is equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Inequality(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns a value that indicates whether two specified vectors are not equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Multiply(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Multiply(System.Numerics.Vector3,System.Single)">
      <summary>Multiples the specified vector by the specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Multiply(System.Single,System.Numerics.Vector3)">
      <summary>Multiples the scalar value by the specified vector. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_Subtraction(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from subtracting <paramref name="right" /> from <paramref name="left" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.op_UnaryNegation(System.Numerics.Vector3)">
      <summary>Negates the specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Reflect(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Returns the reflection of a vector off a surface that has the specified normal. </summary>
      <param name="vector">The source vector. </param>
      <param name="normal">The normal of the surface being reflected off. </param>
      <returns>The reflected vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.SquareRoot(System.Numerics.Vector3)">
      <summary>Returns a vector whose elements are the square root of each of a specified vector's elements.</summary>
      <param name="value">A vector. </param>
      <returns>The square root vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Subtract(System.Numerics.Vector3,System.Numerics.Vector3)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The difference vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.ToString">
      <summary>Returns the string representation of the current instance using default formatting. </summary>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.ToString(System.String)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements. </summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements.</param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.ToString(System.String,System.IFormatProvider)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements and the specified format provider to define culture-specific formatting.</summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements. </param>
      <param name="formatProvider">A format provider that supplies culture-specific formatting information. </param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Transform(System.Numerics.Vector3,System.Numerics.Matrix4x4)">
      <summary>Transforms a vector by a specified 4x4 matrix. </summary>
      <param name="position">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.Transform(System.Numerics.Vector3,System.Numerics.Quaternion)">
      <summary>Transforms a vector by the specified Quaternion rotation value. </summary>
      <param name="value">The vector to rotate. </param>
      <param name="rotation">The rotation to apply. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector3.TransformNormal(System.Numerics.Vector3,System.Numerics.Matrix4x4)">
      <summary>Transforms a vector normal by the given 4x4 matrix. </summary>
      <param name="normal">The source vector. </param>
      <param name="matrix">The matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.#ctor(System.Numerics.Vector2,System.Single,System.Single)">
      <summary>Creates a   new <see cref="T:System.Numerics.Vector4" /> object from the specified <see cref="T:System.Numerics.Vector2" /> object and a Z and a W component. </summary>
      <param name="value">The vector to use for the X and Y components. </param>
      <param name="z">The Z component. </param>
      <param name="w">The W component. </param>
    </member>
    <member name="M:System.Numerics.Vector4.#ctor(System.Numerics.Vector3,System.Single)">
      <summary>Constructs a new <see cref="T:System.Numerics.Vector4" /> object from the specified <see cref="T:System.Numerics.Vector3" /> object and a W component.  </summary>
      <param name="value">The vector to use for the X, Y, and Z components. </param>
      <param name="w">The W component. </param>
    </member>
    <member name="M:System.Numerics.Vector4.#ctor(System.Single)">
      <summary>Creates a new <see cref="T:System.Numerics.Vector4" /> object whose four elements have the same value.</summary>
      <param name="value">The value to assign to all four elements. </param>
    </member>
    <member name="M:System.Numerics.Vector4.#ctor(System.Single,System.Single,System.Single,System.Single)">
      <summary>Creates a vector whose elements have the specified values. </summary>
      <param name="x">The value to assign to the <see cref="F:System.Numerics.Vector4.X" /> field. </param>
      <param name="y">The value to assign to the <see cref="F:System.Numerics.Vector4.Y" /> field. </param>
      <param name="z">The value to assign to the <see cref="F:System.Numerics.Vector4.Z" /> field. </param>
      <param name="w">The value to assign to the <see cref="F:System.Numerics.Vector4.W" /> field. </param>
    </member>
    <member name="M:System.Numerics.Vector4.Abs(System.Numerics.Vector4)">
      <summary>Returns a vector whose elements are the absolute values of each of the specified vector's elements. </summary>
      <param name="value">A vector. </param>
      <returns>The absolute value vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Add(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Clamp(System.Numerics.Vector4,System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Restricts a vector between a minimum and a maximum value. </summary>
      <param name="value1">The vector to restrict. </param>
      <param name="min">The minimum value. </param>
      <param name="max">The maximum value. </param>
      <returns>The restricted vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.CopyTo(System.Single[])">
      <summary>Copies the elements of the vector to a specified array. </summary>
      <param name="array">The destination array. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector4.CopyTo(System.Single[],System.Int32)">
      <summary>Copies the elements of the vector to a specified array starting at a specified index position. </summary>
      <param name="array">The destination array.</param>
      <param name="index">The index at which to copy the first element of the vector. </param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />. </exception>
      <exception cref="T:System.ArgumentException">The number of elements in the current instance is greater than in the array. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="index" /> is less than zero.-or-
              <paramref name="index" /> is greater than or equal to the array length. </exception>
      <exception cref="T:System.RankException">
              <paramref name="array" /> is multidimensional.</exception>
    </member>
    <member name="M:System.Numerics.Vector4.Distance(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Computes the Euclidean distance between the two given points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.DistanceSquared(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns the Euclidean distance squared between two specified points. </summary>
      <param name="value1">The first point. </param>
      <param name="value2">The second point. </param>
      <returns>The distance squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Divide(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector resulting from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Divide(System.Numerics.Vector4,System.Single)">
      <summary>Divides the specified vector by a specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="divisor">The scalar value. </param>
      <returns>The vector that results from the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Dot(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns the dot product of two vectors. </summary>
      <param name="vector1">The first vector. </param>
      <param name="vector2">The second vector. </param>
      <returns>The dot product. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Equals(System.Numerics.Vector4)">
      <summary>Returns a value that indicates whether this instance and another vector are equal. </summary>
      <param name="other">The other vector. </param>
      <returns>
          <see langword="true" /> if the two vectors are equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Equals(System.Object)">
      <summary>Returns a value that indicates whether this instance and a specified object are equal.</summary>
      <param name="obj">The object to compare with the current instance. </param>
      <returns>
          <see langword="true" /> if the current instance and <paramref name="obj" /> are equal; otherwise, <see langword="false" /><see langword="" />. If <paramref name="obj" /> is <see langword="null" />, the method returns <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.GetHashCode">
      <summary>Returns the hash code for this instance. </summary>
      <returns>The hash code. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Length">
      <summary>Returns the length of this vector object. </summary>
      <returns>The vector's length. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.LengthSquared">
      <summary>Returns the length of the vector squared. </summary>
      <returns>The vector's length squared. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Lerp(System.Numerics.Vector4,System.Numerics.Vector4,System.Single)">
      <summary>Performs a linear interpolation between two vectors based on the given weighting. </summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <param name="amount">A value between 0 and 1 that indicates the weight of <paramref name="value2" />. </param>
      <returns>The interpolated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Max(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns a vector whose elements are the maximum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The maximized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Min(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns a vector whose elements are the minimum of each of the pairs of elements in two specified vectors.</summary>
      <param name="value1">The first vector. </param>
      <param name="value2">The second vector. </param>
      <returns>The minimized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Multiply(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Multiply(System.Numerics.Vector4,System.Single)">
      <summary>Multiplies a vector by a specified scalar. </summary>
      <param name="left">The vector to multiply. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Multiply(System.Single,System.Numerics.Vector4)">
      <summary>Multiplies a scalar value by a specified vector.</summary>
      <param name="left">The scaled value. </param>
      <param name="right">The vector. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Negate(System.Numerics.Vector4)">
      <summary>Negates a specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Normalize(System.Numerics.Vector4)">
      <summary>Returns a vector with the same direction as the specified vector, but with a length of one. </summary>
      <param name="vector">The vector to normalize. </param>
      <returns>The normalized vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Addition(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Adds two vectors together. </summary>
      <param name="left">The first vector to add. </param>
      <param name="right">The second vector to add. </param>
      <returns>The summed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Division(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Divides the first vector by the second. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from dividing <paramref name="left" /> by <paramref name="right" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Division(System.Numerics.Vector4,System.Single)">
      <summary>Divides the specified vector by a specified scalar value.</summary>
      <param name="value1">The vector. </param>
      <param name="value2">The scalar value. </param>
      <returns>The result of the division. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Equality(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns a value that indicates whether each pair of elements in two specified vectors is equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are equal; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Inequality(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Returns a value that indicates whether two specified vectors are not equal.  </summary>
      <param name="left">The first vector to compare. </param>
      <param name="right">The second vector to compare. </param>
      <returns>
          <see langword="true" /> if <paramref name="left" /> and <paramref name="right" /> are not equal; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Multiply(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Multiplies two vectors together. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The product vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Multiply(System.Numerics.Vector4,System.Single)">
      <summary>Multiples the specified vector by the specified scalar value. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Multiply(System.Single,System.Numerics.Vector4)">
      <summary>Multiples the scalar value by the specified vector. </summary>
      <param name="left">The vector. </param>
      <param name="right">The scalar value. </param>
      <returns>The scaled vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_Subtraction(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The vector that results from subtracting <paramref name="right" /> from <paramref name="left" />. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.op_UnaryNegation(System.Numerics.Vector4)">
      <summary>Negates the specified vector. </summary>
      <param name="value">The vector to negate. </param>
      <returns>The negated vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.SquareRoot(System.Numerics.Vector4)">
      <summary>Returns a vector whose elements are the square root of each of a specified vector's elements.</summary>
      <param name="value">A vector. </param>
      <returns>The square root vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Subtract(System.Numerics.Vector4,System.Numerics.Vector4)">
      <summary>Subtracts the second vector from the first. </summary>
      <param name="left">The first vector. </param>
      <param name="right">The second vector. </param>
      <returns>The difference vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.ToString">
      <summary>Returns the string representation of the current instance using default formatting. </summary>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.ToString(System.String)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements. </summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements.</param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.ToString(System.String,System.IFormatProvider)">
      <summary>Returns the string representation of the current instance using the specified format string to format individual elements and the specified format provider to define culture-specific formatting.</summary>
      <param name="format">A standard or custom numeric format string that defines the format of individual elements. </param>
      <param name="formatProvider">A format provider that supplies culture-specific formatting information. </param>
      <returns>The string representation of the current instance. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector2,System.Numerics.Matrix4x4)">
      <summary>Transforms a two-dimensional vector by a specified 4x4 matrix. </summary>
      <param name="position">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector2,System.Numerics.Quaternion)">
      <summary>Transforms a two-dimensional vector by the specified Quaternion rotation value. </summary>
      <param name="value">The vector to rotate. </param>
      <param name="rotation">The rotation to apply. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector3,System.Numerics.Matrix4x4)">
      <summary>Transforms a three-dimensional vector by a specified 4x4 matrix. </summary>
      <param name="position">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector3,System.Numerics.Quaternion)">
      <summary>Transforms a three-dimensional vector by the specified Quaternion rotation value. </summary>
      <param name="value">The vector to rotate. </param>
      <param name="rotation">The rotation to apply. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector4,System.Numerics.Matrix4x4)">
      <summary>Transforms a four-dimensional vector by a specified 4x4 matrix. </summary>
      <param name="vector">The vector to transform. </param>
      <param name="matrix">The transformation matrix. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="M:System.Numerics.Vector4.Transform(System.Numerics.Vector4,System.Numerics.Quaternion)">
      <summary>Transforms a four-dimensional vector by the specified Quaternion rotation value. </summary>
      <param name="value">The vector to rotate. </param>
      <param name="rotation">The rotation to apply. </param>
      <returns>The transformed vector. </returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsEven">
      <summary>Indicates whether the value of the current <see cref="T:System.Numerics.BigInteger" /> object is an even number.</summary>
      <returns>
          <see langword="true" /> if the value of the <see cref="T:System.Numerics.BigInteger" /> object is an even number; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsOne">
      <summary>Indicates whether the value of the current <see cref="T:System.Numerics.BigInteger" /> object is <see cref="P:System.Numerics.BigInteger.One" />.</summary>
      <returns>
          <see langword="true" /> if the value of the <see cref="T:System.Numerics.BigInteger" /> object is <see cref="P:System.Numerics.BigInteger.One" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsPowerOfTwo">
      <summary>Indicates whether the value of the current <see cref="T:System.Numerics.BigInteger" /> object is a power of two.</summary>
      <returns>
          <see langword="true" /> if the value of the <see cref="T:System.Numerics.BigInteger" /> object is a power of two; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.IsZero">
      <summary>Indicates whether the value of the current <see cref="T:System.Numerics.BigInteger" /> object is <see cref="P:System.Numerics.BigInteger.Zero" />.</summary>
      <returns>
          <see langword="true" /> if the value of the <see cref="T:System.Numerics.BigInteger" /> object is <see cref="P:System.Numerics.BigInteger.Zero" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.MinusOne">
      <summary>Gets a value that represents the number negative one (-1).</summary>
      <returns>An integer whose value is negative one (-1).</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.One">
      <summary>Gets a value that represents the number one (1).</summary>
      <returns>An object whose value is one (1).</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.Sign">
      <summary>Gets a number that indicates the sign (negative, positive, or zero) of the current <see cref="T:System.Numerics.BigInteger" /> object.</summary>
      <returns>A number that indicates the sign of the <see cref="T:System.Numerics.BigInteger" /> object, as shown in the following table.NumberDescription-1The value of this object is negative.0The value of this object is 0 (zero).1The value of this object is positive.</returns>
    </member>
    <member name="P:System.Numerics.BigInteger.Zero">
      <summary>Gets a value that represents the number 0 (zero).</summary>
      <returns>An integer whose value is 0 (zero).</returns>
    </member>
    <member name="P:System.Numerics.Complex.Imaginary">
      <summary>Gets the imaginary component of the current <see cref="T:System.Numerics.Complex" /> object.</summary>
      <returns>The imaginary component of a complex number.</returns>
    </member>
    <member name="P:System.Numerics.Complex.Magnitude">
      <summary>Gets the magnitude (or absolute value) of a complex number.</summary>
      <returns>The magnitude of the current instance.</returns>
    </member>
    <member name="P:System.Numerics.Complex.Phase">
      <summary>Gets the phase of a complex number.</summary>
      <returns>The phase of a complex number, in radians.</returns>
    </member>
    <member name="P:System.Numerics.Complex.Real">
      <summary>Gets the real component of the current <see cref="T:System.Numerics.Complex" /> object.</summary>
      <returns>The real component of a complex number.</returns>
    </member>
    <member name="P:System.Numerics.Matrix3x2.Identity">
      <summary>Gets the multiplicative identity matrix. </summary>
      <returns>The multiplicative identify matrix. </returns>
    </member>
    <member name="P:System.Numerics.Matrix3x2.IsIdentity">
      <summary>Indicates whether the current matrix is the identity matrix. </summary>
      <returns>
          <see langword="true" /> if the current matrix is the identity matrix; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="P:System.Numerics.Matrix3x2.Translation">
      <summary>Gets or sets the translation component of this matrix. </summary>
      <returns>The translation component of the current instance. </returns>
    </member>
    <member name="P:System.Numerics.Matrix4x4.Identity">
      <summary>Gets the multiplicative identity matrix. </summary>
      <returns>Gets the multiplicative identity matrix. </returns>
    </member>
    <member name="P:System.Numerics.Matrix4x4.IsIdentity">
      <summary>Indicates whether the current matrix is the identity matrix. </summary>
      <returns>
          <see langword="true" /> if the current matrix is the identity matrix; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="P:System.Numerics.Matrix4x4.Translation">
      <summary>Gets or sets the translation component of this matrix. </summary>
      <returns>The translation component of the current instance. </returns>
    </member>
    <member name="P:System.Numerics.Quaternion.Identity">
      <summary>Gets a quaternion that represents no rotation. </summary>
      <returns>A quaternion whose values are (0, 0, 0, 1). </returns>
    </member>
    <member name="P:System.Numerics.Quaternion.IsIdentity">
      <summary>Gets a value that indicates whether the current instance is the identity quaternion. </summary>
      <returns>
          <see langword="true" /> if the current instance is the identity quaternion; otherwise, <see langword="false" />.  </returns>
    </member>
    <member name="P:System.Numerics.Vector2.One">
      <summary>Gets a vector whose 2 elements are equal to one. </summary>
      <returns>A vector whose two elements are equal to one (that is, it returns the vector (1,1).</returns>
    </member>
    <member name="P:System.Numerics.Vector2.UnitX">
      <summary>Gets the vector (1,0). </summary>
      <returns>The vector (1,0). </returns>
    </member>
    <member name="P:System.Numerics.Vector2.UnitY">
      <summary>Gets the vector (0,1).</summary>
      <returns>The vector (0,1).</returns>
    </member>
    <member name="P:System.Numerics.Vector2.Zero">
      <summary>Returns a vector whose 2 elements are equal to zero. </summary>
      <returns>A vector whose two elements are equal to zero (that is, it returns the vector (0,0). </returns>
    </member>
    <member name="P:System.Numerics.Vector3.One">
      <summary>Gets a vector whose 3 elements are equal to one.</summary>
      <returns>A vector whose three elements are equal to one (that is, it returns the vector (1,1,1). </returns>
    </member>
    <member name="P:System.Numerics.Vector3.UnitX">
      <summary>Gets the vector (1,0,0).</summary>
      <returns>The vector (1,0,0).</returns>
    </member>
    <member name="P:System.Numerics.Vector3.UnitY">
      <summary>Gets the vector (0,1,0).</summary>
      <returns>The vector (0,1,0)..</returns>
    </member>
    <member name="P:System.Numerics.Vector3.UnitZ">
      <summary>Gets the vector (0,0,1).</summary>
      <returns>The vector (0,0,1). </returns>
    </member>
    <member name="P:System.Numerics.Vector3.Zero">
      <summary>Gets a vector whose 3 elements are equal to zero. </summary>
      <returns>A vector whose three elements are equal to zero (that is, it returns the vector (0,0,0). </returns>
    </member>
    <member name="P:System.Numerics.Vector4.One">
      <summary>Gets a vector whose 4 elements are equal to one. </summary>
      <returns>Returns <see cref="T:System.Numerics.Vector4" />.</returns>
    </member>
    <member name="P:System.Numerics.Vector4.UnitW">
      <summary>Gets the vector (0,0,0,1).</summary>
      <returns>The vector (0,0,0,1).</returns>
    </member>
    <member name="P:System.Numerics.Vector4.UnitX">
      <summary>Gets the vector (1,0,0,0).</summary>
      <returns>The vector (1,0,0,0).</returns>
    </member>
    <member name="P:System.Numerics.Vector4.UnitY">
      <summary>Gets the vector (0,1,0,0).</summary>
      <returns>The vector (0,1,0,0)..</returns>
    </member>
    <member name="P:System.Numerics.Vector4.UnitZ">
      <summary>Gets the vector (0,0,1,0). </summary>
      <returns>The vector (0,0,1,0).</returns>
    </member>
    <member name="P:System.Numerics.Vector4.Zero">
      <summary>Gets a vector whose 4 elements are equal to zero. </summary>
      <returns>A vector whose four elements are equal to zero (that is, it returns the vector (0,0,0,0). </returns>
    </member>
    <member name="T:System.Numerics.BigInteger">
      <summary>Represents an arbitrarily large signed integer.</summary>
    </member>
    <member name="T:System.Numerics.Complex">
      <summary>Represents a complex number.</summary>
    </member>
    <member name="T:System.Numerics.Matrix3x2">
      <summary>Represents a 3x2 matrix. </summary>
    </member>
    <member name="T:System.Numerics.Matrix4x4">
      <summary>Represents a 4x4 matrix. </summary>
    </member>
    <member name="T:System.Numerics.Plane">
      <summary>Represents a three-dimensional plane.</summary>
    </member>
    <member name="T:System.Numerics.Quaternion">
      <summary>Represents a vector that is used to encode three-dimensional physical rotations. </summary>
    </member>
    <member name="T:System.Numerics.Vector2">
      <summary>Represents a vector with two single-precision floating-point values. </summary>
    </member>
    <member name="T:System.Numerics.Vector3">
      <summary>Represents a vector with three  single-precision floating-point values. </summary>
    </member>
    <member name="T:System.Numerics.Vector4">
      <summary>Represents a vector with four single-precision floating-point values. </summary>
    </member>
  </members>
</doc>