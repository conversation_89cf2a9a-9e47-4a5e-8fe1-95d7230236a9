﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Runtime.Serialization</name>
  </assembly>
  <members>
    <member name="M:System.Runtime.Serialization.CollectionDataContractAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.CollectionDataContractAttribute" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DataContractSerializerSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.DataContractSerializerSection" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElement" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElement" /> class with the specified type name.</summary>
      <param name="typeName">The name of the type that requires a collection of known types.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Add(System.Runtime.Serialization.Configuration.DeclaredTypeElement)">
      <summary>Adds a specified configuration element to the collection.</summary>
      <param name="element">The configuration element to add.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Clear">
      <summary>Removes all members of the collection.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Contains(System.String)">
      <summary>Returns a value that specifies whether the element is in the collection.</summary>
      <param name="typeName">The name of the type to check for.</param>
      <returns>
          <see langword="true" /> if the element is in the collection; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.IndexOf(System.Runtime.Serialization.Configuration.DeclaredTypeElement)">
      <summary>Returns the position of the specified configuration element.</summary>
      <param name="element">The element to find in the collection.</param>
      <returns>The index of the specified configuration element; otherwise, -1.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="element" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Remove(System.Runtime.Serialization.Configuration.DeclaredTypeElement)">
      <summary>Removes the specified configuration element from the collection.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElement" /> to remove.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Remove(System.String)">
      <summary>Removes the element specified by its key from the collection.</summary>
      <param name="typeName">The name of the type (which functions as a key) to remove from the collection.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.RemoveAt(System.Int32)">
      <summary>Removes the configuration element found at the specified position.</summary>
      <param name="index">The position of the configuration element to remove.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElement.#ctor(System.Int32)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> class with the specified index. </summary>
      <param name="index">Specifies a position in the collection of parameters.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> class with the specified type name.</summary>
      <param name="typeName">The name of the parameter's type.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.ParameterElementCollection" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.Add(System.Runtime.Serialization.Configuration.ParameterElement)">
      <summary>Adds an element to the collection of parameter elements.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> element to add to the collection.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.Clear">
      <summary>Removes all members of the collection.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.Contains(System.String)">
      <summary>Gets or sets a value specifying whether the named type is found in the collection.</summary>
      <param name="typeName">The name of the type to find.</param>
      <returns>
          <see langword="true" /> if the element is present; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.IndexOf(System.Runtime.Serialization.Configuration.ParameterElement)">
      <summary>Gets the position of the specified element in the collection.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> element to find.</param>
      <returns>The position of the specified element.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.Remove(System.Runtime.Serialization.Configuration.ParameterElement)">
      <summary>Removes the specified element from the collection.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> to remove.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.ParameterElementCollection.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified position.</summary>
      <param name="index">The position of the element to remove.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.SerializationSectionGroup.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.SerializationSectionGroup" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.SerializationSectionGroup.GetSectionGroup(System.Configuration.Configuration)">
      <summary>Gets the serialization configuration section for the specified configuration.</summary>
      <param name="config">A <see cref="T:System.Configuration.Configuration" /> that represents the configuration to retrieve.</param>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.SerializationSectionGroup" /> that represents the configuration section.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElement.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.TypeElement" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElement.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.TypeElement" /> class with the specified type name. </summary>
      <param name="typeName">The name of the type that uses known types.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Configuration.TypeElementCollection" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.Add(System.Runtime.Serialization.Configuration.TypeElement)">
      <summary>Adds the specified element to the collection.</summary>
      <param name="element">A <see cref="T:System.Runtime.Serialization.Configuration.TypeElement" /> that represents the known type to add. </param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.Clear">
      <summary>Removes all members of the collection.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.IndexOf(System.Runtime.Serialization.Configuration.TypeElement)">
      <summary>Returns the position of the specified element.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.TypeElement" /> to find in the collection.</param>
      <returns>The position of the specified element.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.Remove(System.Runtime.Serialization.Configuration.TypeElement)">
      <summary>Removes the specified element from the collection.</summary>
      <param name="element">The <see cref="T:System.Runtime.Serialization.Configuration.TypeElement" /> to remove.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Configuration.TypeElementCollection.RemoveAt(System.Int32)">
      <summary>Removes the element at the specified position.</summary>
      <param name="index">The position in the collection from which to remove the element.</param>
    </member>
    <member name="M:System.Runtime.Serialization.ContractNamespaceAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.ContractNamespaceAttribute" /> class using the supplied namespace. </summary>
      <param name="contractNamespace">The namespace of the contract.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractResolver.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractResolver.ResolveName(System.String,System.String,System.Type,System.Runtime.Serialization.DataContractResolver)">
      <summary>Override this method to map the specified <see langword="xsi:type" /> name and namespace to a data contract type during deserialization.</summary>
      <param name="typeName">The <see langword="xsi:type" /> name to map.</param>
      <param name="typeNamespace">The <see langword="xsi:type" /> namespace to map.</param>
      <param name="declaredType">The type declared in the data contract.</param>
      <param name="knownTypeResolver">The known type resolver.</param>
      <returns>The type the <see langword="xsi:type" /> name and namespace is mapped to. </returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractResolver.TryResolveType(System.Type,System.Type,System.Runtime.Serialization.DataContractResolver,System.Xml.XmlDictionaryString@,System.Xml.XmlDictionaryString@)">
      <summary>Override this method to map a data contract type to an <see langword="xsi:type" /> name and namespace during serialization.</summary>
      <param name="type">The type to map.</param>
      <param name="declaredType">The type declared in the data contract.</param>
      <param name="knownTypeResolver">The known type resolver.</param>
      <param name="typeName">The xsi:type name.</param>
      <param name="typeNamespace">The xsi:type namespace.</param>
      <returns>
          <see langword="true" /> if mapping succeeded; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type, and a collection of known types that may be present in the object graph.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" />  of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, and a surrogate for custom serialization.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. The default is the value returned by the <see cref="F:System.Int32.MaxValue" /> property.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of items exceeds the maximum value.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Runtime.Serialization.DataContractResolver)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, a surrogate for custom serialization, and an alternative for mapping <see langword="xsi:type" /> declarations at run time.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. The default is the value returned by the <see cref="F:System.Int32.MaxValue" /> property.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="dataContractResolver">An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> to map <see langword="xsi:type" /> declarations to data contract types.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Runtime.Serialization.DataContractSerializerSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type and settings.</summary>
      <param name="type">The type of the instance to serialize or deserialize.</param>
      <param name="settings">The serializer settings.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type using the supplied XML root element and namespace.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.String,System.String,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies the root XML element and namespace in two string parameters as well as a list of known types that may be present in the object graph.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The root element name of the content.</param>
      <param name="rootNamespace">The namespace of the root element.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" />  of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.String,System.String,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, a surrogate for custom serialization, and the XML element and namespace that contain the content.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of items exceeds the maximum value.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.String,System.String,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Runtime.Serialization.DataContractResolver)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, a surrogate for custom serialization, the XML element and namespace that contains the content, and an alternative for mapping <see langword="xsi:type" /> declarations at run time.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="dataContractResolver">An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> to map <see langword="xsi:type" /> declarations to data contract types.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type using the XML root element and namespace specified through the parameters of type <see cref="T:System.Xml.XmlDictionaryString" />.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content.</param>
      <param name="rootNamespace">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the namespace of the root element.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies the root XML element and namespace in two <see cref="T:System.Xml.XmlDictionaryString" /> parameters as well as a list of known types that may be present in the object graph.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content.</param>
      <param name="rootNamespace">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the namespace of the root element.</param>
      <param name="knownTypes">A <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, a surrogate for custom serialization, and parameters of <see cref="T:System.Xml.XmlDictionaryString" /> that specify the XML element and namespace that contain the content.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The <see cref="T:System.Xml.XmlDictionaryString" /> that specifies the XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The <see cref="T:System.Xml.XmlDictionaryString" /> that specifies the XML namespace of the root.</param>
      <param name="knownTypes">A <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of items exceeds the maximum value.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Runtime.Serialization.DataContractResolver)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize, parameters to ignore unexpected data, whether to use non-standard XML constructs to preserve object reference data in the graph, a surrogate for custom serialization, parameters of <see cref="T:System.Xml.XmlDictionaryString" /> that specify the XML element and namespace that contains the content, and an alternative for mapping <see langword="xsi:type" /> declarations at run time.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">The XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type upon serialization and deserialization; otherwise, <see langword="false" />.</param>
      <param name="preserveObjectReferences">
            <see langword="true" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="dataContractResolver">An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> to map <see langword="xsi:type" /> declarations to data contract types.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.IsStartObject(System.Xml.XmlDictionaryReader)">
      <summary>Determines whether the <see cref="T:System.Xml.XmlDictionaryReader" /> is positioned on an object that can be deserialized.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML stream.</param>
      <returns>
          <see langword="true" /> if the reader is at the start element of the stream to read; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.IsStartObject(System.Xml.XmlReader)">
      <summary>Determines whether the <see cref="T:System.Xml.XmlReader" /> is positioned on an object that can be deserialized.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream.</param>
      <returns>
          <see langword="true" /> if the reader is at the start element of the stream to read; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Reads the XML stream with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object, and also specifies whether a check is made to verify the object name before reading its value. </summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML stream.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the name of the object corresponds to the root name value supplied in the constructor; otherwise, <see langword="false" />. </param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.Runtime.Serialization.SerializationException">The <paramref name="verifyObjectName" /> parameter is set to <see langword="true" />, and the element name and namespace do not correspond to the values set in the constructor. </exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean,System.Runtime.Serialization.DataContractResolver)">
      <summary>Reads an XML document or document stream and returns the deserialized object.  The method includes a parameter to specify whether the object name is verified is validated, and a resolver for mapping <see langword="xsi:type" /> declarations at runtime.</summary>
      <param name="reader">The XML reader used to read the content.</param>
      <param name="verifyObjectName">
            <see langword="true" />  to verify the object name; otherwise, <see langword="false" />.</param>
      <param name="dataContractResolver">An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> to map <see langword="xsi:type" /> declarations to data contract types.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.ReadObject(System.Xml.XmlReader)">
      <summary>Reads the XML stream with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.ReadObject(System.Xml.XmlReader,System.Boolean)">
      <summary>Reads the XML stream with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object, and also specifies whether a check is made to verify the object name before reading its value.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the name of the object corresponds to the root name value supplied in the constructor; otherwise, <see langword="false" />.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.Runtime.Serialization.SerializationException">The <paramref name="verifyObjectName" /> parameter is set to <see langword="true" />, and the element name and namespace do not correspond to the values set in the constructor. </exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteEndObject(System.Xml.XmlDictionaryWriter)">
      <summary>Writes the closing XML element using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteEndObject(System.Xml.XmlWriter)">
      <summary>Writes the closing XML element using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteObject(System.Xml.XmlDictionaryWriter,System.Object,System.Runtime.Serialization.DataContractResolver)">
      <summary>Writes all the object data (starting XML element, content, and enclosing element) to an XML document or stream  using the specified XmlDictionaryWriter. The method includes a resolver for mapping <see langword="xsi:type" /> declarations at runtime.</summary>
      <param name="writer">An XmlDictionaryWriter used to write the content to the XML document or stream.</param>
      <param name="graph">The object that contains the content to write.</param>
      <param name="dataContractResolver">An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> used to map <see langword="xsi:type" /> declarations to known data contracts.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes all the object data (starting XML element, content, and closing element) to an XML document or stream with an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <param name="graph">The object that contains the data to write to the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteObjectContent(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the XML content using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the stream.</param>
      <param name="graph">The object to write to the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteObjectContent(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the XML content using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the stream.</param>
      <param name="graph">The object to write to the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteStartObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the opening XML element using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML start element.</param>
      <param name="graph">The object to write.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializer.WriteStartObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the opening XML element using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML start element.</param>
      <param name="graph">The object to write.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializerExtensions.GetSerializationSurrogateProvider(System.Runtime.Serialization.DataContractSerializer)">
      <summary>Returns the surrogate serialization provider for this serializer.</summary>
      <param name="serializer">The serializer which is being surrogated.</param>
      <returns>The surrogate serializer.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializerExtensions.SetSerializationSurrogateProvider(System.Runtime.Serialization.DataContractSerializer,System.Runtime.Serialization.ISerializationSurrogateProvider)">
      <summary>Specifies a surrogate serialization provider for this <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
      <param name="serializer">The serializer which is being surrogated.</param>
      <param name="provider">The surrogate serialization provider.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DataContractSerializerSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializerSettings" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.DataMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DataMemberAttribute" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> class using the format string.</summary>
      <param name="formatString">The format string.</param>
    </member>
    <member name="M:System.Runtime.Serialization.DateTimeFormat.#ctor(System.String,System.IFormatProvider)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.DateTimeFormat" /> class using the format string and format provider.</summary>
      <param name="formatString">The format sting.</param>
      <param name="formatProvider">The format provider.</param>
    </member>
    <member name="M:System.Runtime.Serialization.EnumMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.EnumMemberAttribute" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.ExportOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.ExportOptions" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetCustomDataToExport(System.Reflection.MemberInfo,System.Type)">
      <summary>During schema export operations, inserts annotations into the schema for non-null return values. </summary>
      <param name="memberInfo">A <see cref="T:System.Reflection.MemberInfo" /> that describes the member. </param>
      <param name="dataContractType">A <see cref="T:System.Type" />. </param>
      <returns>An object that represents the annotation to be inserted into the XML schema definition. </returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetCustomDataToExport(System.Type,System.Type)">
      <summary>During schema export operations, inserts annotations into the schema for non-null return values. </summary>
      <param name="clrType">The CLR type to be replaced. </param>
      <param name="dataContractType">The data contract type to be annotated. </param>
      <returns>An object that represents the annotation to be inserted into the XML schema definition. </returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetDataContractType(System.Type)">
      <summary>During serialization, deserialization, and schema import and export, returns a data contract type that substitutes the specified type. </summary>
      <param name="type">The CLR type <see cref="T:System.Type" /> to substitute. </param>
      <returns>The <see cref="T:System.Type" /> to substitute for the <paramref name="type" /> value. This type must be serializable by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. For example, it must be marked with the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute or other mechanisms that the serializer recognizes.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetDeserializedObject(System.Object,System.Type)">
      <summary>During deserialization, returns an object that is a substitute for the specified object.</summary>
      <param name="obj">The deserialized object to be substituted.</param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to. </param>
      <returns>The substituted deserialized object. This object must be of a type that is serializable by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. For example, it must be marked with the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute or other mechanisms that the serializer recognizes.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetKnownCustomDataTypes(System.Collections.ObjectModel.Collection{System.Type})">
      <summary>Sets the collection of known types to use for serialization and deserialization of the custom data objects. </summary>
      <param name="customDataTypes">A <see cref="T:System.Collections.ObjectModel.Collection`1" />  of <see cref="T:System.Type" /> to add known types to.</param>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetObjectToSerialize(System.Object,System.Type)">
      <summary>During serialization, returns an object that substitutes the specified object. </summary>
      <param name="obj">The object to substitute. </param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to.</param>
      <returns>The substituted object that will be serialized. The object must be serializable by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. For example, it must be marked with the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute or other mechanisms that the serializer recognizes.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.GetReferencedTypeOnImport(System.String,System.String,System.Object)">
      <summary>During schema import, returns the type referenced by the schema.</summary>
      <param name="typeName">The name of the type in schema.</param>
      <param name="typeNamespace">The namespace of the type in schema.</param>
      <param name="customData">The object that represents the annotation inserted into the XML schema definition, which is data that can be used for finding the referenced type.</param>
      <returns>The <see cref="T:System.Type" /> to use for the referenced type.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.IDataContractSurrogate.ProcessImportedType(System.CodeDom.CodeTypeDeclaration,System.CodeDom.CodeCompileUnit)">
      <summary>Processes the type that has been generated from the imported schema.</summary>
      <param name="typeDeclaration">A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> to process that represents the type declaration generated during schema import.</param>
      <param name="compileUnit">The <see cref="T:System.CodeDom.CodeCompileUnit" /> that contains the other code generated during schema import.</param>
      <returns>A <see cref="T:System.CodeDom.CodeTypeDeclaration" /> that contains the processed type.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.IgnoreDataMemberAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.IgnoreDataMemberAttribute" /> class. </summary>
    </member>
    <member name="M:System.Runtime.Serialization.ImportOptions.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.ImportOptions" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified <see cref="T:System.Runtime.Serialization.SerializationInfo" /> and <see cref="T:System.Runtime.Serialization.StreamingContext" />. </summary>
      <param name="info">A <see cref="T:System.Runtime.Serialization.SerializationInfo" /> that contains data needed to serialize and deserialize an object. </param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that specifies user context during serialization and deserialization.</param>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified error message. </summary>
      <param name="message">A description of the error. </param>
    </member>
    <member name="M:System.Runtime.Serialization.InvalidDataContractException.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.InvalidDataContractException" /> class with the specified error message and inner exception. </summary>
      <param name="message">A description of the error. </param>
      <param name="innerException">The original <see cref="T:System.Exception" />. </param>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetDeserializedObject(System.Object,System.Type)">
      <summary>During deserialization, returns an object that is a substitute for the specified object.</summary>
      <param name="obj">The deserialized object to be substituted.</param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to.</param>
      <returns>The substituted deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetObjectToSerialize(System.Object,System.Type)">
      <summary>During serialization, returns an object that substitutes the specified object.</summary>
      <param name="obj">The object to substitute.</param>
      <param name="targetType">The <see cref="T:System.Type" /> that the substituted object should be assigned to.</param>
      <returns>The substituted object that will be serialized.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.ISerializationSurrogateProvider.GetSurrogateType(System.Type)">
      <summary>During serialization, deserialization, and schema import and export, returns a data contract type that substitutes the specified type.</summary>
      <param name="type">The type to substitute.</param>
      <returns>The <see cref="T:System.Type" /> to substitute for the <paramref name="type" /> value.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type, with a collection of known types that may be present in the object graph. </summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" />  of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies a list of known types that may be present in the object graph, the maximum number of graph items to serialize or deserialize, whether to ignore unexpected data or emit type information, and a surrogate for custom serialization.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="knownTypes">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content. </param>
      <param name="maxItemsInObjectGraph">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the <see cref="T:System.Runtime.Serialization.IExtensibleDataObject" /> interface upon serialization and ignore unexpected data upon deserialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="alwaysEmitTypeInformation">
            <see langword="true" /> to emit type information; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Runtime.Serialization.Json.DataContractJsonSerializerSettings)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type and serializer settings.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="settings">The serializer settings for the JSON serializer.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of a specified type using the XML root element specified by a parameter.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.String,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of a specified type using the XML root element specified by a parameter, with a collection of known types that may be present in the object graph.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize. The default is "root".</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" />  of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.String,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies the root name of the XML element, a list of known types that may be present in the object graph, the maximum number of graph items to serialize or deserialize, whether to ignore unexpected data or emit type information, and a surrogate for custom serialization.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize. The default is "root".</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. The default is the value returned by the <see cref="F:System.Int32.MaxValue" /> property.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the <see cref="T:System.Runtime.Serialization.IExtensibleDataObject" /> interface upon serialization and ignore unexpected data upon deserialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="alwaysEmitTypeInformation">
            <see langword="true" /> to emit type information; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of a specified type using the XML root element specified by a parameter of type <see cref="T:System.Xml.XmlDictionaryString" />.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Collections.Generic.IEnumerable{System.Type})">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of a specified type using the XML root element specified by a parameter of type <see cref="T:System.Xml.XmlDictionaryString" />, with a collection of known types that may be present in the object graph.</summary>
      <param name="type">The type of the instances that is serialized or deserialized.</param>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content. </param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the types that may be present in the object graph.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.#ctor(System.Type,System.Xml.XmlDictionaryString,System.Collections.Generic.IEnumerable{System.Type},System.Int32,System.Boolean,System.Runtime.Serialization.IDataContractSurrogate,System.Boolean)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> class to serialize or deserialize an object of the specified type. This method also specifies the root name of the XML element, a list of known types that may be present in the object graph, the maximum number of graph items to serialize or deserialize, whether to ignore unexpected data or emit type information, and a surrogate for custom serialization.</summary>
      <param name="type">The type of the instances that are serialized or deserialized.</param>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element name of the content.</param>
      <param name="knownTypes">An <see cref="T:System.Collections.Generic.IEnumerable`1" /> of <see cref="T:System.Type" /> that contains the known types that may be present in the object graph.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. The default is the value returned by the <see cref="F:System.Int32.MaxValue" /> property.</param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the <see cref="T:System.Runtime.Serialization.IExtensibleDataObject" /> interface upon serialization and ignore unexpected data upon deserialization; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
      <param name="dataContractSurrogate">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to customize the serialization process.</param>
      <param name="alwaysEmitTypeInformation">
            <see langword="true" /> to emit type information; otherwise, <see langword="false" />. The default is <see langword="false" />.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.IsStartObject(System.Xml.XmlDictionaryReader)">
      <summary>Gets a value that specifies whether the <see cref="T:System.Xml.XmlDictionaryReader" /> is positioned over an XML element that represents an object the serializer can deserialize from.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML stream mapped from JSON.</param>
      <returns>
          <see langword="true" /> if the reader is positioned correctly; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.IsStartObject(System.Xml.XmlReader)">
      <summary>Determines whether the <see cref="T:System.Xml.XmlReader" /> is positioned on an object that can be deserialized.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream.</param>
      <returns>
          <see langword="true" /> if the reader is positioned correctly; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.IO.Stream)">
      <summary>Reads a document stream in the JSON (JavaScript Object Notation) format and returns the deserialized object.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> to be read.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.Xml.XmlDictionaryReader)">
      <summary>Reads the XML document mapped from JSON (JavaScript Object Notation) with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML document mapped from JSON.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Reads the XML document mapped from JSON with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object; it also enables you to specify whether the serializer should verify that it is positioned on an appropriate element before attempting to deserialize.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML document mapped from JSON.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the expected name and namespace; otherwise, <see langword="false" /> to skip the verification. The default is <see langword="true" />.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.Xml.XmlReader)">
      <summary>Reads the XML document mapped from JSON (JavaScript Object Notation) with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> used to read the XML document mapped from JSON.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.ReadObject(System.Xml.XmlReader,System.Boolean)">
      <summary>Reads an XML document mapped from JSON with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object; it also enables you to specify whether the serializer should verify that it is positioned on an appropriate element before attempting to deserialize.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> used to read the XML document mapped from JSON.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the expected name and namespace; otherwise, <see langword="false" />, which skips the verification. The default is <see langword="true" />.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteEndObject(System.Xml.XmlDictionaryWriter)">
      <summary>Writes the closing XML element to an XML document, using an <see cref="T:System.Xml.XmlDictionaryWriter" />, which can be mapped to JavaScript Object Notation (JSON).</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document to map to JSON.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteEndObject(System.Xml.XmlWriter)">
      <summary>Writes the closing XML element to an XML document, using an <see cref="T:System.Xml.XmlWriter" />, which can be mapped to JavaScript Object Notation (JSON).</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document mapped to JSON.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.IO.Stream,System.Object)">
      <summary>Serializes a specified object to JavaScript Object Notation (JSON) data and writes the resulting JSON to a stream.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> that is written to.</param>
      <param name="graph">The object that contains the data to write to the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">The maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Serializes an object to XML that may be mapped to JavaScript Object Notation (JSON). Writes all the object data, including the starting XML element, content, and closing element, with an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document or stream to map to JSON.</param>
      <param name="graph">The object that contains the data to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">The maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObject(System.Xml.XmlWriter,System.Object)">
      <summary>Serializes an object to XML that may be mapped to JavaScript Object Notation (JSON). Writes all the object data, including the starting XML element, content, and closing element, with an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document to map to JSON.</param>
      <param name="graph">The object that contains the data to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">The maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObjectContent(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the XML content that can be mapped to JavaScript Object Notation (JSON) using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> to write to.</param>
      <param name="graph">The object to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">The maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteObjectContent(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the XML content that can be mapped to JavaScript Object Notation (JSON) using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write to.</param>
      <param name="graph">The object to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">The type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">There is a problem with the instance being written.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">The maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteStartObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the opening XML element for serializing an object to XML that can be mapped to JavaScript Object Notation (JSON) using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML start element.</param>
      <param name="graph">The object to write.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializer.WriteStartObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the opening XML element for serializing an object to XML that can be mapped to JavaScript Object Notation (JSON) using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML start element.</param>
      <param name="graph">The object to write.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings" /> class.</summary>
    </member>
    <member name="M:System.Runtime.Serialization.Json.IXmlJsonReaderInitializer.SetInput(System.Byte[],System.Int32,System.Int32,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Reinitializes a JavaScript Object Notation (JSON) enabled reader to a specified buffer that contains JSON-encoded data.</summary>
      <param name="buffer">The input <see cref="T:System.Byte" /> buffer array from which to read. </param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> used by the reader.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">The <see cref="T:System.Xml.OnXmlDictionaryReaderClose" /> delegate to call when the reader is closed.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.IXmlJsonReaderInitializer.SetInput(System.IO.Stream,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Reinitializes a JavaScript Object Notation (JSON) enabled reader to a specified stream that contains JSON-encoded data.</summary>
      <param name="stream">The input <see cref="T:System.IO.Stream" /> from which to read.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> used by the reader.</param>
      <param name="quotas">
            <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">Delegate to call when the reader is closed.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.IXmlJsonWriterInitializer.SetOutput(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Initializes (or reinitializes) a JavaScript Object Notation (JSON) writer to a specified output stream with specified character encoding.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> to which the writer writes. </param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding of the output stream.</param>
      <param name="ownsStream">If <see langword="true" />, the output stream is closed by the writer when done; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryReader" /> that can map buffers encoded with JavaScript Object Notation (JSON), with a specified size and offset and character encoding, to an XML Infoset. </summary>
      <param name="buffer">The input <see cref="T:System.Byte" /> buffer array from which to read.</param>
      <param name="offset">Starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">Number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the reader. If <see langword="null" /> is specified as the value, the reader attempts to auto-detect the encoding.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> used to prevent Denial of Service attacks when reading untrusted data. </param>
      <param name="onClose">The <see cref="T:System.Xml.OnXmlDictionaryReaderClose" /> delegate to call when the reader is closed. The default value is <see langword="null" />.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryReader" /> that can read JavaScript Object Notation (JSON).</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonReader(System.Byte[],System.Int32,System.Int32,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryReader" /> that can map buffers encoded with JavaScript Object Notation (JSON), of a specified size and offset, to an XML Infoset.</summary>
      <param name="buffer">The input <see cref="T:System.Byte" /> buffer array from which to read.</param>
      <param name="offset">Starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">Number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> used to prevent Denial of Service attacks when reading untrusted data. </param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryReader" /> that can read JavaScript Object Notation (JSON).</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonReader(System.Byte[],System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryReader" /> that can map buffers encoded with JavaScript Object Notation (JSON) to an XML Infoset.</summary>
      <param name="buffer">The input <see cref="T:System.Byte" /> buffer array from which to read.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> used to prevent Denial of Service attacks when reading untrusted data. </param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryReader" /> that can process JavaScript Object Notation (JSON) data.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonReader(System.IO.Stream,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryReader" /> that can map streams encoded with JavaScript Object Notation (JSON), of a specified size and offset, to an XML Infoset.</summary>
      <param name="stream">The input <see cref="T:System.IO.Stream" /> from which to read.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the reader. If <see langword="null" /> is specified as the value, the reader attempts to auto-detect the encoding.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> used to prevent Denial of Service attacks when reading untrusted data. </param>
      <param name="onClose">The <see cref="T:System.Xml.OnXmlDictionaryReaderClose" /> delegate to call when the reader is closed.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryReader" /> that can read JavaScript Object Notation (JSON).</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonReader(System.IO.Stream,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryReader" /> that can map streams encoded with JavaScript Object Notation (JSON) to an XML Infoset.</summary>
      <param name="stream">The input <see cref="T:System.IO.Stream" /> from which to read.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> used to prevent Denial of Service attacks when reading untrusted data. </param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryReader" /> that can read JavaScript Object Notation (JSON).</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonWriter(System.IO.Stream)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to a stream.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> for the JSON writer.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to the stream based on an XML Infoset.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonWriter(System.IO.Stream,System.Text.Encoding)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to a stream with a specified character encoding.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> for the JSON writer.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the writer. The default encoding is UTF-8.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to the stream based on an XML Infoset.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonWriter(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to a stream with a specified character encoding.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> for the JSON writer.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the writer. The default encoding is UTF-8.</param>
      <param name="ownsStream">If <see langword="true" />, the output stream is closed by the writer when done; otherwise <see langword="false" />. The default value is <see langword="true" />.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to the stream based on an XML Infoset.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonWriter(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Boolean)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to a stream with a specified character.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> for the JSON writer.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the writer. The default encoding is UTF-8.</param>
      <param name="ownsStream">If <see langword="true" />, the output stream is closed by the writer when done; otherwise <see langword="false" />. The default value is <see langword="true" />.</param>
      <param name="indent">If <see langword="true" />, the output uses multiline format, indenting each level properly; otherwise, <see langword="false" />. </param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to the stream based on an XML Infoset.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.Json.JsonReaderWriterFactory.CreateJsonWriter(System.IO.Stream,System.Text.Encoding,System.Boolean,System.Boolean,System.String)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to a stream with a specified character.</summary>
      <param name="stream">The output <see cref="T:System.IO.Stream" /> for the JSON writer.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> that specifies the character encoding used by the writer. The default encoding is UTF-8.</param>
      <param name="ownsStream">If <see langword="true" />, the output stream is closed by the writer when done; otherwise <see langword="false" />. The default value is <see langword="true" />.</param>
      <param name="indent">If <see langword="true" />, the output uses multiline format, indenting each level properly; otherwise, <see langword="false" />.</param>
      <param name="indentChars">The string used to indent each level.</param>
      <returns>An <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes data encoded with JSON to the stream based on an XML Infoset.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.KnownTypeAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class with the name of a method that returns an <see cref="T:System.Collections.IEnumerable" /> of known types. </summary>
      <param name="methodName">The name of the method that returns an <see cref="T:System.Collections.IEnumerable" /> of types used when serializing or deserializing data.</param>
    </member>
    <member name="M:System.Runtime.Serialization.KnownTypeAttribute.#ctor(System.Type)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class with the specified type. </summary>
      <param name="type">The <see cref="T:System.Type" /> that is included as a known type when serializing or deserializing data.</param>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.Runtime.Serialization.StreamingContext)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with the supplied streaming context data. </summary>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains context data.</param>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.Runtime.Serialization.StreamingContext,System.Int32,System.Boolean,System.Runtime.Serialization.Formatters.FormatterAssemblyStyle,System.Runtime.Serialization.ISurrogateSelector)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with the supplied context data; in addition, specifies the maximum number of items in the object to be serialized, and parameters to specify whether extra data is ignored, the assembly loading method, and a surrogate selector.</summary>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains context data.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. </param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type; otherwise, <see langword="false" />.</param>
      <param name="assemblyFormat">A <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> enumeration value that specifies a method for locating and loading assemblies.</param>
      <param name="surrogateSelector">An implementation of the <see cref="T:System.Runtime.Serialization.ISurrogateSelector" />.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="maxItemsInObjectGraph" /> value is less than 0.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.String,System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with the supplied XML root element and namespace.</summary>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.String,System.String,System.Runtime.Serialization.StreamingContext,System.Int32,System.Boolean,System.Runtime.Serialization.Formatters.FormatterAssemblyStyle,System.Runtime.Serialization.ISurrogateSelector)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with the supplied context data and root name and namespace; in addition, specifies the maximum number of items in the object to be serialized, and parameters to specify whether extra data is ignored, the assembly loading method, and a surrogate selector.</summary>
      <param name="rootName">The name of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">The namespace of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains context data.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. </param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type; otherwise, <see langword="false" />.</param>
      <param name="assemblyFormat">A <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> enumeration value that specifies a method for locating and loading assemblies.</param>
      <param name="surrogateSelector">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to handle the legacy type.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="maxItemsInObjectGraph" /> value is less than 0.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with two parameters of type <see cref="T:System.Xml.XmlDictionaryString" /> that contain the root element and namespace used to specify the content.</summary>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the name of the XML element that encloses the content to serialize or deserialize.</param>
      <param name="rootNamespace">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the namespace of the XML element that encloses the content to serialize or deserialize.</param>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.#ctor(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Runtime.Serialization.StreamingContext,System.Int32,System.Boolean,System.Runtime.Serialization.Formatters.FormatterAssemblyStyle,System.Runtime.Serialization.ISurrogateSelector)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> class with the supplied context data, and root name and namespace (as <see cref="T:System.Xml.XmlDictionaryString" />  parameters); in addition, specifies the maximum number of items in the object to be serialized, and parameters to specify whether extra data found is ignored, assembly loading method, and a surrogate selector.</summary>
      <param name="rootName">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the root element of the content.</param>
      <param name="rootNamespace">An <see cref="T:System.Xml.XmlDictionaryString" /> that contains the namespace of the root element.</param>
      <param name="context">A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains context data.</param>
      <param name="maxItemsInObjectGraph">The maximum number of items in the graph to serialize or deserialize. </param>
      <param name="ignoreExtensionDataObject">
            <see langword="true" /> to ignore the data supplied by an extension of the type; otherwise, <see langword="false" />.</param>
      <param name="assemblyFormat">A <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> enumeration value that specifies a method for locating and loading assemblies.</param>
      <param name="surrogateSelector">An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> to handle the legacy type.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="maxItemsInObjectGraph" /> value is less than 0.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.Deserialize(System.IO.Stream)">
      <summary>Deserializes an XML document or stream into an object.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> that contains the XML to deserialize.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.IsStartObject(System.Xml.XmlDictionaryReader)">
      <summary>Determines whether the <see cref="T:System.Xml.XmlDictionaryReader" /> is positioned on an object that can be deserialized using the specified reader.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> that contains the XML to read.</param>
      <returns>
          <see langword="true" />, if the reader is at the start element of the stream to read; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">the <paramref name="reader" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.IsStartObject(System.Xml.XmlReader)">
      <summary>Determines whether the <see cref="T:System.Xml.XmlReader" /> is positioned on an object that can be deserialized using the specified reader.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> that contains the XML to read.</param>
      <returns>
          <see langword="true" /> if the reader is at the start element of the stream to read; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">the <paramref name="reader" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Reads the XML stream or document with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object; also checks whether the object data conforms to the name and namespace used to create the serializer.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML stream or document.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the root name and root namespace used to construct the serializer; <see langword="false" /> to skip the verification.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.ArgumentNullException">the <paramref name="reader" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.ReadObject(System.Xml.XmlReader)">
      <summary>Reads the XML stream or document with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream or document.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.ArgumentNullException">the <paramref name="reader" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.ReadObject(System.Xml.XmlReader,System.Boolean)">
      <summary>Reads the XML stream or document with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object; also checks whether the object data conforms to the name and namespace used to create the serializer.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" /> used to read the XML stream or document.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the root name and root namespace used to construct the serializer; <see langword="false" /> to skip the verification.</param>
      <returns>The deserialized object.</returns>
      <exception cref="T:System.ArgumentNullException">the <paramref name="reader" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.Serialize(System.IO.Stream,System.Object)">
      <summary>Serializes the specified object graph using the specified writer.</summary>
      <param name="stream">The <see cref="T:System.IO.Stream" /> to serialize with.</param>
      <param name="graph">The object to serialize. All child objects of this root object are automatically serialized.</param>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteEndObject(System.Xml.XmlDictionaryWriter)">
      <summary>Writes the closing XML element using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document or stream.</param>
      <exception cref="T:System.ArgumentNullException">the <paramref name="writer" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteEndObject(System.Xml.XmlWriter)">
      <summary>Writes the closing XML element using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <exception cref="T:System.ArgumentNullException">the <paramref name="writer" /> is set to <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the complete content (start, content, and end) of the object to the XML document or stream with the specified <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <param name="graph">The object containing the content to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of object to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteObjectContent(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the XML content using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML content.</param>
      <param name="graph">The object to serialize. All child objects of this root object are automatically serialized.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of object to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteObjectContent(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the XML content using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlWriter" /> used to write the XML content.</param>
      <param name="graph">The object to serialize. All child objects of this root object are automatically serialized.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of object to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteStartObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the opening XML element using an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML element.</param>
      <param name="graph">The object to serialize. All child objects of this root object are automatically serialized.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of object to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.NetDataContractSerializer.WriteStartObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the opening XML element using an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">The <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML element.</param>
      <param name="graph">The object to serialize. All child objects of this root object are automatically serialized.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of object to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.XmlObjectSerializer" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.IsStartObject(System.Xml.XmlDictionaryReader)">
      <summary>Gets a value that specifies whether the <see cref="T:System.Xml.XmlDictionaryReader" /> is positioned over an XML element that can be read.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML stream or document.</param>
      <returns>
          <see langword="true" /> if the reader can read the data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.IsStartObject(System.Xml.XmlReader)">
      <summary>Gets a value that specifies whether the <see cref="T:System.Xml.XmlReader" /> is positioned over an XML element that can be read. </summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> used to read the XML stream or document.</param>
      <returns>
          <see langword="true" /> if the reader is positioned over the starting element; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.ReadObject(System.IO.Stream)">
      <summary>Reads the XML stream or document with a <see cref="T:System.IO.Stream" /> and returns the deserialized object.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> used to read the XML stream or document.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.ReadObject(System.Xml.XmlDictionaryReader)">
      <summary>Reads the XML document or stream with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML document.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.ReadObject(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Reads the XML stream or document with an <see cref="T:System.Xml.XmlDictionaryReader" /> and returns the deserialized object; it also enables you to specify whether the serializer can read the data before attempting to read it.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlDictionaryReader" /> used to read the XML document.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the root name and root namespace; otherwise, <see langword="false" /> to skip the verification.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.ReadObject(System.Xml.XmlReader)">
      <summary>Reads the XML document or stream with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> used to read the XML stream or document.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.ReadObject(System.Xml.XmlReader,System.Boolean)">
      <summary>Reads the XML document or stream with an <see cref="T:System.Xml.XmlReader" /> and returns the deserialized object; it also enables you to specify whether the serializer can read the data before attempting to read it.</summary>
      <param name="reader">An <see cref="T:System.Xml.XmlReader" /> used to read the XML document or stream.</param>
      <param name="verifyObjectName">
            <see langword="true" /> to check whether the enclosing XML element name and namespace correspond to the root name and root namespace; <see langword="false" /> to skip the verification.</param>
      <returns>The deserialized object.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteEndObject(System.Xml.XmlDictionaryWriter)">
      <summary>Writes the end of the object data as a closing XML element to the XML document or stream with an <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document or stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteEndObject(System.Xml.XmlWriter)">
      <summary>Writes the end of the object data as a closing XML element to the XML document or stream with an <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteObject(System.IO.Stream,System.Object)">
      <summary>Writes the complete content (start, content, and end) of the object to the XML document or stream with the specified <see cref="T:System.IO.Stream" />.</summary>
      <param name="stream">A <see cref="T:System.IO.Stream" /> used to write the XML document or stream.</param>
      <param name="graph">The object that contains the data to write to the stream.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the complete content (start, content, and end) of the object to the XML document or stream with the specified <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the content to the XML document or stream.</param>
      <param name="graph">The object that contains the content to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the complete content (start, content, and end) of the object to the XML document or stream with the specified <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <param name="graph">The object that contains the content to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteObjectContent(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes only the content of the object to the XML document or stream using the specified <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document or stream.</param>
      <param name="graph">The object that contains the content to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteObjectContent(System.Xml.XmlWriter,System.Object)">
      <summary>Writes only the content of the object to the XML document or stream with the specified <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document or stream.</param>
      <param name="graph">The object that contains the content to write.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteStartObject(System.Xml.XmlDictionaryWriter,System.Object)">
      <summary>Writes the start of the object's data as an opening XML element using the specified <see cref="T:System.Xml.XmlDictionaryWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlDictionaryWriter" /> used to write the XML document.</param>
      <param name="graph">The object to serialize.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlObjectSerializer.WriteStartObject(System.Xml.XmlWriter,System.Object)">
      <summary>Writes the start of the object's data as an opening XML element using the specified <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An <see cref="T:System.Xml.XmlWriter" /> used to write the XML document.</param>
      <param name="graph">The object to serialize.</param>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized.</exception>
      <exception cref="T:System.ServiceModel.QuotaExceededException">the maximum number of objects to serialize has been exceeded. Check the <see cref="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph" /> property.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlSerializableServices.AddDefaultSchema(System.Xml.Schema.XmlSchemaSet,System.Xml.XmlQualifiedName)">
      <summary>Generates a default schema type given the specified type name and adds it to the specified schema set. </summary>
      <param name="schemas">An <see cref="T:System.Xml.Schema.XmlSchemaSet" /> to add the generated schema type to.</param>
      <param name="typeQName">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the type name to assign the schema to. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="typeQName" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlSerializableServices.ReadNodes(System.Xml.XmlReader)">
      <summary>Reads a set of XML nodes from the specified reader and returns the result.</summary>
      <param name="xmlReader">An <see cref="T:System.Xml.XmlReader" /> used for reading.</param>
      <returns>An array of type <see cref="T:System.Xml.XmlNode" />. </returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="xmlReader" /> argument is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">While reading, a <see langword="null" /> node was encountered.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XmlSerializableServices.WriteNodes(System.Xml.XmlWriter,System.Xml.XmlNode[])">
      <summary>Writes the supplied nodes using the specified writer.</summary>
      <param name="xmlWriter">An <see cref="T:System.Xml.XmlWriter" /> used for writing.</param>
      <param name="nodes">An array of type <see cref="T:System.Xml.XmlNode" /> to write.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="xmlWriter" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XPathQueryGenerator.CreateFromDataContractSerializer(System.Type,System.Reflection.MemberInfo[],System.Text.StringBuilder,System.Xml.XmlNamespaceManager@)">
      <summary>Creates an XPath from a data contract using the specified contract data type, array of metadata elements, the top level element, and namespaces.</summary>
      <param name="type">The type that represents a data contract.</param>
      <param name="pathToMember">The metadata, generated using the <see cref="Overload:System.Type.GetMember" /> method of the <see cref="T:System.Type" /> class, that points to the specific data member used to generate the query.</param>
      <param name="rootElementXpath">The top level element in the xpath.</param>
      <param name="namespaces">The XML namespaces and their prefixes found in the data contract.</param>
      <returns>
          <see cref="T:System.String" />
        The XPath generated from the type and member data.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XPathQueryGenerator.CreateFromDataContractSerializer(System.Type,System.Reflection.MemberInfo[],System.Xml.XmlNamespaceManager@)">
      <summary>Creates an XPath from a data contract using the specified data contract type, array of metadata elements, and namespaces..</summary>
      <param name="type">The type that represents a data contract. </param>
      <param name="pathToMember">The metadata, generated using the <see cref="Overload:System.Type.GetMember" /> method of the <see cref="T:System.Type" /> class, that points to the specific data member used to generate the query.</param>
      <param name="namespaces">The XML namespaces and their prefixes found in the data contract.</param>
      <returns>
          <see cref="T:System.String" />
        The XPath generated from the type and member data.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.XsdDataContractExporter" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.#ctor(System.Xml.Schema.XmlSchemaSet)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.XsdDataContractExporter" /> class with the specified set of schemas.  </summary>
      <param name="schemas">An <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schemas to be exported. </param>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.CanExport(System.Collections.Generic.ICollection{System.Reflection.Assembly})">
      <summary>Gets a value that indicates whether the set of .common language runtime (CLR) types contained in a set of assemblies can be exported. </summary>
      <param name="assemblies">A <see cref="T:System.Collections.Generic.ICollection`1" />   of <see cref="T:System.Reflection.Assembly" /> that contains the assemblies with the types to export.</param>
      <returns>
          <see langword="true" /> if the types can be exported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.CanExport(System.Collections.Generic.ICollection{System.Type})">
      <summary>Gets a value that indicates whether the set of .common language runtime (CLR) types contained in a <see cref="T:System.Collections.Generic.ICollection`1" /> can be exported. </summary>
      <param name="types">A <see cref="T:System.Collections.Generic.ICollection`1" />   that contains the specified types to export.</param>
      <returns>
          <see langword="true" /> if the types can be exported; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.CanExport(System.Type)">
      <summary>Gets a value that indicates whether the specified common language runtime (CLR) type can be exported. </summary>
      <param name="type">The <see cref="T:System.Type" /> to export. </param>
      <returns>
          <see langword="true" /> if the type can be exported; otherwise, <see langword="false" />. </returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.Export(System.Collections.Generic.ICollection{System.Reflection.Assembly})">
      <summary>Transforms the types contained in the specified collection of assemblies. </summary>
      <param name="assemblies">A <see cref="T:System.Collections.Generic.ICollection`1" />   (of <see cref="T:System.Reflection.Assembly" />) that contains the types to export.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="assemblies" /> argument is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">An <see cref="T:System.Reflection.Assembly" /> in the collection is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.Export(System.Collections.Generic.ICollection{System.Type})">
      <summary>Transforms the types contained in the <see cref="T:System.Collections.Generic.ICollection`1" /> passed to this method.</summary>
      <param name="types">A  <see cref="T:System.Collections.Generic.ICollection`1" /> (of <see cref="T:System.Type" />) that contains the types to export.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="types" /> argument is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">A type in the collection is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.Export(System.Type)">
      <summary>Transforms the specified .NET Framework type into an XML schema definition language (XSD) schema. </summary>
      <param name="type">The <see cref="T:System.Type" /> to transform into an XML schema. </param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="type" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.GetRootElementName(System.Type)">
      <summary>Returns the top-level name and namespace for the <see cref="T:System.Type" />.</summary>
      <param name="type">The <see cref="T:System.Type" /> to query.</param>
      <returns>The <see cref="T:System.Xml.XmlQualifiedName" /> that represents the top-level name and namespace for this <see cref="T:System.Type" />, which is written to the stream when writing this object. </returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="type" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.GetSchemaType(System.Type)">
      <summary>Returns the XML schema type for the specified type.</summary>
      <param name="type">The type to return a schema for.</param>
      <returns>An <see cref="T:System.Xml.Schema.XmlSchemaType" /> that contains the XML schema. </returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="type" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractExporter.GetSchemaTypeName(System.Type)">
      <summary>Returns the contract name and contract namespace for the <see cref="T:System.Type" />.</summary>
      <param name="type">The <see cref="T:System.Type" /> that was exported. </param>
      <returns>An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the contract name of the type and its namespace.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="type" /> argument is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.XsdDataContractImporter" /> class.  </summary>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.#ctor(System.CodeDom.CodeCompileUnit)">
      <summary>Initializes a new instance of the <see cref="T:System.Runtime.Serialization.XsdDataContractImporter" /> class with the <see cref="T:System.CodeDom.CodeCompileUnit" /> that will be used to generate CLR code. </summary>
      <param name="codeCompileUnit">The <see cref="T:System.CodeDom.CodeCompileUnit" /> that will be used to store the code. </param>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.CanImport(System.Xml.Schema.XmlSchemaSet)">
      <summary>Gets a value that indicates whether the schemas contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> can be transformed into a <see cref="T:System.CodeDom.CodeCompileUnit" />. </summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schemas to transform. </param>
      <returns>
          <see langword="true" /> if the schemas can be transformed to data contract types; otherwise, <see langword="false" />. </returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">A data contract involved in the import is invalid.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.CanImport(System.Xml.Schema.XmlSchemaSet,System.Collections.Generic.ICollection{System.Xml.XmlQualifiedName})">
      <summary>Gets a value that indicates whether the specified set of types contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> can be transformed into CLR types generated into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schemas to transform.</param>
      <param name="typeNames">An <see cref="T:System.Collections.Generic.ICollection`1" /> of <see cref="T:System.Xml.XmlQualifiedName" /> that represents the set of schema types to import.</param>
      <returns>
          <see langword="true" /> if the schemas can be transformed; otherwise, <see langword="false" />. </returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="typeNames" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">A data contract involved in the import is invalid.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.CanImport(System.Xml.Schema.XmlSchemaSet,System.Xml.Schema.XmlSchemaElement)">
      <summary>Gets a value that indicates whether a specific schema element contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> can be imported.</summary>
      <param name="schemas">An <see cref="T:System.Xml.Schema.XmlSchemaSet" /> to import.</param>
      <param name="element">A specific <see cref="T:System.Xml.Schema.XmlSchemaElement" /> to check in the set of schemas.</param>
      <returns>
          <see langword="true" /> if the element can be imported; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="element" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">A data contract involved in the import is invalid.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.CanImport(System.Xml.Schema.XmlSchemaSet,System.Xml.XmlQualifiedName)">
      <summary>Gets a value that indicates whether the schemas contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> can be transformed into a <see cref="T:System.CodeDom.CodeCompileUnit" />. </summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schema representations. </param>
      <param name="typeName">An <see cref="T:System.Collections.IList" /> of <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the names of the schema types that need to be imported from the <see cref="T:System.Xml.Schema.XmlSchemaSet" />.</param>
      <returns>
          <see langword="true" /> if the schemas can be transformed to data contract types; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="typeName" /> parameter is <see langword="null" />.</exception>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">A data contract involved in the import is invalid.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.GetCodeTypeReference(System.Xml.XmlQualifiedName)">
      <summary>Returns a <see cref="T:System.CodeDom.CodeTypeReference" /> to the CLR type generated for the schema type with the specified <see cref="T:System.Xml.XmlQualifiedName" />.</summary>
      <param name="typeName">The <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the schema type to look up.</param>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> reference to the CLR type generated for the schema type with the <paramref name="typeName" /> specified.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.GetCodeTypeReference(System.Xml.XmlQualifiedName,System.Xml.Schema.XmlSchemaElement)">
      <summary>Returns a <see cref="T:System.CodeDom.CodeTypeReference" /> for the specified XML qualified element and schema element.</summary>
      <param name="typeName">An <see cref="T:System.Xml.XmlQualifiedName" /> that specifies the XML qualified name of the schema type to look up.</param>
      <param name="element">An <see cref="T:System.Xml.Schema.XmlSchemaElement" /> that specifies an element in an XML schema.</param>
      <returns>A <see cref="T:System.CodeDom.CodeTypeReference" /> that represents the type that was generated for the specified schema type.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.GetKnownTypeReferences(System.Xml.XmlQualifiedName)">
      <summary>Returns a list of <see cref="T:System.CodeDom.CodeTypeReference" /> objects that represents the known types generated when generating code for the specified schema type.</summary>
      <param name="typeName">An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the schema type to look up known types for.</param>
      <returns>A <see cref="T:System.Collections.Generic.IList`1" /> of type <see cref="T:System.CodeDom.CodeTypeReference" />.</returns>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.Import(System.Xml.Schema.XmlSchemaSet)">
      <summary>Transforms the specified set of XML schemas contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> into a <see cref="T:System.CodeDom.CodeCompileUnit" />. </summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schema representations to generate CLR types for.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.Import(System.Xml.Schema.XmlSchemaSet,System.Collections.Generic.ICollection{System.Xml.XmlQualifiedName})">
      <summary>Transforms the specified set of schema types contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> into CLR types generated into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schema representations.</param>
      <param name="typeNames">A <see cref="T:System.Collections.Generic.ICollection`1" />  (of <see cref="T:System.Xml.XmlQualifiedName" />) that represents the set of schema types to import.</param>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.Import(System.Xml.Schema.XmlSchemaSet,System.Xml.Schema.XmlSchemaElement)">
      <summary>Transforms the specified schema element in the set of specified XML schemas into a <see cref="T:System.CodeDom.CodeCompileUnit" /> and returns an <see cref="T:System.Xml.XmlQualifiedName" /> that represents the data contract name for the specified element.</summary>
      <param name="schemas">An <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schemas to transform.</param>
      <param name="element">An <see cref="T:System.Xml.Schema.XmlSchemaElement" /> that represents the specific schema element to transform. </param>
      <returns>An <see cref="T:System.Xml.XmlQualifiedName" /> that represents the specified element.</returns>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="element" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Runtime.Serialization.XsdDataContractImporter.Import(System.Xml.Schema.XmlSchemaSet,System.Xml.XmlQualifiedName)">
      <summary>Transforms the specified XML schema type contained in an <see cref="T:System.Xml.Schema.XmlSchemaSet" /> into a <see cref="T:System.CodeDom.CodeCompileUnit" />.</summary>
      <param name="schemas">A <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schema representations. </param>
      <param name="typeName">A <see cref="T:System.Xml.XmlQualifiedName" /> that represents a specific schema type to import.</param>
      <exception cref="T:System.ArgumentNullException">The <paramref name="schemas" /> or <paramref name="typeName" /> parameter is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.IFragmentCapableXmlDictionaryWriter.EndFragment">
      <summary>Ends the processing of an XML fragment.</summary>
    </member>
    <member name="M:System.Xml.IFragmentCapableXmlDictionaryWriter.StartFragment(System.IO.Stream,System.Boolean)">
      <summary>Starts the processing of an XML fragment.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="generateSelfContainedTextFragment">If <see langword="true" />, any namespaces declared outside the fragment is declared again if used inside of it; if <see langword="false" /> the namespaces are not declared again.</param>
    </member>
    <member name="M:System.Xml.IFragmentCapableXmlDictionaryWriter.WriteFragment(System.Byte[],System.Int32,System.Int32)">
      <summary>Writes an XML fragment to the underlying stream of the writer.</summary>
      <param name="buffer">The buffer to write to.</param>
      <param name="offset">The starting position from which to write in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes to be written to the <paramref name="buffer" />.</param>
    </member>
    <member name="M:System.Xml.IStreamProvider.GetStream">
      <summary>Gets a stream.</summary>
      <returns>A <see cref="T:System.IO.Stream" /> object.</returns>
    </member>
    <member name="M:System.Xml.IStreamProvider.ReleaseStream(System.IO.Stream)">
      <summary>Releases a stream to output.</summary>
      <param name="stream">The stream being released.</param>
    </member>
    <member name="M:System.Xml.IXmlBinaryReaderInitializer.SetInput(System.Byte[],System.Int32,System.Int32,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Reinitializes the binary reader using the given input buffer.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">Starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">Number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">
            <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="session">
            <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <param name="onClose">Delegate to call when the reader is closed.</param>
    </member>
    <member name="M:System.Xml.IXmlBinaryReaderInitializer.SetInput(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Reinitializes the binary reader using the given input stream.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">
            <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="session">
            <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <param name="onClose">Delegate to call when the reader is closed.</param>
    </member>
    <member name="M:System.Xml.IXmlBinaryWriterInitializer.SetOutput(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlBinaryWriterSession,System.Boolean)">
      <summary>Specifies initialization requirements for XML binary writers that implement this method.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="session">The <see cref="T:System.Xml.XmlBinaryWriterSession" /> to use.</param>
      <param name="ownsStream">If <see langword="true" />, stream is closed by the writer when done; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.IXmlDictionary.TryLookup(System.Int32,System.Xml.XmlDictionaryString@)">
      <summary>Attempts to look up an entry in the dictionary.</summary>
      <param name="key">Key to look up.</param>
      <param name="result">If <paramref name="key" /> is defined, the <see cref="T:System.Xml.XmlDictionaryString" /> that is mapped to the key; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if key is in the dictionary, otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.IXmlDictionary.TryLookup(System.String,System.Xml.XmlDictionaryString@)">
      <summary>Checks the dictionary for a specified string value.</summary>
      <param name="value">String value being checked for.</param>
      <param name="result">The corresponding <see cref="T:System.Xml.XmlDictionaryString" />, if found; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if value is in the dictionary, otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.IXmlDictionary.TryLookup(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString@)">
      <summary>Checks the dictionary for a specified <see cref="T:System.Xml.XmlDictionaryString" />.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlDictionaryString" /> being checked for.</param>
      <param name="result">The matching <see cref="T:System.Xml.XmlDictionaryString" />, if found; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if <see cref="T:System.Xml.XmlDictionaryString" /> is in the dictionary, otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.IXmlMtomReaderInitializer.SetInput(System.Byte[],System.Int32,System.Int32,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas,System.Int32,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Specifies initialization requirements for XML MTOM readers that read a buffer.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encodings">The possible character encodings of the input.</param>
      <param name="contentType">The Content-Type of the message. Can be <see langword="null" /> if the MIME type is present in the document being read.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply to the reader.</param>
      <param name="maxBufferSize">The maximum allowed size of the buffer.</param>
      <param name="onClose">The delegate to use when an <see langword="onClose" /> event happens.</param>
    </member>
    <member name="M:System.Xml.IXmlMtomReaderInitializer.SetInput(System.IO.Stream,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas,System.Int32,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Specifies initialization requirements for XML MTOM readers that read a stream.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encodings">The possible character encodings of the stream.</param>
      <param name="contentType">The Content-Type of the message. Can be <see langword="null" /> if the MIME type is present in the document being read.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply to the reader.</param>
      <param name="maxBufferSize">The maximum allowed size of the buffer.</param>
      <param name="onClose">The delegate to use when an <see langword="onClose" /> event happens.</param>
    </member>
    <member name="M:System.Xml.IXmlMtomWriterInitializer.SetOutput(System.IO.Stream,System.Text.Encoding,System.Int32,System.String,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>When implemented by an MTOM writer, initializes an MTOM writer.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="maxSizeInBytes">The maximum number of bytes that are buffered in the writer.</param>
      <param name="startInfo">An attribute in the ContentType SOAP header, set to "Application/soap+xml".</param>
      <param name="boundary">The MIME boundary string.</param>
      <param name="startUri">The URI for MIME section.</param>
      <param name="writeMessageHeaders">If <see langword="true" />, write message headers.</param>
      <param name="ownsStream">If <see langword="true" />, the stream is closed by the writer when done; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.IXmlTextReaderInitializer.SetInput(System.Byte[],System.Int32,System.Int32,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Specifies initialization requirements for XML text readers that read a buffer.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
    </member>
    <member name="M:System.Xml.IXmlTextReaderInitializer.SetInput(System.IO.Stream,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Specifies initialization requirements for XML text readers that read a stream.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
    </member>
    <member name="M:System.Xml.IXmlTextWriterInitializer.SetOutput(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Specifies initialization requirements for XML text writers that implement this method.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="ownsStream">If <see langword="true" />, stream is closed by the writer when done; otherwise <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor">
      <summary>Creates a new instance of this class with a new, unique Guid.</summary>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor(System.Byte[])">
      <summary>Creates a new instance of this class using a byte array that represents a <see cref="T:System.Guid" />.</summary>
      <param name="guid">A byte array that represents a <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="guid" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="guid" /> provides less than 16 valid bytes.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor(System.Byte[],System.Int32)">
      <summary>Creates a new instance of this class starting from an offset within a <see langword="byte" /> array that represents a <see cref="T:System.Guid" />.</summary>
      <param name="guid">A <see langword="byte" /> array that represents a <see cref="T:System.Guid" />.</param>
      <param name="offset">Offset position within the<see langword=" byte" /> array that represents a <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="guid" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> less than zero or greater than the length of the array.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="guid " />and<paramref name=" offset" /> provide less than 16 valid bytes.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor(System.Char[],System.Int32,System.Int32)">
      <summary>Creates a new instance of this class starting from an offset within a <see langword="char" /> using a specified number of entries.</summary>
      <param name="chars">A <see langword="char" /> array that represents a <see cref="T:System.Guid" />.</param>
      <param name="offset">Offset position within the <see langword="char" /> array that represents a <see cref="T:System.Guid" />.</param>
      <param name="count">Number of array entries to use, starting from <paramref name="offset" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="chars" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> less than zero or greater than the length of the array.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> less than zero or greater than the length of the array minus <paramref name="offset" />.</exception>
      <exception cref="T:System.FormatException">
              <paramref name="count" /> equals zero.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor(System.Guid)">
      <summary>Creates a new instance of this class using a <see cref="T:System.Guid" />.</summary>
      <param name="guid">A <see cref="T:System.Guid" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="guid" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.#ctor(System.String)">
      <summary>Creates a new instance of this class using a string.</summary>
      <param name="value">A string used to generate the <see cref="T:System.Xml.UniqueId" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.FormatException">Length of<paramref name=" value" /> is zero.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.Equals(System.Object)">
      <summary>Tests whether an object equals this <see cref="T:System.Xml.UniqueId" />.</summary>
      <param name="obj">The object to compare.</param>
      <returns>
          <see langword="true" /> if the object equals this <see cref="T:System.Xml.UniqueId" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.UniqueId.GetHashCode">
      <summary>Creates a hash-code representation of this <see cref="T:System.Xml.UniqueId" />.</summary>
      <returns>An integer hash-code representation of this <see cref="T:System.Xml.UniqueId" />.</returns>
    </member>
    <member name="M:System.Xml.UniqueId.op_Equality(System.Xml.UniqueId,System.Xml.UniqueId)">
      <summary>Overrides the equality operator to test for equality of two <see cref="T:System.Xml.UniqueId" />s.</summary>
      <param name="id1">The first <see cref="T:System.Xml.UniqueId" />.</param>
      <param name="id2">The second <see cref="T:System.Xml.UniqueId" />.</param>
      <returns>
          <see langword="true" /> if the two <see cref="T:System.Xml.UniqueId" />s are equal, or are both <see langword="null" />; <see langword="false" /> if they are not equal, or if only one of them is <see langword="null" />.</returns>
    </member>
    <member name="M:System.Xml.UniqueId.op_Inequality(System.Xml.UniqueId,System.Xml.UniqueId)">
      <summary>Overrides the equality operator to test for inequality of two <see cref="T:System.Xml.UniqueId" />s.</summary>
      <param name="id1">The first <see cref="T:System.Xml.UniqueId" />.</param>
      <param name="id2">The second <see cref="T:System.Xml.UniqueId" />.</param>
      <returns>
          <see langword="true" /> if the overridden equality operator returns <see langword="false" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.UniqueId.ToCharArray(System.Char[],System.Int32)">
      <summary>Puts the <see cref="T:System.Xml.UniqueId" /> value into a <see langword="char" /> array.</summary>
      <param name="chars">The <see langword="char" /> array.</param>
      <param name="offset">Position in the <see langword="char" /> array to start inserting the <see cref="T:System.Xml.UniqueId" /> value.</param>
      <returns>Number of entries in the <see langword="char" /> array filled by the <see cref="T:System.Xml.UniqueId" /> value.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="chars" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> less than zero or greater than the length of the array.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="guid " />and<paramref name=" offset" /> provide less than 16 valid bytes.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.ToString">
      <summary>Displays the <see cref="T:System.Xml.UniqueId" /> value in string format.</summary>
      <returns>A string representation of the <see cref="T:System.Xml.UniqueId" /> value.</returns>
    </member>
    <member name="M:System.Xml.UniqueId.TryGetGuid(System.Byte[],System.Int32)">
      <summary>Tries to get the value of the <see cref="T:System.Xml.UniqueId" /> as a <see cref="T:System.Guid" /> and store it in the given byte array at the specified offest.</summary>
      <param name="buffer">
            <see langword="byte" /> array that will contain the <see cref="T:System.Guid" />.</param>
      <param name="offset">Position in the <see langword="byte" /> array to start inserting the <see cref="T:System.Guid" /> value.</param>
      <returns>
          <see langword="true" /> if the value stored in this instance of <see cref="T:System.Xml.UniqueId" /> is a <see cref="T:System.Guid" />; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> less than zero or greater than the length of the array.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="buffer " />and<paramref name=" offset" /> provide less than 16 valid bytes.</exception>
    </member>
    <member name="M:System.Xml.UniqueId.TryGetGuid(System.Guid@)">
      <summary>Tries to get the value of the <see cref="T:System.Xml.UniqueId" /> as a <see cref="T:System.Guid" />.</summary>
      <param name="guid">The <see cref="T:System.Guid" /> if successful; otherwise <see cref="F:System.Guid.Empty" />.</param>
      <returns>
          <see langword="true" /> if the UniqueId represents a <see cref="T:System.Guid" />; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentException">
              <paramref name="buffer " />and<paramref name=" offset" /> provide less than 16 valid bytes.</exception>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlBinaryReaderSession" /> class.</summary>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.Add(System.Int32,System.String)">
      <summary>Creates an <see cref="T:System.Xml.XmlDictionaryString" /> from the input parameters and adds it to an internal collection.</summary>
      <param name="id">The key value.</param>
      <param name="value">The value.</param>
      <returns>The newly created <see cref="T:System.Xml.XmlDictionaryString" /> that is added to an internal collection.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="id" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">An entry with key = <paramref name="id" /> already exists.</exception>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.Clear">
      <summary>Clears the internal collection of all contents.</summary>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.TryLookup(System.Int32,System.Xml.XmlDictionaryString@)">
      <summary>Checks whether the internal collection contains an entry matching a key.</summary>
      <param name="key">The key to search on.</param>
      <param name="result">When this method returns, contains a string if an entry is found; otherwise, <see langword="null" />. This parameter is passed uninitialized.</param>
      <returns>
          <see langword="true" /> if an entry matching the <paramref name="key" /> was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="key" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.TryLookup(System.String,System.Xml.XmlDictionaryString@)">
      <summary>Checks whether the internal collection contains an entry matching a value.</summary>
      <param name="value">The value to search for.</param>
      <param name="result">When this method returns, contains a string if an entry is found; otherwise, <see langword="null" />. This parameter is passed uninitialized.</param>
      <returns>
          <see langword="true" /> if an entry matching the <paramref name="value" /> was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlBinaryReaderSession.TryLookup(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString@)">
      <summary>Checks whether the internal collection contains an entry matching a value.</summary>
      <param name="value">The value to search for.</param>
      <param name="result">When this method returns, contains a string if an entry is found; otherwise, <see langword="null" />. This parameter is passed uninitialized.</param>
      <returns>
          <see langword="true" /> if an entry matching the <paramref name="value" /> was found; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlBinaryWriterSession.#ctor">
      <summary>Creates an instance of this class. </summary>
    </member>
    <member name="M:System.Xml.XmlBinaryWriterSession.Reset">
      <summary>Clears out the internal collections.</summary>
    </member>
    <member name="M:System.Xml.XmlBinaryWriterSession.TryAdd(System.Xml.XmlDictionaryString,System.Int32@)">
      <summary>Tries to add an <see cref="T:System.Xml.XmlDictionaryString" /> to the internal collection.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlDictionaryString" /> to add.</param>
      <param name="key">The key of the <see cref="T:System.Xml.XmlDictionaryString" /> that was successfully added.</param>
      <returns>
          <see langword="true" /> if the string could be added; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.InvalidOperationException">An entry with key = <paramref name="key" /> already exists.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionary.#ctor">
      <summary>Creates an empty <see cref="T:System.Xml.XmlDictionary" />.</summary>
    </member>
    <member name="M:System.Xml.XmlDictionary.#ctor(System.Int32)">
      <summary>Creates a <see cref="T:System.Xml.XmlDictionary" /> with an initial capacity.</summary>
      <param name="capacity">The initial size of the dictionary.</param>
    </member>
    <member name="M:System.Xml.XmlDictionary.Add(System.String)">
      <summary>Adds a string to the <see cref="T:System.Xml.XmlDictionary" />.</summary>
      <param name="value">String to add to the dictionary.</param>
      <returns>The <see cref="T:System.Xml.XmlDictionaryString" /> that was added.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionary.TryLookup(System.Int32,System.Xml.XmlDictionaryString@)">
      <summary>Attempts to look up an entry in the dictionary.</summary>
      <param name="key">Key to look up.</param>
      <param name="result">If <paramref name="key" /> is defined, the <see cref="T:System.Xml.XmlDictionaryString" /> that is mapped to the key; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if key is in the dictionary, otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionary.TryLookup(System.String,System.Xml.XmlDictionaryString@)">
      <summary>Checks the dictionary for a specified string value.</summary>
      <param name="value">String value being checked for.</param>
      <param name="result">The corresponding <see cref="T:System.Xml.XmlDictionaryString" />, if found; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if value is in the dictionary, otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionary.TryLookup(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString@)">
      <summary>Checks the dictionary for a specified <see cref="T:System.Xml.XmlDictionaryString" />.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlDictionaryString" /> being checked for.</param>
      <param name="result">The matching <see cref="T:System.Xml.XmlDictionaryString" />, if found; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if <see cref="T:System.Xml.XmlDictionaryString" /> is in the dictionary, otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.#ctor">
      <summary>Creates an instance of this class.  Invoked only by its derived classes.</summary>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.Byte[],System.Int32,System.Int32,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is less than zero or greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is less than zero or greater than the buffer length minus the offset.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.Byte[],System.Int32,System.Int32,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="session">The <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is less than zero or greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is less than zero or greater than the buffer length minus the offset.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.Byte[],System.Int32,System.Int32,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="session">The <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <param name="onClose">Delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is less than zero or greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is less than zero or greater than the buffer length minus the offset.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.Byte[],System.Int32,System.Int32,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is less than zero or greater than the buffer length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is less than zero or greater than the buffer length minus the offset.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.Byte[],System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="stream" /> or <paramref name="quotas" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <param name="session">
            <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlDictionaryReaderQuotas,System.Xml.XmlBinaryReaderSession,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="dictionary">
            <see cref="T:System.Xml.XmlDictionary" /> to use.</param>
      <param name="quotas">
            <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="session">
            <see cref="T:System.Xml.XmlBinaryReaderSession" /> to use.</param>
      <param name="onClose">Delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateBinaryReader(System.IO.Stream,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read .NET Binary XML Format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="quotas">The quotas that apply to this operation.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="stream" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateDictionaryReader(System.Xml.XmlReader)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> from an existing <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">An instance of <see cref="T:System.Xml.XmlReader" />.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encoding">The possible character encoding of the input.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="encoding" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encodings">The possible character encodings of the input.</param>
      <param name="contentType">The Content-Type MIME type of the message.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas,System.Int32,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encodings">The possible character encodings of the input.</param>
      <param name="contentType">The Content-Type MIME type of the message.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply to the reader.</param>
      <param name="maxBufferSize">The maximum allowed size of the buffer.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding[],System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encodings">The possible character encodings of the input.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.IO.Stream,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encoding">The possible character encoding of the stream.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="encoding" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.IO.Stream,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encodings">The possible character encodings of the stream.</param>
      <param name="contentType">The Content-Type MIME type of the message.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.IO.Stream,System.Text.Encoding[],System.String,System.Xml.XmlDictionaryReaderQuotas,System.Int32,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encodings">The possible character encodings of the stream.</param>
      <param name="contentType">The Content-Type MIME type of the message.</param>
      <param name="quotas">The MIME type of the message.</param>
      <param name="maxBufferSize">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply to the reader.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateMtomReader(System.IO.Stream,System.Text.Encoding[],System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" /> that reads XML in the MTOM format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encodings">The possible character encodings of the stream.</param>
      <param name="quotas">The quotas to apply to this reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="encoding" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateTextReader(System.Byte[],System.Int32,System.Int32,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> object that specifies the encoding properties to apply.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateTextReader(System.Byte[],System.Int32,System.Int32,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <param name="quotas">The quotas applied to the reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateTextReader(System.Byte[],System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="quotas">The quotas applied to the reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="buffer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateTextReader(System.IO.Stream,System.Text.Encoding,System.Xml.XmlDictionaryReaderQuotas,System.Xml.OnXmlDictionaryReaderClose)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="encoding">The <see cref="T:System.Text.Encoding" /> object that specifies the encoding properties to apply.</param>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> to apply.</param>
      <param name="onClose">The delegate to be called when the reader is closed.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.CreateTextReader(System.IO.Stream,System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="quotas">The quotas applied to the reader.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReader" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.EndCanonicalization">
      <summary>This method is not yet implemented.</summary>
      <exception cref="T:System.NotSupportedException">Always.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.GetAttribute(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>When overridden in a derived class, gets the value of an attribute.</summary>
      <param name="localName">An <see cref="T:System.Xml.XmlDictionaryString" /> that represents the local name of the attribute.</param>
      <param name="namespaceUri">An <see cref="T:System.Xml.XmlDictionaryString" /> that represents the namespace of the attribute.</param>
      <returns>The value of the attribute.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.GetNonAtomizedNames(System.String@,System.String@)">
      <summary>Gets non-atomized names.</summary>
      <param name="localName">The local name.</param>
      <param name="namespaceUri">The namespace for the local <paramref name="localName" />.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IndexOfLocalName(System.String[],System.String)">
      <summary>Gets the index of the local name of the current node within an array of names.</summary>
      <param name="localNames">The string array of local names to be searched.</param>
      <param name="namespaceUri">The namespace of current node.</param>
      <returns>The index of the local name of the current node within an array of names.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="localNames" /> or any of the names in the array is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IndexOfLocalName(System.Xml.XmlDictionaryString[],System.Xml.XmlDictionaryString)">
      <summary>Gets the index of the local name of the current node within an array of names.</summary>
      <param name="localNames">The <see cref="T:System.Xml.XmlDictionaryString" /> array of local names to be searched.</param>
      <param name="namespaceUri">The namespace of current node.</param>
      <returns>The index of the local name of the current node within an array of names.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="localNames" /> or any of the names in the array is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsLocalName(System.String)">
      <summary>Checks whether the parameter, <paramref name="localName" />, is the local name of the current node.</summary>
      <param name="localName">The local name of the current node.</param>
      <returns>
          <see langword="true" /> if <paramref name="localName" /> matches local name of the current node; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsLocalName(System.Xml.XmlDictionaryString)">
      <summary>Checks whether the parameter, <paramref name="localName" />, is the local name of the current node.</summary>
      <param name="localName">An <see cref="T:System.Xml.XmlDictionaryString" /> that represents the local name of the current node.</param>
      <returns>
          <see langword="true" /> if <paramref name="localName" /> matches local name of the current node; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="localName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsNamespaceUri(System.String)">
      <summary>Checks whether the parameter, <paramref name="namespaceUri" />, is the namespace of the current node.</summary>
      <param name="namespaceUri">The namespace of current node.</param>
      <returns>
          <see langword="true" /> if <paramref name="namespaceUri" /> matches namespace of the current node; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsNamespaceUri(System.Xml.XmlDictionaryString)">
      <summary>Checks whether the parameter, <paramref name="namespaceUri" />, is the namespace of the current node.</summary>
      <param name="namespaceUri">Namespace of current node.</param>
      <returns>
          <see langword="true" /> if <paramref name="namespaceUri" /> matches namespace of the current node; otherwise <see langword="false" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsStartArray(System.Type@)">
      <summary>Checks whether the reader is positioned at the start of an array. This class returns <see langword="false" />, but derived classes that have the concept of arrays might return <see langword="true" />.</summary>
      <param name="type">Type of the node, if a valid node; otherwise <see langword="null" />.</param>
      <returns>
          <see langword="true" /> if the reader is positioned at the start of an array node; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Tests whether the first tag is a start tag or empty element tag and if the local name and namespace URI match those of the current node.</summary>
      <param name="localName">An <see cref="T:System.Xml.XmlDictionaryString" /> that represents the local name of the attribute.</param>
      <param name="namespaceUri">An <see cref="T:System.Xml.XmlDictionaryString" /> that represents the namespace of the attribute.</param>
      <returns>
          <see langword="true" /> if the first tag in the array is a start tag or empty element tag and matches <paramref name="localName" /> and <paramref name="namespaceUri" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.IsTextNode(System.Xml.XmlNodeType)">
      <summary>Tests whether the current node is a text node.</summary>
      <param name="nodeType">Type of the node being tested.</param>
      <returns>
          <see langword="true" /> if the node type is <see cref="F:System.Xml.XmlNodeType.Text" />, <see cref="F:System.Xml.XmlNodeType.Whitespace" />, <see cref="F:System.Xml.XmlNodeType.SignificantWhitespace" />, <see cref="F:System.Xml.XmlNodeType.CDATA" />, or <see cref="F:System.Xml.XmlNodeType.Attribute" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.MoveToStartElement">
      <summary>Tests whether the current content node is a start element or an empty element.</summary>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.MoveToStartElement(System.String)">
      <summary>Tests whether the current content node is a start element or an empty element and if the <see cref="P:System.Xml.XmlReader.Name" /> property of the element matches the given argument.</summary>
      <param name="name">The <see cref="P:System.Xml.XmlReader.Name" /> property of the element.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.MoveToStartElement(System.String,System.String)">
      <summary>Tests whether the current content node is a start element or an empty element and if the <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" /> properties of the element matches the given arguments.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.MoveToStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Tests whether the current content node is a start element or an empty element and if the <see cref="P:System.Xml.XmlReader.LocalName" /> and <see cref="P:System.Xml.XmlReader.NamespaceURI" /> properties of the element matches the given argument.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Boolean[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Boolean" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The local name of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.DateTime[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.DateTime" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Decimal[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Decimal" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Double[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Double" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Guid[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Guid" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Int16[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="short" /> integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Int32[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Int64[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="long" /> integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.Single[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="float" /> numbers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the float numbers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of float numbers to put in the array.</param>
      <returns>The umber of float numbers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.String,System.String,System.TimeSpan[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.TimeSpan" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Boolean[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Boolean" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.DateTime[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.DateTime" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Decimal[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Decimal" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Double[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Double" /> nodes type into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Guid[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.Guid" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int16[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="short" /> integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int32[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int64[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="long" /> integers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the integers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of integers to put in the array.</param>
      <returns>The number of integers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Single[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see langword="float" /> numbers into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the float numbers are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of float numbers to put in the array.</param>
      <returns>The number of float numbers put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.TimeSpan[],System.Int32,System.Int32)">
      <summary>Reads repeated occurrences of <see cref="T:System.TimeSpan" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array into which the nodes are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to put in the array.</param>
      <returns>The number of nodes put in the array.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadBooleanArray(System.String,System.String)">
      <summary>Reads repeated occurrences of <see cref="T:System.Boolean" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>A <see cref="T:System.Boolean" /> array of the <see cref="T:System.Boolean" /> nodes.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadBooleanArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads repeated occurrences of <see cref="T:System.Boolean" /> nodes into a typed array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>A <see cref="T:System.Boolean" /> array of the <see cref="T:System.Boolean" /> nodes.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAs(System.Type,System.Xml.IXmlNamespaceResolver)">
      <summary>Converts a node's content to a specified type.</summary>
      <param name="type">The <see cref="T:System.Type" /> of the value to be returned.</param>
      <param name="namespaceResolver">An <see cref="T:System.Xml.IXmlNamespaceResolver" /> object that is used to resolve any namespace prefixes related to type conversion. For example, this can be used when converting an <see cref="T:System.Xml.XmlQualifiedName" /> object to an xs:string. This value can be a null reference.</param>
      <returns>The concatenated text content or attribute value converted to the requested type.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsBase64">
      <summary>Reads the content and returns the Base64 decoded binary bytes.</summary>
      <returns>A byte array that contains the Base64 decoded binary bytes.</returns>
      <exception cref="T:System.Xml.XmlException">The array size is greater than the MaxArrayLength quota for this reader.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsBinHex">
      <summary>Reads the content and returns the <see langword="BinHex" /> decoded binary bytes.</summary>
      <returns>A byte array that contains the <see langword="BinHex" /> decoded binary bytes.</returns>
      <exception cref="T:System.Xml.XmlException">The array size is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsBinHex(System.Int32)">
      <summary>Reads the content and returns the <see langword="BinHex" /> decoded binary bytes.</summary>
      <param name="maxByteArrayContentLength">The maximum array length.</param>
      <returns>A byte array that contains the <see langword="BinHex" /> decoded binary bytes.</returns>
      <exception cref="T:System.Xml.XmlException">The array size is greater than <paramref name="maxByteArrayContentLength" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsChars(System.Char[],System.Int32,System.Int32)">
      <summary>Reads the content into a <see langword="char" /> array.</summary>
      <param name="chars">The array into which the characters are put.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of characters to put in the array.</param>
      <returns>Number of characters read.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsDecimal">
      <summary>Converts a node's content to <see langword="decimal" />.</summary>
      <returns>The <see langword="decimal" /> representation of node's content.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsFloat">
      <summary>Converts a node's content to <see langword="float" />.</summary>
      <returns>The <see langword="float" /> representation of node's content.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsGuid">
      <summary>Converts a node's content to <see langword="guid" />.</summary>
      <returns>The <see langword="guid" /> representation of node's content.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsQualifiedName(System.String@,System.String@)">
      <summary>Converts a node's content to a qualified name representation.</summary>
      <param name="localName">The <see cref="P:System.Xml.XmlReader.LocalName" /> part of the qualified name (<see langword="out" /> parameter).</param>
      <param name="namespaceUri">The <see cref="P:System.Xml.XmlReader.NamespaceURI" /> part of the qualified name (<see langword="out" /> parameter).</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsString">
      <summary>Converts a node's content to a string.</summary>
      <returns>The node content in a string representation.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsString(System.Int32)">
      <summary>Converts a node's content to a string.</summary>
      <param name="maxStringContentLength">The maximum string length.</param>
      <returns>Node content in string representation.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsString(System.String[],System.Int32@)">
      <summary>Converts a node's content to a string.</summary>
      <param name="strings">The array of strings to match content against.</param>
      <param name="index">The index of the entry in <paramref name="strings" /> that matches the content.</param>
      <returns>The node content in a string representation.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="strings" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">An entry in<paramref name=" strings" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsString(System.Xml.XmlDictionaryString[],System.Int32@)">
      <summary>Converts a node's content to a string.</summary>
      <param name="strings">The array of <see cref="T:System.Xml.XmlDictionaryString" /> objects to match content against.</param>
      <param name="index">The index of the entry in <paramref name="strings" /> that matches the content.</param>
      <returns>The node content in a string representation.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="strings" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">An entry in<paramref name=" strings" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsTimeSpan">
      <summary>Converts a node's content to <see cref="T:System.TimeSpan" />.</summary>
      <returns>
          <see cref="T:System.TimeSpan" /> representation of node's content.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadContentAsUniqueId">
      <summary>Converts a node's content to a unique identifier.</summary>
      <returns>The node's content represented as a unique identifier.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDateTimeArray(System.String,System.String)">
      <summary>Converts a node's content to a <see cref="T:System.DateTime" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.DateTime" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDateTimeArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Converts a node's content to a <see cref="T:System.DateTime" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.DateTime" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDecimalArray(System.String,System.String)">
      <summary>Converts a node's content to a <see cref="T:System.Decimal" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.Decimal" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDecimalArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Converts a node's content to a <see cref="T:System.Decimal" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.Decimal" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDoubleArray(System.String,System.String)">
      <summary>Converts a node's content to a <see cref="T:System.Double" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.Double" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadDoubleArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Converts a node's content to a <see cref="T:System.Double" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>The node's content represented as a <see cref="T:System.Double" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsBase64">
      <summary>Converts a node's content to a array of Base64 bytes.</summary>
      <returns>The node's content represented as an array of Base64 bytes.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsBinHex">
      <summary>Converts a node's content to an array of <see langword="BinHex" /> bytes.</summary>
      <returns>The node's content represented as an array of <see langword="BinHex" /> bytes.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsBoolean">
      <summary>Converts an element's content to a <see cref="T:System.Boolean" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.Boolean" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsDateTime">
      <summary>Converts an element's content to a <see cref="T:System.DateTime" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.DateTime" />.</returns>
      <exception cref="T:System.ArgumentException">The element is not in valid format.</exception>
      <exception cref="T:System.FormatException">The element is not in valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsDecimal">
      <summary>Converts an element's content to a <see cref="T:System.Decimal" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.Decimal" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsDouble">
      <summary>Converts an element's content to a <see cref="T:System.Double" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.Double" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsFloat">
      <summary>Converts an element's content to a floating point number (<see cref="T:System.Single" />).</summary>
      <returns>The node's content represented as a floating point number (<see cref="T:System.Single" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsGuid">
      <summary>Converts an element's content to a <see cref="T:System.Guid" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.Guid" />.</returns>
      <exception cref="T:System.ArgumentException">The element is not in valid format.</exception>
      <exception cref="T:System.FormatException">The element is not in valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsInt">
      <summary>Converts an element's content to an integer (<see cref="T:System.Int32" />).</summary>
      <returns>The node's content represented as an integer (<see cref="T:System.Int32" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsLong">
      <summary>Converts an element's content to a long integer (<see cref="T:System.Int64" />).</summary>
      <returns>The node's content represented as a long integer (<see cref="T:System.Int64" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsString">
      <summary>Converts an element's content to a <see cref="T:System.String" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.String" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsTimeSpan">
      <summary>Converts an element's content to a <see cref="T:System.TimeSpan" />.</summary>
      <returns>The node's content represented as a <see cref="T:System.TimeSpan" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadElementContentAsUniqueId">
      <summary>Converts an element's content to a unique identifier.</summary>
      <returns>The node's content represented as a unique identifier.</returns>
      <exception cref="T:System.ArgumentException">The element is not in valid format.</exception>
      <exception cref="T:System.FormatException">The element is not in valid format.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadFullStartElement">
      <summary>Checks whether the current node is an element and advances the reader to the next node.</summary>
      <exception cref="T:System.Xml.XmlException">
              <see cref="M:System.Xml.XmlDictionaryReader.IsStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)" /> returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadFullStartElement(System.String)">
      <summary>Checks whether the current node is an element with the given <paramref name="name" /> and advances the reader to the next node.</summary>
      <param name="name">The qualified name of the element.</param>
      <exception cref="T:System.Xml.XmlException">
              <see cref="M:System.Xml.XmlDictionaryReader.IsStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)" /> returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadFullStartElement(System.String,System.String)">
      <summary>Checks whether the current node is an element with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> and advances the reader to the next node.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <exception cref="T:System.Xml.XmlException">
              <see cref="M:System.Xml.XmlDictionaryReader.IsStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)" /> returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadFullStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Checks whether the current node is an element with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> and advances the reader to the next node.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <exception cref="T:System.Xml.XmlException">
              <see cref="M:System.Xml.XmlDictionaryReader.IsStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)" /> returns <see langword="false" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadGuidArray(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see cref="T:System.Guid" />.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see cref="T:System.Guid" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadGuidArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see cref="T:System.Guid" />.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see cref="T:System.Guid" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt16Array(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="short" /> integers (<see cref="T:System.Int16" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="short" /> integers (<see cref="T:System.Int16" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt16Array(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="short" /> integers (<see cref="T:System.Int16" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="short" /> integers (<see cref="T:System.Int16" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt32Array(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of integers (<see cref="T:System.Int32" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of integers (<see cref="T:System.Int32" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt32Array(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of integers (<see cref="T:System.Int32" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of integers (<see cref="T:System.Int32" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt64Array(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="long" /> integers (<see cref="T:System.Int64" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="long" /> integers (<see cref="T:System.Int64" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadInt64Array(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="long" /> integers (<see cref="T:System.Int64" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="long" /> integers (<see cref="T:System.Int64" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadSingleArray(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="float" /> numbers (<see cref="T:System.Single" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="float" /> numbers (<see cref="T:System.Single" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadSingleArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into an array of <see langword="float" /> numbers (<see cref="T:System.Single" />).</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>An array of <see langword="float" /> numbers (<see cref="T:System.Single" />).</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Checks whether the current node is an element with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> and advances the reader to the next node.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadString">
      <summary>Reads the contents of the current node into a string.</summary>
      <returns>A string that contains the contents of the current node.</returns>
      <exception cref="T:System.InvalidOperationException">Unable to read the contents of the current node.</exception>
      <exception cref="T:System.Xml.XmlException">Maximum allowed string length exceeded.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadString(System.Int32)">
      <summary>Reads the contents of the current node into a string with a given maximum length.</summary>
      <param name="maxStringContentLength">Maximum allowed string length.</param>
      <returns>A string that contains the contents of the current node.</returns>
      <exception cref="T:System.InvalidOperationException">Unable to read the contents of the current node.</exception>
      <exception cref="T:System.Xml.XmlException">Maximum allowed string length exceeded.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadTimeSpanArray(System.String,System.String)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into a <see cref="T:System.TimeSpan" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>A <see cref="T:System.TimeSpan" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadTimeSpanArray(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Reads the contents of a series of nodes with the given <paramref name="localName" /> and <paramref name="namespaceUri" /> into a <see cref="T:System.TimeSpan" /> array.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <returns>A <see cref="T:System.TimeSpan" /> array.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.ReadValueAsBase64(System.Byte[],System.Int32,System.Int32)">
      <summary>Not implemented.</summary>
      <param name="buffer">The buffer from which to read.</param>
      <param name="offset">The starting position from which to read in <paramref name="buffer" />.</param>
      <param name="count">The number of bytes that can be read from <paramref name="buffer" />.</param>
      <returns>Not implemented.</returns>
      <exception cref="T:System.NotSupportedException">Always.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.StartCanonicalization(System.IO.Stream,System.Boolean,System.String[])">
      <summary>This method is not yet implemented.</summary>
      <param name="stream">The stream to read from.</param>
      <param name="includeComments">Determines whether comments are included.</param>
      <param name="inclusivePrefixes">The prefixes to be included.</param>
      <exception cref="T:System.NotSupportedException">Always.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.TryGetArrayLength(System.Int32@)">
      <summary>Not implemented in this class (it always returns <see langword="false" />). May be overridden in derived classes.</summary>
      <param name="count">Returns 0, unless overridden in a derived class.</param>
      <returns>
          <see langword="false" />, unless overridden in a derived class.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.TryGetBase64ContentLength(System.Int32@)">
      <summary>Not implemented in this class (it always returns <see langword="false" />). May be overridden in derived classes.</summary>
      <param name="length">Returns 0, unless overridden in a derived class.</param>
      <returns>
          <see langword="false" />, unless overridden in a derived class.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.TryGetLocalNameAsDictionaryString(System.Xml.XmlDictionaryString@)">
      <summary>Not implemented in this class (it always returns <see langword="false" />). May be overridden in derived classes.</summary>
      <param name="localName">Returns <see langword="null" />, unless overridden in a derived class. .</param>
      <returns>
          <see langword="false" />, unless overridden in a derived class.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.TryGetNamespaceUriAsDictionaryString(System.Xml.XmlDictionaryString@)">
      <summary>Not implemented in this class (it always returns <see langword="false" />). May be overridden in derived classes.</summary>
      <param name="namespaceUri">Returns <see langword="null" />, unless overridden in a derived class.</param>
      <returns>
          <see langword="false" />, unless overridden in a derived class.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReader.TryGetValueAsDictionaryString(System.Xml.XmlDictionaryString@)">
      <summary>Not implemented in this class (it always returns <see langword="false" />). May be overridden in derived classes.</summary>
      <param name="value">Returns <see langword="null" />, unless overridden in a derived class.</param>
      <returns>
          <see langword="false" />, unless overridden in a derived class.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryReaderQuotas.#ctor">
      <summary>Creates a new instance of this class. </summary>
    </member>
    <member name="M:System.Xml.XmlDictionaryReaderQuotas.CopyTo(System.Xml.XmlDictionaryReaderQuotas)">
      <summary>Sets the properties on a passed-in quotas instance, based on the values in this instance.</summary>
      <param name="quotas">The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> instance to which to copy values.</param>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value, but quota values are read-only for the passed in instance.</exception>
      <exception cref="T:System.ArgumentNullException">Passed in target<paramref name=" quotas" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryString.#ctor(System.Xml.IXmlDictionary,System.String,System.Int32)">
      <summary>Creates an instance of this class. </summary>
      <param name="dictionary">The <see cref="T:System.Xml.IXmlDictionary" /> containing this instance.</param>
      <param name="value">The string that is the value of the dictionary entry.</param>
      <param name="key">The integer that is the key of the dictionary entry.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="dictionary" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="key" /> is less than 0 or greater than <see cref="F:System.Int32.MaxValue" /> / 4.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryString.ToString">
      <summary>Displays a text representation of this object.</summary>
      <returns>The string value for this instance of the class. </returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Xml.XmlDictionaryWriter" /> class.</summary>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateBinaryWriter(System.IO.Stream)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes WCF binary XML format.</summary>
      <param name="stream">The stream to write to.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateBinaryWriter(System.IO.Stream,System.Xml.IXmlDictionary)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes WCF binary XML format.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use as the shared dictionary.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateBinaryWriter(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlBinaryWriterSession)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes WCF binary XML format.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use as the shared dictionary.</param>
      <param name="session">The <see cref="T:System.Xml.XmlBinaryWriterSession" /> to use.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateBinaryWriter(System.IO.Stream,System.Xml.IXmlDictionary,System.Xml.XmlBinaryWriterSession,System.Boolean)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes WCF binary XML format.</summary>
      <param name="stream">The stream from which to read.</param>
      <param name="dictionary">The <see cref="T:System.Xml.XmlDictionary" /> to use as the shared dictionary.</param>
      <param name="session">The <see cref="T:System.Xml.XmlBinaryWriterSession" /> to use.</param>
      <param name="ownsStream">
            <see langword="true" /> to indicate that the stream is closed by the writer when done; otherwise <see langword="false" />.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateDictionaryWriter(System.Xml.XmlWriter)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> from an existing <see cref="T:System.Xml.XmlWriter" />.</summary>
      <param name="writer">An instance of <see cref="T:System.Xml.XmlWriter" />.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="writer" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateMtomWriter(System.IO.Stream,System.Text.Encoding,System.Int32,System.String)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes XML in the MTOM format.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="maxSizeInBytes">The maximum number of bytes that are buffered in the writer.</param>
      <param name="startInfo">An attribute in the ContentType SOAP header.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateMtomWriter(System.IO.Stream,System.Text.Encoding,System.Int32,System.String,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes XML in the MTOM format.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="maxSizeInBytes">The maximum number of bytes that are buffered in the writer.</param>
      <param name="startInfo">The content-type of the MIME part that contains the Infoset.</param>
      <param name="boundary">The MIME boundary in the message.</param>
      <param name="startUri">The content-id URI of the MIME part that contains the Infoset.</param>
      <param name="writeMessageHeaders">
            <see langword="true" /> to write message headers.</param>
      <param name="ownsStream">
            <see langword="true" /> to indicate that the stream is closed by the writer when done; otherwise <see langword="false" />.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateTextWriter(System.IO.Stream)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes text XML.</summary>
      <param name="stream">The stream to write to.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateTextWriter(System.IO.Stream,System.Text.Encoding)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes text XML.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the output.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.CreateTextWriter(System.IO.Stream,System.Text.Encoding,System.Boolean)">
      <summary>Creates an instance of <see cref="T:System.Xml.XmlDictionaryWriter" /> that writes text XML.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="encoding">The character encoding of the stream.</param>
      <param name="ownsStream">
            <see langword="true" /> to indicate that the stream is closed by the writer when done; otherwise <see langword="false" />.</param>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryWriter" />.</returns>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.EndCanonicalization">
      <summary>When implemented by a derived class, it stops the canonicalization started by the matching <see cref="M:System.Xml.XmlDictionaryWriter.StartCanonicalization(System.IO.Stream,System.Boolean,System.String[])" /> call.</summary>
      <exception cref="T:System.NotSupportedException">Method is not implemented yet.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.StartCanonicalization(System.IO.Stream,System.Boolean,System.String[])">
      <summary>When implemented by a derived class, it starts the canonicalization.</summary>
      <param name="stream">The stream to write to.</param>
      <param name="includeComments">
            <see langword="true" /> to include comments; otherwise, <see langword="false" />.</param>
      <param name="inclusivePrefixes">The prefixes to be included.</param>
      <exception cref="T:System.NotSupportedException">Method is not implemented yet.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Boolean[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Boolean" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the data.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of values to write from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.DateTime[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.DateTime" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Decimal[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Decimal" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Double[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Double" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Guid[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Guid" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Int16[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int16" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Int32[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int32" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Int64[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int64" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.Single[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Single" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.String,System.String,System.TimeSpan[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.TimeSpan" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Boolean[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Boolean" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.DateTime[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.DateTime" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Decimal[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Decimal" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Double[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Double" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Guid[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Guid" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int16[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int16" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int32[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int32" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Int64[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Int64" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.Single[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.Single" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteArray(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.TimeSpan[],System.Int32,System.Int32)">
      <summary>Writes nodes from a <see cref="T:System.TimeSpan" /> array.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="array">The array that contains the nodes.</param>
      <param name="offset">The starting index in the array.</param>
      <param name="count">The number of nodes to get from the array.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="array" /> is <see langword="null" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="offset" /> is &lt; 0 or &gt; <paramref name="array" /> length.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
              <paramref name="count" /> is &lt; 0 or &gt; <paramref name="array" /> length minus <paramref name="offset" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteAttributeString(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.String)">
      <summary>Writes an attribute qualified name and value.</summary>
      <param name="prefix">The prefix of the attribute.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="namespaceUri">The namespace URI of the attribute.</param>
      <param name="value">The attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteAttributeString(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.String)">
      <summary>Writes an attribute qualified name and value.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="namespaceUri">The namespace URI of the attribute.</param>
      <param name="value">The attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteBase64Async(System.Byte[],System.Int32,System.Int32)">
      <summary>Asynchronously encodes the specified binary bytes as Base64 and writes out the resulting text.</summary>
      <param name="buffer">Byte array to encode. </param>
      <param name="index">The position in the buffer indicating the start of the bytes to write. </param>
      <param name="count">The number of bytes to write. </param>
      <returns>The task that represents the asynchronous <see langword="WriteBase64" /> operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlDictionaryWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlDictionaryWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to <see langword="true" />. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteElementString(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.String)">
      <summary>Writes an element with a text content.</summary>
      <param name="prefix">The prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="value">The element content.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteElementString(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString,System.String)">
      <summary>Writes an element with a text content.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <param name="value">The element content.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteNode(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Writes the current XML node from an <see cref="T:System.Xml.XmlDictionaryReader" />.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" />.</param>
      <param name="defattr">
            <see langword="true" /> to copy the default attributes from the <see langword="XmlReader" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteNode(System.Xml.XmlReader,System.Boolean)">
      <summary>Writes the current XML node from an <see cref="T:System.Xml.XmlReader" />.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlReader" />.</param>
      <param name="defattr">
            <see langword="true" /> to copy the default attributes from the <see cref="T:System.Xml.XmlReader" />; otherwise, <see langword="false" />.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="reader" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteQualifiedName(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes out the namespace-qualified name. This method looks up the prefix that is in scope for the given namespace.</summary>
      <param name="localName">The local name of the qualified name.</param>
      <param name="namespaceUri">The namespace URI of the qualified name.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="localName" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteStartAttribute(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes the start of an attribute with the specified prefix, local name, and namespace URI.</summary>
      <param name="prefix">The namespace prefix.</param>
      <param name="localName">The local name of the attribute.</param>
      <param name="namespaceUri">The namespace URI of the attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteStartAttribute(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes the start of an attribute with the specified local name, and namespace URI.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="namespaceUri">The namespace URI of the attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteStartElement(System.String,System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes the specified start tag and associates it with the given namespace and prefix.</summary>
      <param name="prefix">The prefix of the element.</param>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteStartElement(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes the specified start tag and associates it with the given namespace.</summary>
      <param name="localName">The local name of the element.</param>
      <param name="namespaceUri">The namespace URI of the element.</param>
      <exception cref="T:System.InvalidOperationException">The writer is closed.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteString(System.Xml.XmlDictionaryString)">
      <summary>Writes the given text content.</summary>
      <param name="value">The text to write.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteTextNode(System.Xml.XmlDictionaryReader,System.Boolean)">
      <summary>Writes the text node that an <see cref="T:System.Xml.XmlDictionaryReader" /> is currently positioned on.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" /> to get the text value from.</param>
      <param name="isAttribute">
            <see langword="true" /> to indicate that the reader is positioned on an attribute value or element content; otherwise, <see langword="false" />.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValue(System.Guid)">
      <summary>Writes a <see cref="T:System.Guid" /> value.</summary>
      <param name="value">The <see cref="T:System.Guid" /> value to write.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValue(System.TimeSpan)">
      <summary>Writes a <see cref="T:System.TimeSpan" /> value.</summary>
      <param name="value">The <see cref="T:System.TimeSpan" /> value to write.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValue(System.Xml.IStreamProvider)">
      <summary>Writes a value from an <see cref="T:System.Xml.IStreamProvider" />.</summary>
      <param name="value">The <see cref="T:System.Xml.IStreamProvider" /> value to write.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
      <exception cref="T:System.Xml.XmlException">
              <paramref name="value" /> returns a <see langword="null" /> stream object.</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlDictionaryWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValue(System.Xml.UniqueId)">
      <summary>Writes a Unique Id value.</summary>
      <param name="value">The Unique Id value to write.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="value" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValue(System.Xml.XmlDictionaryString)">
      <summary>Writes a <see cref="T:System.Xml.XmlDictionaryString" /> value.</summary>
      <param name="value">The <see cref="T:System.Xml.XmlDictionaryString" /> value.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteValueAsync(System.Xml.IStreamProvider)">
      <summary>Asynchronously writes a value from an <see cref="T:System.Xml.IStreamProvider" />.</summary>
      <param name="value">The <see cref="T:System.Xml.IStreamProvider" /> value to write.</param>
      <returns>The task that represents the asynchronous <see langword="WriteValue" /> operation.</returns>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlDictionaryWriter" /> method was called before a previous asynchronous operation finished. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “An asynchronous operation is already in progress.”</exception>
      <exception cref="T:System.InvalidOperationException">An <see cref="T:System.Xml.XmlDictionaryWriter" /> asynchronous method was called without setting the <see cref="P:System.Xml.XmlWriterSettings.Async" /> flag to <see langword="true" />. In this case, <see cref="T:System.InvalidOperationException" /> is thrown with the message “Set XmlWriterSettings.Async to true if you want to use Async Methods.”</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteXmlAttribute(System.String,System.String)">
      <summary>Writes a standard XML attribute in the current node.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="value">The value of the attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteXmlAttribute(System.Xml.XmlDictionaryString,System.Xml.XmlDictionaryString)">
      <summary>Writes an XML attribute in the current node.</summary>
      <param name="localName">The local name of the attribute.</param>
      <param name="value">The value of the attribute.</param>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteXmlnsAttribute(System.String,System.String)">
      <summary>Writes a namespace declaration attribute.</summary>
      <param name="prefix">The prefix that is bound to the given namespace.</param>
      <param name="namespaceUri">The namespace to which the prefix is bound.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="M:System.Xml.XmlDictionaryWriter.WriteXmlnsAttribute(System.String,System.Xml.XmlDictionaryString)">
      <summary>Writes a namespace declaration attribute.</summary>
      <param name="prefix">The prefix that is bound to the given namespace.</param>
      <param name="namespaceUri">The namespace to which the prefix is bound.</param>
      <exception cref="T:System.ArgumentNullException">
              <paramref name="namespaceUri" /> is <see langword="null" />.</exception>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsItemNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.ItemName" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the item name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsKeyNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.KeyName" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the key name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.Name" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsNamespaceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.Namespace" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the item namespace has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsReference">
      <summary>Gets or sets a value that indicates whether to preserve object reference data.</summary>
      <returns>
          <see langword="true" /> to keep object reference data; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsReferenceSetExplicitly">
      <summary>Gets whether reference has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the reference has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.IsValueNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.CollectionDataContractAttribute.ValueName" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the value name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.ItemName">
      <summary>Gets or sets a custom name for a collection element.</summary>
      <returns>The name to apply to collection elements.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.KeyName">
      <summary>Gets or sets the custom name for a dictionary key name.</summary>
      <returns>The name to use instead of the default dictionary key name.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.Name">
      <summary>Gets or sets the data contract name for the collection type.</summary>
      <returns>The data contract name for the collection type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.Namespace">
      <summary>Gets or sets the namespace for the data contract.</summary>
      <returns>The namespace of the data contract.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.CollectionDataContractAttribute.ValueName">
      <summary>Gets or sets the custom name for a dictionary value name.</summary>
      <returns>The name to use instead of the default dictionary value name.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.DataContractSerializerSection.DeclaredTypes">
      <summary>Gets a collection of types added to the <see cref="P:System.Runtime.Serialization.DataContractSerializer.KnownTypes" /> property.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection" /> that contains the known types.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.DeclaredTypeElement.KnownTypes">
      <summary>Gets the collection of known types.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.TypeElementCollection" /> that contains the known types.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.DeclaredTypeElement.Type">
      <summary>Gets or sets the name of the declared type that requires a collection of known types. </summary>
      <returns>The name of the declared type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Item(System.Int32)">
      <summary>Gets or sets the configuration element at the specified index location.</summary>
      <param name="index">The index location of the configuration element to return.</param>
      <returns>The <see cref="T:System.Runtime.Serialization.Configuration.DeclaredTypeElement" /> at the specified index.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection.Item(System.String)">
      <summary>Gets or sets the element in the collection of types by its key.</summary>
      <param name="typeName">The name (that functions as a key) of the type to get or set.</param>
      <returns>The specified element (when used to get the element).</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection.EnableUnsafeTypeForwarding">
      <summary>Gets a value that indicates whether unsafe type forwarding is enabled.</summary>
      <returns>
          <see langword="true" /> if unsafe type forwarding is enabled; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.ParameterElement.Index">
      <summary>Gets or sets the position of the generic known type.</summary>
      <returns>The position of the parameter in the containing generic declared type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.ParameterElement.Parameters">
      <summary>Gets the collection of parameters.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.ParameterElementCollection" /> that contains all parameters.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.ParameterElement.Type">
      <summary>Gets or sets the type name of the parameter of the generic known type.</summary>
      <returns>The type name of the parameter.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.ParameterElementCollection.CollectionType">
      <summary>Gets the type of the parameters collection in configuration.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> that contains the type of the parameters collection in configuration.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.ParameterElementCollection.Item(System.Int32)">
      <summary>Gets or sets the element in the collection at the specified position. </summary>
      <param name="index">The position of the element in the collection to get or set.</param>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.ParameterElement" /> from the collection.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.SerializationSectionGroup.DataContractSerializer">
      <summary>Gets the <see cref="T:System.Runtime.Serialization.Configuration.DataContractSerializerSection" /> used to set up the known types collection. </summary>
      <returns>The <see cref="T:System.Runtime.Serialization.Configuration.DataContractSerializerSection" /> used for the serialization configuration section.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.SerializationSectionGroup.NetDataContractSerializer">
      <summary>Gets the <see cref="T:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection" /> used to set up the known types collection. </summary>
      <returns>The <see cref="T:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection" /> object.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.TypeElement.Index">
      <summary>Gets or sets the position of the element.</summary>
      <returns>The position of the element.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.TypeElement.Parameters">
      <summary>Gets a collection of parameters.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Configuration.ParameterElementCollection" /> that contains the parameters for the type. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.TypeElement.Type">
      <summary>Gets or sets the name of the type.</summary>
      <returns>The name of the type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.TypeElementCollection.CollectionType">
      <summary>Gets the collection of elements that represents the types using known types.</summary>
      <returns>A <see cref="T:System.Configuration.ConfigurationElementCollectionType" /> that contains the element objects.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Configuration.TypeElementCollection.Item(System.Int32)">
      <summary>Returns a specific member of the collection by its position.</summary>
      <param name="index">The position of the item to return.</param>
      <returns>The element at the specified position.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ContractNamespaceAttribute.ClrNamespace">
      <summary>Gets or sets the CLR namespace of the data contract type. </summary>
      <returns>The CLR-legal namespace of a type.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ContractNamespaceAttribute.ContractNamespace">
      <summary>Gets the namespace of the data contract members.</summary>
      <returns>The namespace of the data contract members.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.Name" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsNamespaceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.Namespace" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the namespace has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsReference">
      <summary>Gets or sets a value that indicates whether to preserve object reference data.</summary>
      <returns>
          <see langword="true" /> to keep object reference data using standard XML; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.IsReferenceSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataContractAttribute.IsReference" /> has been explicitly set.</summary>
      <returns>
          <see langword="true" /> if the reference has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.Name">
      <summary>Gets or sets the name of the data contract for the type.</summary>
      <returns>The local name of a data contract. The default is the name of the class that the attribute is applied to. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractAttribute.Namespace">
      <summary>Gets or sets the namespace for the data contract for the type.</summary>
      <returns>The namespace of the contract. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.DataContractResolver">
      <summary>Gets the component used to dynamically map <see langword="xsi:type" /> declarations to known contract types.</summary>
      <returns>An implementation of the <see cref="T:System.Runtime.Serialization.DataContractResolver" /> class.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.DataContractSurrogate">
      <summary>Gets a surrogate type that can extend the serialization or deserialization process.</summary>
      <returns>An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> class. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.IgnoreExtensionDataObject">
      <summary>Gets a value that specifies whether to ignore data supplied by an extension of the class when the class is being serialized or deserialized.</summary>
      <returns>
          <see langword="true" /> to omit the extension data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.KnownTypes">
      <summary>Gets a collection of types that may be present in the object graph serialized using this instance of the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> that contains the expected types passed in as known types to the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> constructor.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.MaxItemsInObjectGraph">
      <summary>Gets the maximum number of items in an object graph to serialize or deserialize.</summary>
      <returns>The maximum number of items to serialize or deserialize. The default is <see cref="F:System.Int32.MaxValue" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of items exceeds the maximum value.</exception>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.PreserveObjectReferences">
      <summary>Gets a value that specifies whether to use non-standard XML constructs to preserve object reference data. </summary>
      <returns>
          <see langword="true" /> to keep the references; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializer.SerializeReadOnlyTypes">
      <summary>Gets a value that specifies whether read-only types are serialized.</summary>
      <returns>
          <see langword="true" /> if read-only types are serialized; <see langword="false" /> if all types are serialized.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.DataContractResolver">
      <summary>Gets or sets the component used to dynamically map xsi:type declarations to known contract types.</summary>
      <returns>The component used to dynamically map xsi:type declarations to known contract types.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.DataContractSurrogate">
      <summary>Gets or sets a serialization surrogate.</summary>
      <returns>The serialization surrogate.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.IgnoreExtensionDataObject">
      <summary>Gets or sets a value that specifies whether to ignore data supplied by an extension of the class when the class is being serialized or deserialized.</summary>
      <returns>
          <see langword="True" /> to ignore data supplied by an extension of the class when the class is being serialized or deserialized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.KnownTypes">
      <summary>Gets or sets a collection of types that may be present in the object graph serialized using this instance of the DataContractSerializerSettings.</summary>
      <returns>A collection of types that may be present in the object graph serialized using this instance of the DataContractSerializerSettings.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.MaxItemsInObjectGraph">
      <summary>Gets or sets the maximum number of items in an object graph to serialize or deserialize.</summary>
      <returns>The maximum number of items in an object graph to serialize or deserialize.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.PreserveObjectReferences">
      <summary>Gets or sets a value that specifies whether to use non-standard XML constructs to preserve object reference data.</summary>
      <returns>
          <see langword="True" /> to use non-standard XML constructs to preserve object reference data; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.RootName">
      <summary>Gets or sets the root name of the selected object.</summary>
      <returns>The root name of the selected object.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.RootNamespace">
      <summary>Gets or sets the root namespace for the specified object.</summary>
      <returns>The root namespace for the specified object.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataContractSerializerSettings.SerializeReadOnlyTypes">
      <summary>Gets or sets a value that specifies whether to serialize read only types.</summary>
      <returns>
          <see langword="True" /> to serialize read only types; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.EmitDefaultValue">
      <summary>Gets or sets a value that specifies whether to serialize the default value for a field or property being serialized. </summary>
      <returns>
          <see langword="true" /> if the default value for a member should be generated in the serialization stream; otherwise, <see langword="false" />. The default is <see langword="true" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.IsNameSetExplicitly">
      <summary>Gets whether <see cref="P:System.Runtime.Serialization.DataMemberAttribute.Name" /> has been explicitly set.</summary>
      <returns>Returns <see langword="true" /> if the name has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.IsRequired">
      <summary>Gets or sets a value that instructs the serialization engine that the member must be present when reading or deserializing.</summary>
      <returns>
          <see langword="true" />, if the member is required; otherwise, <see langword="false" />.</returns>
      <exception cref="T:System.Runtime.Serialization.SerializationException">the member is not present.</exception>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.Name">
      <summary>Gets or sets a data member name. </summary>
      <returns>The name of the data member. The default is the name of the target that the attribute is applied to. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.DataMemberAttribute.Order">
      <summary>Gets or sets the order of serialization and deserialization of a member.</summary>
      <returns>The numeric order of serialization or deserialization.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.DateTimeStyles">
      <summary>Gets or sets the formatting options that customize string parsing for some date and time parsing methods.</summary>
      <returns>The formatting options that customize string parsing for some date and time parsing methods.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatProvider">
      <summary>Gets an object that controls formatting.</summary>
    </member>
    <member name="P:System.Runtime.Serialization.DateTimeFormat.FormatString">
      <summary>Gets the format strings to control the formatting produced when a date or time is represented as a string.</summary>
      <returns>The format strings to control the formatting produced when a date or time is represented as a string.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.EnumMemberAttribute.IsValueSetExplicitly">
      <summary>Gets whether the <see cref="P:System.Runtime.Serialization.EnumMemberAttribute.Value" /> has been explicitly set.</summary>
      <returns>
          <see langword="true" /> if the value has been explicitly set; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.EnumMemberAttribute.Value">
      <summary>Gets or sets the value associated with the enumeration member the attribute is applied to. </summary>
      <returns>The value associated with the enumeration member.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ExportOptions.DataContractSurrogate">
      <summary>Gets or sets a serialization surrogate. </summary>
      <returns>An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> interface that can be used to customize how an XML schema representation is exported for a specific type. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.ExportOptions.KnownTypes">
      <summary>Gets the collection of types that may be encountered during serialization or deserialization. </summary>
      <returns>A <see langword="KnownTypes" /> collection that contains types that may be encountered during serialization or deserialization. XML schema representations are exported for all the types specified in this collection by the <see cref="T:System.Runtime.Serialization.XsdDataContractExporter" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.IExtensibleDataObject.ExtensionData">
      <summary>Gets or sets the structure that contains extra data.</summary>
      <returns>An <see cref="T:System.Runtime.Serialization.ExtensionDataObject" /> that contains data that is not recognized as belonging to the data contract.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.CodeProvider">
      <summary>Gets or sets a <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> instance that provides the means to check whether particular options for a target language are supported.</summary>
      <returns>A <see cref="T:System.CodeDom.Compiler.CodeDomProvider" /> that provides the means to check whether particular options for a target language are supported.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.DataContractSurrogate">
      <summary>Gets or sets a data contract surrogate that can be used to modify the code generated during an import operation. </summary>
      <returns>An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> interface that handles schema import. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.EnableDataBinding">
      <summary>Gets or sets a value that specifies whether types in generated code should implement the <see cref="T:System.ComponentModel.INotifyPropertyChanged" /> interface.</summary>
      <returns>
          <see langword="true" /> if the generated code should implement the <see cref="T:System.ComponentModel.INotifyPropertyChanged" /> interface; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.GenerateInternal">
      <summary>Gets or sets a value that specifies whether generated code will be marked internal or public.</summary>
      <returns>
          <see langword="true" /> if the code will be marked <see langword="internal" />; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.GenerateSerializable">
      <summary>Gets or sets a value that specifies whether generated data contract classes will be marked with the <see cref="T:System.SerializableAttribute" /> attribute in addition to the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute.</summary>
      <returns>
          <see langword="true" /> to generate classes with the <see cref="T:System.SerializableAttribute" /> applied; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.ImportXmlType">
      <summary>Gets or sets a value that determines whether all XML schema types, even those that do not conform to a data contract schema, will be imported.</summary>
      <returns>
          <see langword="true" /> to import all schema types; otherwise, <see langword="false" />. The default is <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.Namespaces">
      <summary>Gets a dictionary that contains the mapping of data contract namespaces to the CLR namespaces that must be used to generate code during an import operation.</summary>
      <returns>A <see cref="T:System.Collections.Generic.IDictionary`2" /> that contains the namespace mappings. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.ReferencedCollectionTypes">
      <summary>Gets a collection of types that represents data contract collections that should be referenced when generating code for collections, such as lists or dictionaries of items.</summary>
      <returns>An <see cref="T:System.Collections.Generic.ICollection`1" /> that contains the referenced collection types.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.ImportOptions.ReferencedTypes">
      <summary>Gets a <see cref="T:System.Collections.Generic.IList`1" /> containing types referenced in generated code. </summary>
      <returns>A <see cref="T:System.Collections.Generic.IList`1" /> that contains the referenced types. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.DataContractSurrogate">
      <summary>Gets a surrogate type that is currently active for a given <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> instance. Surrogates can extend the serialization or deserialization process.</summary>
      <returns>An implementation of the <see cref="T:System.Runtime.Serialization.IDataContractSurrogate" /> class. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.DateTimeFormat">
      <summary>Gets the format of the date and time type items in object graph.</summary>
      <returns>The format of the date and time type items in object graph.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.EmitTypeInformation">
      <summary>Gets or sets the data contract JSON serializer settings to emit type information.</summary>
      <returns>The data contract JSON serializer settings to emit type information.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.IgnoreExtensionDataObject">
      <summary>Gets a value that specifies whether unknown data is ignored on deserialization and whether the <see cref="T:System.Runtime.Serialization.IExtensibleDataObject" /> interface is ignored on serialization.</summary>
      <returns>
          <see langword="true" /> to ignore unknown data and <see cref="T:System.Runtime.Serialization.IExtensibleDataObject" />; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.KnownTypes">
      <summary>Gets a collection of types that may be present in the object graph serialized using this instance of the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" />.</summary>
      <returns>A <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" /> that contains the expected types passed in as known types to the <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> constructor.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.MaxItemsInObjectGraph">
      <summary>Gets the maximum number of items in an object graph that the serializer serializes or deserializes in one read or write call.</summary>
      <returns>The maximum number of items to serialize or deserialize. </returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The number of items exceeds the maximum value.</exception>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.SerializeReadOnlyTypes">
      <summary>Gets or sets a value that specifies whether to serialize read only types.</summary>
      <returns>
          <see langword="true" /> to serialize read only types; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializer.UseSimpleDictionaryFormat">
      <summary>Gets or sets a value that specifies whether to use a simple dictionary format.</summary>
      <returns>
          <see langword="true" /> to use a simple dictionary format; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.DataContractSurrogate">
      <summary>Gets or sets a surrogate type that is currently active for given IDataContractSurrogate instance.</summary>
      <returns>The surrogate type that is currently active for given IDataContractSurrogate instance.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.DateTimeFormat">
      <summary>Gets or sets a DateTimeFormat that defines the culturally appropriate format of displaying dates and times.</summary>
      <returns>The DateTimeFormat that defines the culturally appropriate format of displaying dates and times.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.EmitTypeInformation">
      <summary>Gets or sets the data contract JSON serializer settings to emit type information.</summary>
      <returns>The data contract JSON serializer settings to emit type information.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.IgnoreExtensionDataObject">
      <summary>Gets or sets a value that specifies whether to ignore data supplied by an extension of the class when the class is being serialized or deserialized.</summary>
      <returns>
          <see langword="True" /> to ignore data supplied by an extension of the class when the class is being serialized or deserialized; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.KnownTypes">
      <summary>Gets or sets a collection of types that may be present in the object graph serialized using this instance the DataContractJsonSerializerSettings.</summary>
      <returns>A collection of types that may be present in the object graph serialized using this instance the DataContractJsonSerializerSettings.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.MaxItemsInObjectGraph">
      <summary>Gets or sets the maximum number of items in an object graph to serialize or deserialize.</summary>
      <returns>The maximum number of items in an object graph to serialize or deserialize.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.RootName">
      <summary>Gets or sets the root name of the selected object.</summary>
      <returns>The root name of the selected object.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.SerializeReadOnlyTypes">
      <summary>Gets or sets a value that specifies whether to serialize read only types.</summary>
      <returns>
          <see langword="True" /> to serialize read only types; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings.UseSimpleDictionaryFormat">
      <summary>Gets or sets a value that specifies whether to use a simple dictionary format.</summary>
      <returns>
          <see langword="True" /> to use a simple dictionary format; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.KnownTypeAttribute.MethodName">
      <summary>Gets the name of a method that will return a list of types that should be recognized during serialization or deserialization. </summary>
      <returns>A <see cref="T:System.String" /> that contains the name of the method on the type defined by the <see cref="T:System.Runtime.Serialization.KnownTypeAttribute" /> class. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.KnownTypeAttribute.Type">
      <summary>Gets the type that should be recognized during serialization or deserialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. </summary>
      <returns>The <see cref="T:System.Type" /> that is used during serialization or deserialization. </returns>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.AssemblyFormat">
      <summary>Gets a value that specifies a method for locating and loading assemblies.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> enumeration value that specifies a method for locating and loading assemblies.</returns>
      <exception cref="T:System.ArgumentException">The value being set does not correspond to any of the <see cref="T:System.Runtime.Serialization.Formatters.FormatterAssemblyStyle" /> values. </exception>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.Binder">
      <summary>Gets or sets an object that controls class loading.</summary>
      <returns>The <see cref="T:System.Runtime.Serialization.SerializationBinder" /> used with the current formatter.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.Context">
      <summary>Gets or sets the object that enables the passing of context data that is useful while serializing or deserializing.</summary>
      <returns>A <see cref="T:System.Runtime.Serialization.StreamingContext" /> that contains the context data.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.IgnoreExtensionDataObject">
      <summary>Gets a value that specifies whether data supplied by an extension of the object is ignored.</summary>
      <returns>
          <see langword="true" /> to ignore the data supplied by an extension of the type; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.MaxItemsInObjectGraph">
      <summary>Gets the maximum number of items allowed in the object to be serialized.</summary>
      <returns>The maximum number of items allowed in the object. The default is <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.NetDataContractSerializer.SurrogateSelector">
      <summary>Gets or sets an object that assists the formatter when selecting a surrogate for serialization. </summary>
      <returns>An <see cref="T:System.Runtime.Serialization.ISurrogateSelector" /> for selecting a surrogate.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.XsdDataContractExporter.Options">
      <summary>Gets or sets an <see cref="T:System.Runtime.Serialization.ExportOptions" /> that contains options that can be set for the export operation. </summary>
      <returns>An <see cref="T:System.Runtime.Serialization.ExportOptions" /> that contains options used to customize how types are exported to schemas.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.XsdDataContractExporter.Schemas">
      <summary>Gets the collection of exported XML schemas. </summary>
      <returns>An <see cref="T:System.Xml.Schema.XmlSchemaSet" /> that contains the schemas transformed from the set of common language runtime (CLR) types after calling the <see cref="Overload:System.Runtime.Serialization.XsdDataContractExporter.Export" /> method.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.XsdDataContractImporter.CodeCompileUnit">
      <summary>Gets a <see cref="T:System.CodeDom.CodeCompileUnit" /> used for storing the CLR types generated.</summary>
      <returns>A <see cref="T:System.CodeDom.CodeCompileUnit" /> used to store the CLR types generated.</returns>
    </member>
    <member name="P:System.Runtime.Serialization.XsdDataContractImporter.Options">
      <summary>Gets or sets an <see cref="T:System.Runtime.Serialization.ImportOptions" /> that contains settable options for the import operation. </summary>
      <returns>A <see cref="T:System.Runtime.Serialization.ImportOptions" /> that contains settable options. </returns>
    </member>
    <member name="P:System.Xml.IFragmentCapableXmlDictionaryWriter.CanFragment">
      <summary>Gets a value that indicates whether this <see cref="T:System.Xml.XmlDictionaryWriter" /> can process XML fragments. </summary>
      <returns>
          <see langword="true" /> if this <see cref="T:System.Xml.XmlDictionaryWriter" /> can process XML fragments; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.UniqueId.CharArrayLength">
      <summary>Gets the length of the string representation of the <see cref="T:System.Xml.UniqueId" />.</summary>
      <returns>The length of the string representation of the <see cref="T:System.Xml.UniqueId" />.</returns>
    </member>
    <member name="P:System.Xml.UniqueId.IsGuid">
      <summary>Indicates whether the <see cref="T:System.Xml.UniqueId" /> is a <see cref="T:System.Guid" />.</summary>
      <returns>
          <see langword="true" /> if the <see cref="T:System.Xml.UniqueId" /> is a <see cref="T:System.Guid" />; otherwise <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionary.Empty">
      <summary>Gets a <see langword="static" /> empty <see cref="T:System.Xml.IXmlDictionary" />.</summary>
      <returns>A <see langword="static" /> empty <see cref="T:System.Xml.IXmlDictionary" />.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryReader.CanCanonicalize">
      <summary>This property always returns <see langword="false" />. Its derived classes can override to return <see langword="true" /> if they support canonicalization.</summary>
      <returns>Returns <see langword="false" />.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryReader.Quotas">
      <summary>Gets the quota values that apply to the current instance of this class.</summary>
      <returns>The <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> that applies to the current instance of this class. </returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.Max">
      <summary>Gets an instance of this class with all properties set to maximum values.</summary>
      <returns>An instance of <see cref="T:System.Xml.XmlDictionaryReaderQuotas" /> with properties set to <see cref="F:System.Int32.MaxValue" />.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.MaxArrayLength">
      <summary>Gets and sets the maximum allowed array length.</summary>
      <returns>The maximum allowed array length. The default is 16384.</returns>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value, but quota values are read-only for this instance.</exception>
      <exception cref="T:System.ArgumentException">Trying to <see langword="set" /> the value to less than zero.</exception>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.MaxBytesPerRead">
      <summary>Gets and sets the maximum allowed bytes returned for each read.</summary>
      <returns>The maximum allowed bytes returned for each read. The default is 4096.</returns>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value, but quota values are read-only for this instance.</exception>
      <exception cref="T:System.ArgumentException">Trying to <see langword="set" /> the value to less than zero.</exception>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.MaxDepth">
      <summary>Gets and sets the maximum nested node depth.</summary>
      <returns>The maximum nested node depth. The default is 32;</returns>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value and quota values are read-only for this instance.</exception>
      <exception cref="T:System.ArgumentException">Trying to <see langword="set" /> the value is less than zero.</exception>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.MaxNameTableCharCount">
      <summary>Gets and sets the maximum characters allowed in a table name.</summary>
      <returns>The maximum characters allowed in a table name. The default is 16384.</returns>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value, but quota values are read-only for this instance.</exception>
      <exception cref="T:System.ArgumentException">Trying to <see langword="set" /> the value to less than zero.</exception>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.MaxStringContentLength">
      <summary>Gets and sets the maximum string length returned by the reader.</summary>
      <returns>The maximum string length returned by the reader. The default is 8192.</returns>
      <exception cref="T:System.InvalidOperationException">Trying to <see langword="set" /> the value, but quota values are read-only for this instance.</exception>
      <exception cref="T:System.ArgumentException">Trying to <see langword="set" /> the value to less than zero.</exception>
    </member>
    <member name="P:System.Xml.XmlDictionaryReaderQuotas.ModifiedQuotas">
      <summary>Gets the modified quotas for the <see cref="T:System.Xml.XmlDictionaryReaderQuotas" />.</summary>
      <returns>The modified quotas for the <see cref="T:System.Xml.XmlDictionaryReaderQuotas" />.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryString.Dictionary">
      <summary>Represents the <see cref="T:System.Xml.IXmlDictionary" /> passed to the constructor of this instance of <see cref="T:System.Xml.XmlDictionaryString" />.</summary>
      <returns>The <see cref="T:System.Xml.IXmlDictionary" /> for this dictionary entry.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryString.Empty">
      <summary>Gets an <see cref="T:System.Xml.XmlDictionaryString" /> representing the empty string.</summary>
      <returns>An <see cref="T:System.Xml.XmlDictionaryString" /> representing the empty string.</returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryString.Key">
      <summary>Gets the integer key for this instance of the class.  </summary>
      <returns>The integer key for this instance of the class. </returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryString.Value">
      <summary>Gets the string value for this instance of the class.  </summary>
      <returns>The string value for this instance of the class. </returns>
    </member>
    <member name="P:System.Xml.XmlDictionaryWriter.CanCanonicalize">
      <summary>This property always returns <see langword="false" />. Its derived classes can override to return <see langword="true" /> if they support canonicalization.</summary>
      <returns>
          <see langword="false" /> in all cases.</returns>
    </member>
    <member name="T:System.Runtime.Serialization.CollectionDataContractAttribute">
      <summary>When applied to a collection type, enables custom specification of the collection item elements. This attribute can be applied only to types that are recognized by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> as valid, serializable collections. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.DataContractSerializerSection">
      <summary>Handles the XML elements used to configure serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.DeclaredTypeElement">
      <summary>Handles the XML elements used to add known types that are used for serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.DeclaredTypeElementCollection">
      <summary>Handles the XML elements used to configure XML serialization using the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.NetDataContractSerializerSection">
      <summary>Handles the XML elements used to configure serialization by the <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.ParameterElement">
      <summary>Handles the XML elements used to configure XML serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.ParameterElementCollection">
      <summary>Handles the XML elements used to configure serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.SerializationSectionGroup">
      <summary>Handles the XML elements used to configure serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.TypeElement">
      <summary>Handles the XML elements used to configure serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Configuration.TypeElementCollection">
      <summary>Handles the XML elements used to configure the known types used for serialization by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.ContractNamespaceAttribute">
      <summary>Specifies the CLR namespace and XML namespace of the data contract. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractAttribute">
      <summary>Specifies that the type defines or implements a data contract and is serializable by a serializer, such as the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. To make their type serializable, type authors must define a data contract for their type. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractResolver">
      <summary>Provides a mechanism for dynamically mapping types to and from <see langword="xsi:type" /> representations during serialization and deserialization.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractSerializer">
      <summary>Serializes and deserializes an instance of a type into an XML stream or document using a supplied data contract. This class cannot be inherited. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractSerializerExtensions">
      <summary>Extends the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> class by providing methods for setting and getting an <see cref="T:System.Runtime.Serialization.ISerializationSurrogateProvider" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataContractSerializerSettings">
      <summary>Specifies data contract serializer settings.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.DataMemberAttribute">
      <summary>When applied to the member of a type, specifies that the member is part of a data contract and is serializable by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.DateTimeFormat">
      <summary>Specifies date-time format options.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.EmitTypeInformation">
      <summary>Specifies how often to emit type information.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.AsNeeded">
      <summary>As needed emit type information.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Always">
      <summary>Always to emit type information.</summary>
    </member>
    <member name="F:System.Runtime.Serialization.EmitTypeInformation.Never">
      <summary>Never to emit type information.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.EnumMemberAttribute">
      <summary>Specifies that the field is an enumeration member and should be serialized.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.ExportOptions">
      <summary>Represents the options that can be set for an <see cref="T:System.Runtime.Serialization.XsdDataContractExporter" />.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.ExtensionDataObject">
      <summary>Stores data from a versioned data contract that has been extended by adding new members.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.IDataContractSurrogate">
      <summary>Provides the methods needed to substitute one type for another by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> during serialization, deserialization, and export and import of XML schema documents (XSD). </summary>
    </member>
    <member name="T:System.Runtime.Serialization.IExtensibleDataObject">
      <summary>Provides a data structure to store extra data encountered by the <see cref="T:System.Runtime.Serialization.XmlObjectSerializer" /> during deserialization of a type marked with the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.IgnoreDataMemberAttribute">
      <summary>When applied to the member of a type, specifies that the member is not part of a data contract and is not serialized.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.ImportOptions">
      <summary>Represents the options that can be set on an <see cref="T:System.Runtime.Serialization.XsdDataContractImporter" />. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.InvalidDataContractException">
      <summary>The exception that is thrown when the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> or <see cref="T:System.Runtime.Serialization.NetDataContractSerializer" /> encounters an invalid data contract during serialization and deserialization. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.ISerializationSurrogateProvider">
      <summary>Provides the methods needed to construct a serialization surrogate that extends the <see cref="T:System.Runtime.Serialization.DataContractSerializer" />. A serialization surrogate is used during serialization and deserialization to substitute one type for another. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializer">
      <summary>Serializes objects to the JavaScript Object Notation (JSON) and deserializes JSON data to objects. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.DataContractJsonSerializerSettings">
      <summary>Specifies <see cref="T:System.Runtime.Serialization.Json.DataContractJsonSerializer" /> settings.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.IXmlJsonReaderInitializer">
      <summary>Specifies the interface for initializing a JavaScript Object Notation (JSON) reader when reusing them to read from a particular stream or buffer.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.IXmlJsonWriterInitializer">
      <summary>Specifies the interface for initializing a JavaScript Object Notation (JSON) writer when reusing them to write to a particular output stream.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.Json.JsonReaderWriterFactory">
      <summary>Produces instances of <see cref="T:System.Xml.XmlDictionaryReader" /> that can read data encoded with JavaScript Object Notation (JSON) from a stream or buffer and map it to an XML Infoset and instances of <see cref="T:System.Xml.XmlDictionaryWriter" /> that can map an XML Infoset to JSON and write JSON-encoded data to a stream. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.KnownTypeAttribute">
      <summary>Specifies types that should be recognized by the <see cref="T:System.Runtime.Serialization.DataContractSerializer" /> when serializing or deserializing a given type. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.NetDataContractSerializer">
      <summary>Serializes and deserializes an instance of a type into XML stream or document using the supplied .NET Framework types. This class cannot be inherited.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.XmlObjectSerializer">
      <summary>Provides the base class used to serialize objects as XML streams or documents. This class is abstract.</summary>
      <exception cref="T:System.Runtime.Serialization.InvalidDataContractException">the type being serialized does not conform to data contract rules. For example, the <see cref="T:System.Runtime.Serialization.DataContractAttribute" /> attribute has not been applied to the type.</exception>
      <exception cref="T:System.Runtime.Serialization.SerializationException">there is a problem with the instance being serialized. </exception>
    </member>
    <member name="T:System.Runtime.Serialization.XmlSerializableServices">
      <summary>Contains methods for reading and writing XML. </summary>
    </member>
    <member name="T:System.Runtime.Serialization.XPathQueryGenerator">
      <summary>When given a class representing a data contract, and metadata representing a member of the contract, produces an XPath query for the member.</summary>
    </member>
    <member name="T:System.Runtime.Serialization.XsdDataContractExporter">
      <summary>Allows the transformation of a set of .NET Framework types that are used in data contracts into an XML schema file (.xsd). </summary>
    </member>
    <member name="T:System.Runtime.Serialization.XsdDataContractImporter">
      <summary>Allows the transformation of a set of XML schema files (.xsd) into common language runtime (CLR) types. </summary>
    </member>
    <member name="T:System.Xml.IFragmentCapableXmlDictionaryWriter">
      <summary>Contains properties and methods that when implemented by a <see cref="T:System.Xml.XmlDictionaryWriter" />, allows processing of XML fragments.</summary>
    </member>
    <member name="T:System.Xml.IStreamProvider">
      <summary>Represents an interface that can be implemented by classes providing streams.</summary>
    </member>
    <member name="T:System.Xml.IXmlBinaryReaderInitializer">
      <summary>Provides methods for reinitializing a binary reader to read a new document.</summary>
    </member>
    <member name="T:System.Xml.IXmlBinaryWriterInitializer">
      <summary>Specifies implementation requirements for XML binary writers that derive from this interface.</summary>
    </member>
    <member name="T:System.Xml.IXmlDictionary">
      <summary>An <see langword="interface" /> that defines the contract that an Xml dictionary must implement to be used by <see cref="T:System.Xml.XmlDictionaryReader" /> and <see cref="T:System.Xml.XmlDictionaryWriter" /> implementations.</summary>
    </member>
    <member name="T:System.Xml.IXmlMtomReaderInitializer">
      <summary>Specifies implementation requirements for XML MTOM readers that derive from this interface.</summary>
    </member>
    <member name="T:System.Xml.IXmlMtomWriterInitializer">
      <summary>When implemented by an MTOM writer, this interface ensures initialization for an MTOM writer.</summary>
    </member>
    <member name="T:System.Xml.IXmlTextReaderInitializer">
      <summary>Specifies implementation requirements for XML text readers that derive from this interface.</summary>
    </member>
    <member name="T:System.Xml.IXmlTextWriterInitializer">
      <summary>Specifies implementation requirements for XML text writers that derive from this interface.</summary>
    </member>
    <member name="T:System.Xml.OnXmlDictionaryReaderClose">
      <summary>
          <see langword="delegate" /> for a callback method when closing the reader.</summary>
      <param name="reader">The <see cref="T:System.Xml.XmlDictionaryReader" /> that fires the OnClose event.</param>
    </member>
    <member name="T:System.Xml.UniqueId">
      <summary>A unique identifier optimized for Guids.</summary>
    </member>
    <member name="T:System.Xml.XmlBinaryReaderSession">
      <summary>Enables optimized strings to be managed in a dynamic way.</summary>
    </member>
    <member name="T:System.Xml.XmlBinaryWriterSession">
      <summary>Enables using a dynamic dictionary to compress common strings that appear in a message and maintain state.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionary">
      <summary>Implements a dictionary used to optimize Windows Communication Foundation (WCF)'s XML reader/writer implementations.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionaryReader">
      <summary>An <see langword="abstract" /> class that the Windows Communication Foundation (WCF) derives from <see cref="T:System.Xml.XmlReader" /> to do serialization and deserialization.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionaryReaderQuotas">
      <summary>Contains configurable quota values for XmlDictionaryReaders.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionaryReaderQuotaTypes">
      <summary>Enumerates the configurable quota values for XmlDictionaryReaders.</summary>
    </member>
    <member name="F:System.Xml.XmlDictionaryReaderQuotaTypes.MaxDepth">
      <summary>Specifies the maximum nested node depth.</summary>
    </member>
    <member name="F:System.Xml.XmlDictionaryReaderQuotaTypes.MaxStringContentLength">
      <summary>Specifies the maximum string length returned by the reader.</summary>
    </member>
    <member name="F:System.Xml.XmlDictionaryReaderQuotaTypes.MaxArrayLength">
      <summary>Specifies the maximum allowed array length.</summary>
    </member>
    <member name="F:System.Xml.XmlDictionaryReaderQuotaTypes.MaxBytesPerRead">
      <summary>Specifies the maximum allowed bytes returned for each read.</summary>
    </member>
    <member name="F:System.Xml.XmlDictionaryReaderQuotaTypes.MaxNameTableCharCount">
      <summary>Specifies the maximum characters allowed in a table name.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionaryString">
      <summary>Represents an entry stored in a <see cref="T:System.Xml.XmlDictionary" />.</summary>
    </member>
    <member name="T:System.Xml.XmlDictionaryWriter">
      <summary>Represents an abstract class that Windows Communication Foundation (WCF) derives from <see cref="T:System.Xml.XmlWriter" /> to do serialization and deserialization.</summary>
    </member>
  </members>
</doc>