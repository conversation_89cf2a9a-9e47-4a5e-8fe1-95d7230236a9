﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <members>
    <member name="N:Accessibility">
      <summary>The <see cref="N:Accessibility" /> and all of its exposed members are part of a managed wrapper for the Component Object Model (COM) accessibility interface.</summary>
    </member>
    <member name="N:Microsoft.Activities.Build">
      <summary>Classes related to workflow build tasks.</summary>
    </member>
    <member name="N:Microsoft.Activities.Build.Debugger">
      <summary>Classes related to the build debugger.</summary>
    </member>
    <member name="N:Microsoft.Activities.Build.Expressions">
      <summary>Classes related to build expressions.</summary>
    </member>
    <member name="N:Microsoft.Activities.Build.Validation">
      <summary>The <see cref="N:Microsoft.Activities.Build.Validation" />provides classes related to build validation.</summary>
    </member>
    <member name="N:Microsoft.Build.BuildEngine">
      <summary>The <see cref="N:Microsoft.Build.BuildEngine" /> namespace represents the MSBuild engine. For information, see MSBuild. </summary>
    </member>
    <member name="N:Microsoft.Build.Construction">
      <summary>Contains types that the MSBuild object model uses to construct project roots with unevaluated values. Each project root corresponds to a project or targets file. For information, see MSBuild. </summary>
    </member>
    <member name="N:Microsoft.Build.Conversion">
      <summary>Contains types that Visual Studio uses to convert project files from an older version of Visual Studio.</summary>
    </member>
    <member name="N:Microsoft.Build.Debugging">
      <summary>Contains type for managing the debugger.</summary>
    </member>
    <member name="N:Microsoft.Build.Evaluation">
      <summary>Contains types that the MSBuild object model uses to evaluate projects. Each project is associated with one or more project roots. For information, see MSBuild. </summary>
    </member>
    <member name="N:Microsoft.Build.Exceptions">
      <summary>Contains exception types that may be thrown during the build process. </summary>
    </member>
    <member name="N:Microsoft.Build.Execution">
      <summary>Contains types that the MSBuild object model uses to build projects. For information, see MSBuild.</summary>
    </member>
    <member name="N:Microsoft.Build.Framework">
      <summary>The <see cref="N:Microsoft.Build.Framework" /> namespace contains the types that define how tasks and loggers interact with the MSBuild engine. For information, see MSBuild.</summary>
    </member>
    <member name="N:Microsoft.Build.Framework.XamlTypes">
      <summary>Classes used to represent XAML types parsed from files, rules, and other sources.</summary>
    </member>
    <member name="N:Microsoft.Build.Logging">
      <summary>Contains types used for logging the progress of a build. For information, see MSBuild. </summary>
    </member>
    <member name="N:Microsoft.Build.Tasks">
      <summary>The <see cref="N:Microsoft.Build.Tasks" /> namespace contains the implementation of all tasks shipping with MSBuild. For information, see MSBuild.</summary>
    </member>
    <member name="N:Microsoft.Build.Tasks.Deployment.Bootstrapper">
      <summary>The <see cref="N:Microsoft.Build.Tasks.Deployment.Bootstrapper" /> namespace contains classes used internally by MSBuild.</summary>
    </member>
    <member name="N:Microsoft.Build.Tasks.Deployment.ManifestUtilities">
      <summary>The <see cref="N:Microsoft.Build.Tasks.Deployment.ManifestUtilities" /> namespace contains classes that MSBuild uses.</summary>
    </member>
    <member name="N:Microsoft.Build.Tasks.Hosting">
      <summary>The <see cref="N:Microsoft.Build.Tasks.Hosting" /> namespace contains classes used internally by MSBuild.</summary>
    </member>
    <member name="N:Microsoft.Build.Tasks.Windows">
      <summary>The <see cref="N:Microsoft.Build.Tasks.Windows" /> namespace contains the implementation of all tasks shipping with MSBuild. The types in this namespace support the Windows Presentation Foundation (WPF) infrastructure and are not intended to be used directly from your code.</summary>
    </member>
    <member name="N:Microsoft.Build.Tasks.Xaml">
      <summary>Provides classes related to XAML build tasks.</summary>
    </member>
    <member name="N:Microsoft.Build.Utilities">
      <summary>The <see cref="N:Microsoft.Build.Utilities" /> namespace provides helper classes that you can use to create your own MSBuild loggers and tasks. For information, see MSBuild.</summary>
    </member>
    <member name="N:Microsoft.CSharp">
      <summary>The <see cref="N:Microsoft.CSharp" /> namespace contains classes that support compilation and code generation using the C# language.</summary>
    </member>
    <member name="N:Microsoft.CSharp.Activities">
      <summary>Classes related to C# activities.</summary>
    </member>
    <member name="N:Microsoft.CSharp.RuntimeBinder">
      <summary>The <see cref="N:Microsoft.CSharp.RuntimeBinder" /> namespace provides classes and interfaces that support interoperation between Dynamic Language Runtime and C#.</summary>
    </member>
    <member name="N:Microsoft.JScript">
      <summary>The <see cref="N:Microsoft.JScript" /> namespace contains classes that support compilation and code generation using the JScript language.</summary>
    </member>
    <member name="N:Microsoft.JScript.Vsa">
      <summary>The Microsoft.JScript.Vsa namespace contains interfaces that allow you to integrate Script for the.NET Framework script engines into JScript, and to compile and execute code at run time.</summary>
    </member>
    <member name="N:Microsoft.SqlServer.Server">
      <summary>The <see cref="N:Microsoft.SqlServer.Server" /> namespace contains classes, interfaces, and enumerations that are specific to the integration of the Microsoft .NET Framework common language runtime (CLR) into Microsoft SQL Server, and the SQL Server database engine process execution environment.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic">
      <summary>The <see cref="N:Microsoft.VisualBasic" /> namespace contains types that support the Visual Basic Runtime in Visual Basic.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.Activities">
      <summary>Provides classes related to Visual Basic activities.h</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.Activities.XamlIntegration">
      <summary>Provides classes related to Visual Basic XAML integration.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.ApplicationServices">
      <summary>The <see cref="N:Microsoft.VisualBasic.ApplicationServices" /> namespace contains types that support the Visual Basic Application Model and provide access to application information. </summary>
    </member>
    <member name="N:Microsoft.VisualBasic.Compatibility.VB6">
      <summary>The <see cref="N:Microsoft.VisualBasic.Compatibility.VB6" /> namespace contains functions and objects provided use by the tools for upgrading from Visual Basic 6.0 to Visual Basic 2008.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.CompilerServices">
      <summary>The <see cref="N:Microsoft.VisualBasic.CompilerServices" /> namespace contains internal-use only types that support the Visual Basic compiler.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.Devices">
      <summary>The <see cref="N:Microsoft.VisualBasic.Devices" /> namespace contains types that support the My objects related to devices in Visual Basic.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.FileIO">
      <summary>The <see cref="N:Microsoft.VisualBasic.FileIO" /> namespace contains types that support the My file system object in Visual Basic.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.Logging">
      <summary>The <see cref="N:Microsoft.VisualBasic.Logging" /> namespace contains types that support the My logging objects in Visual Basic and provides a simple log listener that directs logging output to file.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.MyServices">
      <summary>The <see cref="N:Microsoft.VisualBasic.MyServices" /> namespace contains types that support My in Visual Basic.</summary>
    </member>
    <member name="N:Microsoft.VisualBasic.MyServices.Internal">
      <summary>The <see cref="N:Microsoft.VisualBasic.MyServices.Internal" /> namespace contains internal-use only types that support My in Visual Basic.</summary>
    </member>
    <member name="N:Microsoft.VisualC">
      <summary>The <see cref="N:Microsoft.VisualC" /> namespace contains classes that support compilation and code generation using the C++ language.</summary>
    </member>
    <member name="N:Microsoft.VisualC.StlClr">
      <summary>Contains the classes, interfaces, and enumerators that are used to implement the STL/CLR Library.</summary>
    </member>
    <member name="N:Microsoft.VisualC.StlClr.Generic">
      <summary>Contains the classes, interfaces, and iterators that are used to implement the generic interface to the STL/CLR Library. By using this generic interface, other .NET languages, such as C# and Visual Basic, can call code that is written using STL/CLR.</summary>
    </member>
    <member name="N:Microsoft.Win32">
      <summary>The <see cref="N:Microsoft.Win32" /> namespace provides two types of classes: those that handle events raised by the operating system and those that manipulate the system registry.</summary>
    </member>
    <member name="N:Microsoft.Win32.SafeHandles">
      <summary>The <see cref="N:Microsoft.Win32.SafeHandles" /> namespace contains classes that are abstract derivations of safe handle classes that provide common functionality supporting file and operating system handles.</summary>
    </member>
    <member name="N:Microsoft.Windows.Input">
      <summary>Contains interfaces for preview commands in a Microsoft Ribbon for WPF control.</summary>
    </member>
    <member name="N:Microsoft.Windows.Themes">
      <summary>Provides exposure to the set of themes defined by Windows Presentation Foundation. In WPF, a theme is a set of resources, defined at the system level, which provide the default appearance for controls and other visual elements of an application. WPF themes are created using styles and include Luna, Aero, Royale, and Classic. Only the client area of a WPF application uses these themes; the appearance of the window border and buttons that form the window chrome is controlled by the Win32 theme service. </summary>
    </member>
    <member name="N:System">
      <summary>The <see cref="N:System" /> namespace contains fundamental classes and base classes that define commonly-used value and reference data types, events and event handlers, interfaces, attributes, and processing exceptions.</summary>
    </member>
    <member name="N:System.Activities">
      <summary>Provides classes related to system activities.</summary>
    </member>
    <member name="N:System.Activities.Core.Presentation">
      <summary>Provides classes related to presentation.</summary>
    </member>
    <member name="N:System.Activities.Core.Presentation.Factories">
      <summary>Provides classes related to presentation factories.</summary>
    </member>
    <member name="N:System.Activities.Core.Presentation.Themes">
      <summary>Provides classes related to presentation themes.</summary>
    </member>
    <member name="N:System.Activities.Debugger">
      <summary>Provides classes related to the system debugger.</summary>
    </member>
    <member name="N:System.Activities.Debugger.Symbol">
      <summary>Provides classes related to debugger symbols.</summary>
    </member>
    <member name="N:System.Activities.DurableInstancing">
      <summary>Provides classes related to durable instancing.</summary>
    </member>
    <member name="N:System.Activities.DynamicUpdate">
      <summary>Provides classes related to dynamic updates.</summary>
    </member>
    <member name="N:System.Activities.ExpressionParser">
      <summary>Provides classes related to expression parsing.</summary>
    </member>
    <member name="N:System.Activities.Expressions">
      <summary>Provides classes related to expressions.</summary>
    </member>
    <member name="N:System.Activities.Hosting">
      <summary>Provides classes related to hosting.</summary>
    </member>
    <member name="N:System.Activities.Persistence">
      <summary>Provides classes related to persistence.</summary>
    </member>
    <member name="N:System.Activities.Presentation">
      <summary>Provides classes related to presentation.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Annotations">
      <summary>Provides classes related to presentation annotations.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Converters">
      <summary>Provides classes related to converters.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Debug">
      <summary>Provides classes related to presentation debugging.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Expressions">
      <summary>Provides classes related to expressions.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Hosting">
      <summary>Provides classes related to hosting.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Metadata">
      <summary>Provides classes related to presentation metadata.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Model">
      <summary>Provides classes related to presentation model.</summary>
    </member>
    <member name="N:System.Activities.Presentation.PropertyEditing">
      <summary>Provides classes related to property editing.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Services">
      <summary>Provides classes related to presentation services.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Sqm">
      <summary>Provides classes related to SQM.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Toolbox">
      <summary>Classes related to the presentation toolbox.</summary>
    </member>
    <member name="N:System.Activities.Presentation.Validation">
      <summary>Provides classes related to validation.</summary>
    </member>
    <member name="N:System.Activities.Presentation.View">
      <summary>Provides classes related to presentation views.</summary>
    </member>
    <member name="N:System.Activities.Presentation.View.OutlineView">
      <summary>Provides classes related to the outline view.</summary>
    </member>
    <member name="N:System.Activities.Presentation.ViewState">
      <summary>Provides classes related to view state data and management of view states.</summary>
    </member>
    <member name="N:System.Activities.Statements">
      <summary>Provides classes related to activity statements.</summary>
    </member>
    <member name="N:System.Activities.Statements.Tracking">
      <summary>Provides classes for monitoring state machine state executions and state transitions.</summary>
    </member>
    <member name="N:System.Activities.Tracking">
      <summary>The System.Activities.Tracking namespace contains classes used to create and interact with workflow tracking records. Tracking records are created by the workflow runtime when a workflow instance executes, and are accessed by classes that inherit from <see cref="T:System.Activities.Tracking.TrackingParticipant" />.</summary>
    </member>
    <member name="N:System.Activities.Validation">
      <summary>Provides classes related to custom activity validation.</summary>
    </member>
    <member name="N:System.Activities.XamlIntegration">
      <summary>Contains classes that provide support for the serialization of activities into XAML.</summary>
    </member>
    <member name="N:System.AddIn">
      <summary>The <see cref="N:System.AddIn" /> namespace contains the <see cref="T:System.AddIn.AddInAttribute" /> attribute for identifying add-ins. </summary>
    </member>
    <member name="N:System.AddIn.Contract">
      <summary>The <see cref="N:System.AddIn.Contract" /> namespace contains interfaces and structures that provide the basis for communication between components that are updated independently.</summary>
    </member>
    <member name="N:System.AddIn.Contract.Automation">
      <summary>The <see cref="N:System.AddIn.Contract.Automation" /> namespace contains interfaces that components use to access type information and invoke type members.</summary>
    </member>
    <member name="N:System.AddIn.Contract.Collections">
      <summary>The <see cref="N:System.AddIn.Contract.Collections" /> namespace contains interfaces that define collections of <see cref="T:System.AddIn.Contract.IContract" /> and <see cref="T:System.AddIn.Contract.RemoteArgument" /> objects.</summary>
    </member>
    <member name="N:System.AddIn.Hosting">
      <summary>The <see cref="N:System.AddIn.Hosting" /> namespace provides classes for discovering, registering, activating, and controlling Add-Ins.</summary>
    </member>
    <member name="N:System.AddIn.Pipeline">
      <summary>The <see cref="N:System.AddIn.Pipeline" /> namespace provides classes for constructing the  communication pipeline between a host application and an add-in.</summary>
    </member>
    <member name="N:System.CodeDom">
      <summary>The <see cref="N:System.CodeDom" /> namespace contains classes that can be used to represent the elements and structure of a source code document. The classes in this namespace can be used to model the structure of a source code document that can be output as source code in a supported language using the functionality provided by the <see cref="N:System.CodeDom.Compiler" /> namespace.</summary>
    </member>
    <member name="N:System.CodeDom.Compiler">
      <summary>The <see cref="N:System.CodeDom.Compiler" /> namespace contains types for managing the generation and compilation of source code in supported programming languages. Code generators can each produce source code in a particular programming language based on the structure of Code Document Object Model (CodeDOM) source code models consisting of elements provided by the <see cref="N:System.CodeDom" /> namespace.</summary>
    </member>
    <member name="N:System.Collections">
      <summary>The <see cref="N:System.Collections" /> namespace contains interfaces and classes that define various collections of objects, such as lists, queues, bit arrays, hash tables and dictionaries.</summary>
    </member>
    <member name="N:System.Collections.Concurrent">
      <summary>The System.Collections.Concurrent namespace provides several thread-safe collection classes that should be used in place of the corresponding types in the <see cref="N:System.Collections" /> and <see cref="N:System.Collections.Generic" /> namespaces whenever multiple threads are accessing the collection concurrently. However, members accessed through one of the interfaces the current collections implement, including extension methods, are not guaranteed to be thread safe and may need to be synchronized by the caller.</summary>
    </member>
    <member name="N:System.Collections.Generic">
      <summary>The <see cref="N:System.Collections.Generic" /> namespace contains interfaces and classes that define generic collections, which allow users to create strongly typed collections that provide better type safety and performance than non-generic strongly typed collections.</summary>
    </member>
    <member name="N:System.Collections.ObjectModel">
      <summary>The <see cref="N:System.Collections.ObjectModel" /> namespace contains classes that can be used as collections in the object model of a reusable library. Use these classes when properties or methods return collections.</summary>
    </member>
    <member name="N:System.Collections.Specialized">
      <summary>The <see cref="N:System.Collections.Specialized" /> namespace contains specialized and strongly-typed collections; for example, a linked list dictionary, a bit vector, and collections that contain only strings.</summary>
    </member>
    <member name="N:System.ComponentModel">
      <summary>The <see cref="N:System.ComponentModel" /> namespace provides classes that are used to implement the run-time and design-time behavior of components and controls. This namespace includes the base classes and interfaces for implementing attributes and type converters, binding to data sources, and licensing components.</summary>
    </member>
    <member name="N:System.ComponentModel.Composition">
      <summary>This namespace provides classes that constitute the core of the Managed Extensibility Framework, or MEF.</summary>
    </member>
    <member name="N:System.ComponentModel.Composition.Hosting">
      <summary>The <see cref="N:System.ComponentModel.Composition.Hosting" /> namespace provides Managed Extensibility Framework (MEF) types that are useful to developers of extensible applications, or hosts.</summary>
    </member>
    <member name="N:System.ComponentModel.Composition.Primitives">
      <summary>This namespace provides the primitive types underlying the MEF programming model.</summary>
    </member>
    <member name="N:System.ComponentModel.Composition.ReflectionModel">
      <summary>The <see cref="N:System.ComponentModel.Composition.ReflectionModel" /> namespace provides Managed Extensibility Framework (MEF) types for developers who use a reflection-based programming model.</summary>
    </member>
    <member name="N:System.ComponentModel.Composition.Registration">
      <summary>The <see cref="N:System.ComponentModel.Composition.Registration" /> namespace contains types that enable rule-based configuration of Managed Extensibility Framework (MEF) parts.</summary>
    </member>
    <member name="N:System.ComponentModel.DataAnnotations">
      <summary>The <see cref="N:System.ComponentModel.DataAnnotations" /> namespace provides attribute classes that are used to define metadata for ASP.NET MVC and ASP.NET data controls. </summary>
    </member>
    <member name="N:System.ComponentModel.DataAnnotations.Schema">
      <summary>The <see cref="N:System.ComponentModel.DataAnnotations.Schema" /> namespace provides support for attribute classes that are used to define metadata for ASP.NET MVC and ASP.NET data controls.</summary>
    </member>
    <member name="N:System.ComponentModel.Design">
      <summary>The <see cref="N:System.ComponentModel.Design" /> namespace contains classes that developers can use to build custom design-time behavior for components and user interfaces for configuring components at design time. The design time environment provides systems that enable developers to arrange components and configure their properties. Some components may require specific design-time only behavior to function properly in a design time environment. It may also be valuable to provide custom user interfaces which assist developers in configuring components or the values of complex data types. The classes and interfaces defined within this namespace can be used to build design-time behavior for components, access design-time services, and implement customized design-time configuration interfaces.</summary>
    </member>
    <member name="N:System.ComponentModel.Design.Data">
      <summary>The <see cref="N:System.ComponentModel.Design.Data" /> namespace contains classes you can use to build custom design-time behavior for data-related components. </summary>
    </member>
    <member name="N:System.ComponentModel.Design.Serialization">
      <summary>The <see cref="N:System.ComponentModel.Design.Serialization" /> namespace provides types that support customization and control of serialization at design time.</summary>
    </member>
    <member name="N:System.Configuration">
      <summary>The <see cref="N:System.Configuration" /> namespace contains the types that provide the programming model for handling configuration data.</summary>
    </member>
    <member name="N:System.Configuration.Assemblies">
      <summary>The <see cref="N:System.Configuration.Assemblies" /> namespace contains classes that are used to configure an assembly.</summary>
    </member>
    <member name="N:System.Configuration.Install">
      <summary>The <see cref="N:System.Configuration.Install" /> namespace provides classes that allow you to write custom installers for your own components. The <see cref="T:System.Configuration.Install.Installer" /> class is the base class for all custom installers in the.NET Framework.</summary>
    </member>
    <member name="N:System.Configuration.Internal">
      <summary>The <see cref="N:System.Configuration.Internal" /> namespace contains configuration types that are intended for internal use only.</summary>
    </member>
    <member name="N:System.Configuration.Provider">
      <summary>The <see cref="N:System.Configuration.Provider" /> namespace contains the base classes shared by both server and client applications to support a pluggable model to easily add or remove functionality.</summary>
    </member>
    <member name="N:System.Data">
      <summary>The <see cref="N:System.Data" /> namespace provides access to classes that represent the ADO.NET architecture. ADO.NET lets you build components that efficiently manage data from multiple data sources.</summary>
    </member>
    <member name="N:System.Data.Common">
      <summary>The <see cref="N:System.Data.Common" /> namespace contains classes shared by the .NET Framework data providers.</summary>
    </member>
    <member name="N:System.Data.Common.CommandTrees">
      <summary>The <see cref="N:System.Data.Common.CommandTrees" /> namespace provides classes to build expressions that make up a command tree.</summary>
    </member>
    <member name="N:System.Data.Common.CommandTrees.ExpressionBuilder">
      <summary>The <see cref="N:System.Data.Common.CommandTrees.ExpressionBuilder" /> namespace provides a command tree API.</summary>
    </member>
    <member name="N:System.Data.Common.CommandTrees.ExpressionBuilder.Spatial">
      <summary>The <see cref="N:System.Data.Common.CommandTrees.ExpressionBuilder.Spatial" /> namespace contains a class that represent the Entity Data Model (EDM) functions of a spatial.</summary>
    </member>
    <member name="N:System.Data.Common.EntitySql">
      <summary>The <see cref="N:System.Data.Common.EntitySql" /> namespace includes the <see cref="T:System.Data.Common.EntitySql.EntitySqlParser" /> class and other support classes. These classes enable you to parse an Entity SQL query string and create a command tree query. </summary>
    </member>
    <member name="N:System.Data.Design">
      <summary>The <see cref="N:System.Data.Design" /> namespace contains classes that can be used to generate a custom typed-dataset.</summary>
    </member>
    <member name="N:System.Data.Entity.Design">
      <summary>The <see cref="N:System.Data.Entity.Design" /> namespace contains classes to generate Entity Data Model (EDM) files and object source code.</summary>
    </member>
    <member name="N:System.Data.Entity.Design.AspNet">
      <summary>The <see cref="N:System.Data.Entity.Design.AspNet" /> namespace contains the Entity Data Model (EDM) build providers for the ASP.NET build environment.</summary>
    </member>
    <member name="N:System.Data.Entity.Design.PluralizationServices">
      <summary>The <see cref="T:System.Data.Entity.Design.PluralizationServices" /> namespace provides classes for changing words from singular to plural form, and vice versa.</summary>
    </member>
    <member name="N:System.Data.EntityClient">
      <summary>The <see cref="N:System.Data.EntityClient" /> namespace is the .NET Framework Data Provider for the Entity Framework.</summary>
    </member>
    <member name="N:System.Data.Linq">
      <summary>The <see cref="N:System.Data.Linq" /> namespace contains classes that support interaction with relational databases in LINQ to SQL applications. </summary>
    </member>
    <member name="N:System.Data.Linq.Mapping">
      <summary>The <see cref="N:System.Data.Linq.Mapping" /> namespace contains classes that are used to generate a LINQ to SQL object model that represents the structure and content of a relational database.</summary>
    </member>
    <member name="N:System.Data.Linq.SqlClient">
      <summary>The <see cref="N:System.Data.Linq.SqlClient" /> namespace contains provider classes for communicating with SQL Server and classes that contain query helper methods.</summary>
    </member>
    <member name="N:System.Data.Linq.SqlClient.Implementation">
      <summary>The <see cref="N:System.Data.Linq.SqlClient.Implementation" /> namespace contains types that are used for the internal implementation details of a SQL Server provider.</summary>
    </member>
    <member name="N:System.Data.Mapping">
      <summary>The <see cref="N:System.Data.Mapping" /> namespace provides access to the <see cref="T:System.Data.Mapping.MappingItemCollection" /> and <see cref="T:System.Data.Mapping.StorageMappingItemCollection" /> classes. </summary>
    </member>
    <member name="N:System.Data.Metadata.Edm">
      <summary>The <see cref="N:System.Data.Metadata.Edm" /> namespace contains a set of types that represent concepts throughout the models used by the Entity Framework and a set of classes that help applications to work with metadata. </summary>
    </member>
    <member name="N:System.Data.Objects">
      <summary>The <see cref="N:System.Data.Objects" /> namespace includes classes that provide access to the core functionality of Object Services. These classes enable you to query, insert, update, and delete data by working with strongly typed CLR objects that are instances of entity types. Object Services supports both Language-Integrated Query (LINQ) and Entity SQL queries against types that are defined in an Entity Data Model (EDM). Object Services materializes the returned data as objects and propagates object changes back to the data source. It also provides facilities for tracking changes, binding objects to controls, and handling concurrency. For more information, see Object Services Overview (Entity Framework).</summary>
    </member>
    <member name="N:System.Data.Objects.DataClasses">
      <summary>The <see cref="N:System.Data.Objects.DataClasses" /> namespace includes classes that are base classes for types that are defined in an Entity Data Model (EDM), base classes for the types that are returned by navigation properties, and classes that define attributes that map common language runtime (CLR) objects to types in the conceptual model. </summary>
    </member>
    <member name="N:System.Data.Objects.SqlClient">
      <summary>The <see cref="N:System.Data.Objects.SqlClient" /> namespace provides the <see cref="T:System.Data.Objects.SqlClient.SqlFunctions" /> class, which contains common language runtime (CLR) methods that translate to database functions. Methods in the <see cref="T:System.Data.Objects.SqlClient.SqlFunctions" /> class can only be used in LINQ to Entities queries. </summary>
    </member>
    <member name="N:System.Data.Odbc">
      <summary>The <see cref="N:System.Data.Odbc" /> namespace is the .NET Framework Data Provider for ODBC.</summary>
    </member>
    <member name="N:System.Data.OleDb">
      <summary>The <see cref="N:System.Data.OleDb" /> namespace is the.NET Framework Data Provider for OLE DB.</summary>
    </member>
    <member name="N:System.Data.OracleClient">
      <summary>The <see cref="N:System.Data.OracleClient" /> namespace is the .NET Framework Data Provider for Oracle.</summary>
    </member>
    <member name="N:System.Data.Services">
      <summary>Provides access to classes used to build WCF Data Services.</summary>
    </member>
    <member name="N:System.Data.Services.BuildProvider">
      <summary>Classes in this namespace generate C# or Visual Basic code for a WCF Data Services client application based on the metadata returned by the data service.</summary>
    </member>
    <member name="N:System.Data.Services.Client">
      <summary>Represents the .NET Framework client library that applications can use to interact with WCF Data Services.</summary>
    </member>
    <member name="N:System.Data.Services.Common">
      <summary>Implements functionality common to both WCF Data Services client and server runtimes.</summary>
    </member>
    <member name="N:System.Data.Services.Configuration">
      <summary>This namespace provides configuration settings for WCF data services features.</summary>
    </member>
    <member name="N:System.Data.Services.Design">
      <summary>Used by the code generation command line tools and tools in Visual Studio to generate strongly-typed client side objects for communicating with data services.</summary>
    </member>
    <member name="N:System.Data.Services.Internal">
      <summary>This class is not for public use and is used internally by the system to implement support for queries with eager loading of related entities.</summary>
    </member>
    <member name="N:System.Data.Services.Providers">
      <summary>Provides a series of interfaces that are implemented to define a custom data service provider for WCF Data Services. </summary>
    </member>
    <member name="N:System.Data.Spatial">
      <summary>The <see cref="N:System.Data.Spatial" /> namespace that contains classes of spatial database functionality.</summary>
    </member>
    <member name="N:System.Data.Sql">
      <summary>The <see cref="N:System.Data.Sql" /> namespace contains classes that support SQL Server-specific functionality.</summary>
    </member>
    <member name="N:System.Data.SqlClient">
      <summary>The <see cref="N:System.Data.SqlClient" /> namespace is the.NET Framework Data Provider for SQL Server.</summary>
    </member>
    <member name="N:System.Data.SqlTypes">
      <summary>The <see cref="N:System.Data.SqlTypes" /> namespace provides classes for native data types in SQL Server. These classes provide a safer, faster alternative to the data types provided by the .NET Framework common language runtime (CLR). Using the classes in this namespace helps prevent type conversion errors caused by loss of precision. Because other data types are converted to and from SqlTypes behind the scenes, explicitly creating and using objects within this namespace also yields faster code.</summary>
    </member>
    <member name="N:System.Deployment.Application">
      <summary>With the <see cref="N:System.Deployment.Application" /> namespace, you can program custom upgrade behavior into your ClickOnce application.</summary>
    </member>
    <member name="N:System.Deployment.Internal">
      <summary>The <see cref="N:System.Deployment.Internal" /> namespace contains helper classes for accessing the internal members of <see cref="T:System.ActivationContext" /> and <see cref="T:System.ApplicationIdentity" /> objects.</summary>
    </member>
    <member name="N:System.Deployment.Internal.CodeSigning">
      <summary>The <see cref="N:System.Deployment.Internal.CodeSigning" /> namespace contains classes for processing digital signatures. </summary>
    </member>
    <member name="N:System.Device.Location">
      <summary>The <see cref="N:System.Device.Location" /> namespace allows application developers to easily access the computer's location by using a single API.  Location information may come from multiple providers, such as GPS, Wi-Fi triangulation, and cell phone tower triangulation. The <see cref="N:System.Device.Location" /> classes provide a single API to encapsulate the multiple location providers on a computer and support seamless prioritization and transitioning between them. As a result, application developers who use this API do not need to tailor applications to specific hardware configurations. </summary>
    </member>
    <member name="N:System.Diagnostics">
      <summary>The <see cref="N:System.Diagnostics" /> namespace provides classes that allow you to interact with system processes, event logs, and performance counters.</summary>
    </member>
    <member name="N:System.Diagnostics.CodeAnalysis">
      <summary>The <see cref="N:System.Diagnostics.CodeAnalysis" /> namespace contains classes for interaction with code analysis tools. These tools are used to analyze code for conformance to coding conventions such as naming or security rules.</summary>
    </member>
    <member name="N:System.Diagnostics.Contracts">
      <summary>The <see cref="N:System.Diagnostics.Contracts" /> namespace contains static classes for representing program contracts such as preconditions, postconditions, and invariants.</summary>
    </member>
    <member name="N:System.Diagnostics.Contracts.Internal">
      <summary>The <see cref="N:System.Diagnostics.Contracts.Internal" /> namespace provides classes that the binary rewriter can use to handle a contract failure.</summary>
    </member>
    <member name="N:System.Diagnostics.Design">
      <summary>The <see cref="N:System.Diagnostics.Design" /> namespace contains classes that can be used to extend design-time support for application monitoring and instrumentation.</summary>
    </member>
    <member name="N:System.Diagnostics.Eventing">
      <summary>Contains the classes used to instrument your application. The instrumentation, when enabled, logs the event data to the Event Tracing for Windows (ETW) tracing subsystem. For a complete description of ETW, see Event Tracing for Windows. </summary>
    </member>
    <member name="N:System.Diagnostics.Eventing.Reader">
      <summary>Using the <see cref="N:System.Diagnostics.Eventing.Reader" /> namespace, you can develop applications that read and manage event logs. An event in an event log contains information, a warning, or an error that has been published by a specific application, service, or operating system component.  These events are read by applications that monitor a computer's health and applications that take action when specific events occur.  For more information, see Technology Summary for Reading and Managing Event Logs and Event Log Scenarios.</summary>
    </member>
    <member name="N:System.Diagnostics.PerformanceData">
      <summary>Use the classes in this namespace to provide counter data. The counters are used to expose performance metrics to consumers such as the Performance Monitor. The namespace does not contain classes for consuming the counter data. For a complete description of the performance counters architecture, see Performance Counters.</summary>
    </member>
    <member name="N:System.Diagnostics.SymbolStore">
      <summary>The <see cref="N:System.Diagnostics.SymbolStore" /> namespace provides classes that allow you to read and write debug symbol information, such as source line to Microsoft intermediate language (MSIL) maps. Compilers targeting the.NET Framework can store the debug symbol information into programmer's database (PDB) files. Debuggers and code profiler tools can read the debug symbol information at run time.</summary>
    </member>
    <member name="N:System.Diagnostics.Tracing">
      <summary>The <see cref="N:System.Diagnostics.Tracing" /> namespace provides types and members that enable you to create strongly typed events to be captured by event tracing for Windows (ETW).</summary>
    </member>
    <member name="N:System.DirectoryServices">
      <summary>The <see cref="N:System.DirectoryServices" /> namespace provides easy access to Active Directory Domain Services from managed code. The namespace contains two component classes, <see cref="T:System.DirectoryServices.DirectoryEntry" /> and <see cref="T:System.DirectoryServices.DirectorySearcher" />, which use the Active Directory Services Interfaces (ADSI) technology. ADSI is the set of interfaces that Microsoft provides as a flexible tool for working with a variety of network providers. ADSI gives the administrator the ability to locate and manage resources on a network with relative ease, regardless of the size of the network.</summary>
    </member>
    <member name="N:System.DirectoryServices.AccountManagement">
      <summary>The <see cref="N:System.DirectoryServices.AccountManagement" /> namespace provides uniform access and manipulation of user, computer, and group security principals across the multiple principal stores: Active Directory Domain Services (AD DS), Active Directory Lightweight Directory Services (AD LDS), and Machine SAM (MSAM). <see cref="N:System.DirectoryServices.AccountManagement" /> manages directory objects independent of the <see cref="N:System.DirectoryServices" /> namespace. </summary>
    </member>
    <member name="N:System.DirectoryServices.ActiveDirectory">
      <summary>The <see cref="N:System.DirectoryServices.ActiveDirectory" /> namespace provides a high level abstraction object model that builds around Microsoft Active Directory services tasks. The Active Directory service concepts such as forest, domain, site, subnet, partition, and schema are part of the object model.</summary>
    </member>
    <member name="N:System.DirectoryServices.Protocols">
      <summary>The <see cref="N:System.DirectoryServices.Protocols" /> namespace provides the methods defined in the Lightweight Directory Access Protocol (LDAP) version 3 (V3) and Directory Services Markup Language (DSML) version 2.0 (V2) standards.</summary>
    </member>
    <member name="N:System.Drawing">
      <summary>The <see cref="N:System.Drawing" /> namespace provides access to GDI+ basic graphics functionality. More advanced functionality is provided in the <see cref="N:System.Drawing.Drawing2D" />, <see cref="N:System.Drawing.Imaging" />, and <see cref="N:System.Drawing.Text" /> namespaces.</summary>
    </member>
    <member name="N:System.Drawing.Configuration">
      <summary>The <see cref="N:System.Drawing.Configuration" /> namespace contains a class that supports configuration for classes in the <see cref="N:System.Drawing" /> namespace.</summary>
    </member>
    <member name="N:System.Drawing.Design">
      <summary>The <see cref="N:System.Drawing.Design" /> namespace contains classes that extend design-time user interface (UI) logic and drawing. </summary>
    </member>
    <member name="N:System.Drawing.Drawing2D">
      <summary>The <see cref="N:System.Drawing.Drawing2D" /> namespace provides advanced two-dimensional and vector graphics functionality.</summary>
    </member>
    <member name="N:System.Drawing.Imaging">
      <summary>The <see cref="N:System.Drawing.Imaging" /> namespace provides advanced GDI+ imaging functionality. Basic graphics functionality is provided by the <see cref="N:System.Drawing" /> namespace.</summary>
    </member>
    <member name="N:System.Drawing.Printing">
      <summary>The <see cref="N:System.Drawing.Printing" /> namespace provides print-related services for Windows Forms applications.</summary>
    </member>
    <member name="N:System.Drawing.Text">
      <summary>The <see cref="N:System.Drawing.Text" /> namespace provides advanced GDI+ typography functionality.</summary>
    </member>
    <member name="N:System.Dynamic">
      <summary>The <see cref="N:System.Dynamic" /> namespace provides classes and interfaces that support Dynamic Language Runtime.</summary>
    </member>
    <member name="N:System.EnterpriseServices">
      <summary>The <see cref="N:System.EnterpriseServices" /> namespace provides an important infrastructure for enterprise applications. COM+ provides a services architecture for component programming models deployed in an enterprise environment. This namespace provides .NET objects with access to COM+ services making the .NET Framework objects more practical for enterprise applications.</summary>
    </member>
    <member name="N:System.EnterpriseServices.CompensatingResourceManager">
      <summary>The <see cref="N:System.EnterpriseServices.CompensatingResourceManager" /> namespace provides classes that allow you to use a Compensating Resource Manager (CRM) in managed code. A CRM is a service provided by COM+ that enables you to include non transactional objects in Microsoft Distributed Transaction Coordinator (DTC) transactions. Although CRMs do not provide the capabilities of a full resource manager, they do provide transactional atomicity (all or nothing behavior) and durability through the recovery log.</summary>
    </member>
    <member name="N:System.EnterpriseServices.Internal">
      <summary>The <see cref="N:System.EnterpriseServices.Internal" /> namespace provides infrastructure support for COM+ services. The classes and interfaces in this namespace are specifically intended to support calls into <see cref="N:System.EnterpriseServices" /> from the unmanaged COM+ classes.</summary>
    </member>
    <member name="N:System.Globalization">
      <summary>The <see cref="N:System.Globalization" /> namespace contains classes that define culture-related information, including language, country/region, calendars in use, format patterns for dates, currency, and numbers, and sort order for strings. These classes are useful for writing globalized (internationalized) applications. Classes such as <see cref="T:System.Globalization.StringInfo" /> and <see cref="T:System.Globalization.TextInfo" /> provide advanced globalization functionalities, including surrogate support and text element processing.</summary>
    </member>
    <member name="N:System.IdentityModel">
      <summary>The <see cref="N:System.IdentityModel" /> namespace contains classes that are used to build security token services (STS). These include the <see cref="T:System.IdentityModel.SecurityTokenService" /> and <see cref="T:System.IdentityModel.Scope" /> classes, as well as exception several utility classes that provide the ability to perform cookie transforms.</summary>
    </member>
    <member name="N:System.IdentityModel.Claims">
      <summary>The <see cref="N:System.IdentityModel.Claims" /> namespace contains classes that implement the Windows Communication Foundation (WCF) claims-based identity authorization model. This model includes the <see cref="T:System.IdentityModel.Claims.Claim" /> class and the <see cref="T:System.IdentityModel.Claims.ClaimSet" /> class.Beginning with .NET Framework 4.5 and the integration of Windows Identity Foundation (WIF) into the .NET Framework, the WCF claims-based identity model has been superseded by WIF. WIF provides a claims-based identity object model that can be used to provide authentication and authorization across several Microsoft product stacks, including the CLR, WCF, and ASP.NET. The WIF classes that represent claims, claim types, and identities and principals that are based on claims are contained in the <see cref="N:System.Security.Claims" /> namespace. Beginning with .NET 4.5, these classes should be used instead of those in the <see cref="N:System.IdentityModel.Claims" /> namespace.</summary>
    </member>
    <member name="N:System.IdentityModel.Configuration">
      <summary>The <see cref="N:System.IdentityModel.Configuration" /> namespace contains classes that provide configuration for applications and services built using the Windows Identity Foundation (WIF). The classes in this namespace represent settings under the &lt;identityConfiguration&gt; element. Classes that configure federation using the WS-Federation protocol are contained primarily in the <see cref="N:System.IdentityModel.Services.Configuration" /> namespace. These settings appear under the &lt;system.identityModel.services&gt; element in a configuration file.</summary>
    </member>
    <member name="N:System.IdentityModel.Metadata">
      <summary>The <see cref="N:System.IdentityModel.Metadata" /> namespace contains classes that represent elements in a Federation Metadata document. A Federation Metadata document is an XML document that provides information that may be useful to partners when establishing a federation. For more information about federation metadata, see section three of the following specification: Web Services Federation Language (WS-Federation) Version 1.2 (http://go.microsoft.com/fwlink/?LinkID=210152).</summary>
    </member>
    <member name="N:System.IdentityModel.Policy">
      <summary>The <see cref="N:System.IdentityModel.Policy" /> namespace contains classes that are used to implement authorization policy when using the WCF claims-based identity model.Beginning with .NET Framework 4.5 and the integration of Windows Identity Foundation (WIF) into the .NET Framework, the WCF claims-based identity model has been superseded by WIF. WIF provides a claims-based identity object model that can be used to provide authentication and authorization across several Microsoft product stacks, including the CLR, WCF, and ASP.NET. In WIF, authorization policy is implemented by extending the <see cref="T:System.Security.Claims.ClaimsAuthorizationManager" /> class. Beginning with .NET 4.5, authorization policy should be implemented by using WIF instead of the classes in the <see cref="N:System.IdentityModel.Claims" /> namespace.</summary>
    </member>
    <member name="N:System.IdentityModel.Protocols.WSTrust">
      <summary>The <see cref="N:System.IdentityModel.Protocols.WSTrust" /> namespace contains classes that represent WS-Trust artifacts. These include the <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> (RST) and <see cref="T:System.IdentityModel.Protocols.WSTrust.RequestSecurityToken" /> (RSTR) classes and several serializers that can serialize and deserialize between WS-Trust artifacts on the wire and their representation in the object model. Out-of-the-box, Windows Identity Foundation (WIF) contains support for the following WS-Trust specifications: WS-Trust February 2005 (http://go.microsoft.com/fwlink/?LinkID=210149), WS-Trust 1.3 (http://go.microsoft.com/fwlink/?LinkID=210148), or WS-Trust 1.4 (http://go.microsoft.com/fwlink/?LinkID=210229).</summary>
    </member>
    <member name="N:System.IdentityModel.Selectors">
      <summary>The <see cref="N:System.IdentityModel.Selectors" /> namespace contains classes that implement authentication in the Windows Communication Foundation (WCF) claims-based identity model.Beginning with .NET Framework 4.5 and the integration of Windows Identity Foundation (WIF) into the .NET Framework, the WCF claims-based identity model has been superseded by WIF. WIF provides a claims-based identity object model that can be used to provide authentication and authorization across several Microsoft product stacks, including the CLR, WCF, and ASP.NET. The WIF classes that represent security tokens and that are used to process security tokens are contained in the <see cref="N:System.IdentityModel.Tokens" /> namespace; for example, <see cref="T:System.IdentityModel.Tokens.SecurityToken" /> and <see cref="T:System.IdentityModel.Tokens.SecurityTokenHandler" />. Beginning with .NET 4.5, the classes in the <see cref="N:System.IdentityModel.Tokens" /> namespace should be used instead of those in the <see cref="N:System.IdentityModel.Selectors" /> namespace.</summary>
    </member>
    <member name="N:System.IdentityModel.Services">
      <summary>The <see cref="N:System.IdentityModel.Services" /> namespace primarily contains classes that are used in applications built using Windows Identity Foundation (WIF) that perform federated authentication with the WS-Federation protocol. It contains the HTTP Modules, <see cref="T:System.IdentityModel.Services.WSFederationAuthenticationModule" /> (WSFAM) and <see cref="T:System.IdentityModel.Services.SessionAuthenticationModule" /> (SAM), as well as classes that support their operation. The WSFAM provides handling for the WS-Federation sign-in and sign-out sequences and the SAM provides session management by maintaining and handling cookies. The namespace also contains classes that represent WS-Federation messages (<see cref="T:System.IdentityModel.Services.WSFederationMessage" />), such as sign-in (<see cref="T:System.IdentityModel.Services.SignInRequestMessage" />, <see cref="T:System.IdentityModel.Services.SignInResponseMessage" />) and sign-out messages (<see cref="T:System.IdentityModel.Services.SignOutRequestMessage" />), as well as serializers that serialize and deserialize between the object model and the on-the-wire representations of these messages.</summary>
    </member>
    <member name="N:System.IdentityModel.Services.Configuration">
      <summary>The <see cref="N:System.IdentityModel.Services.Configuration" /> namespace contains classes that provide configuration for Windows Identity Foundation (WIF) applications that use the WS-Federation protocol. The classes in this namespace represent settings under the &lt;system.identityModel.services&gt; element. The <see cref="N:System.IdentityModel.Services" /> namespace also contains some of the classes that are used to configure WS-Federation. Classes that configure other service and application settings, such as security token handlers, are contained primarily in the <see cref="N:System.IdentityModel.Configuration" /> namespace. These settings appear under the &lt;identityConfiguration&gt; element in a configuration file.</summary>
    </member>
    <member name="N:System.IdentityModel.Services.Tokens">
      <summary>The <see cref="N:System.IdentityModel.Services.Tokens" /> contains the <see cref="T:System.IdentityModel.Services.Tokens.MachineKeySessionSecurityTokenHandler" /> class and the <see cref="T:System.IdentityModel.Services.Tokens.MembershipUserNameSecurityTokenHandler" /> class.</summary>
    </member>
    <member name="N:System.IdentityModel.Tokens">
      <summary>The <see cref="N:System.IdentityModel.Tokens" /> namespace contains classes that represent security tokens, security token handlers, key identifier clauses and other artifacts used in token generation and processing. The namespace contains base classes such as <see cref="T:System.IdentityModel.Tokens.SecurityToken" />, <see cref="T:System.IdentityModel.Tokens.SecurityTokenHandler" />, and <see cref="T:System.IdentityModel.Tokens.SecurityKeyIdentifierClause" />, as well as classes that derive from these classes and represent several of the token types, artifacts, and handlers for which the Windows Identity Foundation (WIF) has built in support. This includes classes that contain support for SAML v1.1 and v2.0 tokens, such as: <see cref="T:System.IdentityModel.Tokens.SamlSecurityToken" />, <see cref="T:System.IdentityModel.Tokens.SamlSecurityTokenHandler" />, <see cref="T:System.IdentityModel.Tokens.Saml2SecurityToken" />, and <see cref="T:System.IdentityModel.Tokens.Saml2SecurityTokenHandler" />.</summary>
    </member>
    <member name="N:System.IO">
      <summary>The System.IO namespace contains types that allow reading and writing to files and data streams, and types that provide basic file and directory support.</summary>
    </member>
    <member name="N:System.IO.Compression">
      <summary>The <see cref="N:System.IO.Compression" /> namespace contains classes that provide basic compression and decompression services for streams. </summary>
    </member>
    <member name="N:System.IO.IsolatedStorage">
      <summary>The <see cref="N:System.IO.IsolatedStorage" /> namespace contains types that allow the creation and use of isolated stores. With these stores, you can read and write data that less trusted code cannot access and prevent the exposure of sensitive information that can be saved elsewhere on the file system. Data is stored in compartments that are isolated by the current user and by the assembly in which the code exists. Additionally, data can be isolated by domain. Roaming profiles can be used in conjunction with isolated storage so isolated stores will travel with the user's profile. The <see cref="T:System.IO.IsolatedStorage.IsolatedStorageScope" /> enumeration indicates different types of isolation. For more information about when to use isolated storage, see Isolated Storage.</summary>
    </member>
    <member name="N:System.IO.Log">
      <summary>The System.IO.Log namespace defines an interface for logging to a record-oriented sequential I/O system.</summary>
    </member>
    <member name="N:System.IO.MemoryMappedFiles">
      <summary>The <see cref="N:System.IO.MemoryMappedFiles" /> namespace provides classes for using a memory-mapped file, which maps the contents of a file to an application’s logical address space.  </summary>
    </member>
    <member name="N:System.IO.Packaging">
      <summary>Provides classes that support storage of multiple data objects in a single container.</summary>
    </member>
    <member name="N:System.IO.Pipes">
      <summary>The <see cref="N:System.IO.Pipes" /> namespace contains types that provide a means for interprocess communication through anonymous and/or named pipes.</summary>
    </member>
    <member name="N:System.IO.Ports">
      <summary>The <see cref="N:System.IO.Ports" /> namespace contains classes for controlling serial ports. The most important class, <see cref="T:System.IO.Ports.SerialPort" />, provides a framework for synchronous and event-driven I/O, access to pin and break states, and access to serial driver properties. It can be used to wrap <see cref="T:System.IO.Stream" /> objects, allowing the serial port to be accessed by classes that use streams.  </summary>
    </member>
    <member name="N:System.Linq">
      <summary>The <see cref="N:System.Linq" /> namespace provides classes and interfaces that support queries that use Language-Integrated Query (LINQ).</summary>
    </member>
    <member name="N:System.Linq.Expressions">
      <summary>The <see cref="N:System.Linq.Expressions" /> namespace contains classes, interfaces and enumerations that enable language-level code expressions to be represented as objects in the form of expression trees.</summary>
    </member>
    <member name="N:System.Management">
      <summary>Provides access to a rich set of management information and management events about the system, devices, and applications instrumented to the Windows Management Instrumentation (WMI) infrastructure. Applications and services can query for interesting management information (such as how much free space is left on the disk, what is the current CPU utilization, which database a certain application is connected to, and much more), using classes derived from <see cref="T:System.Management.ManagementObjectSearcher" /> and <see cref="T:System.Management.ManagementQuery" />, or subscribe to a variety of management events using the <see cref="T:System.Management.ManagementEventWatcher" /> class. The accessible data can be from both managed and unmanaged components in the distributed environment.</summary>
    </member>
    <member name="N:System.Management.Instrumentation">
      <summary>Provides the classes necessary for instrumenting applications for management and exposing their management information and events through WMI to potential consumers. Consumers such as Microsoft Application Center or Microsoft Operations Manager can then manage your application easily, and monitoring and configuring of your application is available for administrator scripts or other applications, both managed as well as unmanaged. Instrumentation of your application is easy to achieve using the InstrumentationClass custom attribute on classes you wish to expose, or using the provided <see cref="T:System.Management.Instrumentation.BaseEvent" /> and <see cref="T:System.Management.Instrumentation.Instance" /> base classes and the <see cref="T:System.Management.Instrumentation.Instrumentation" /> helper class.</summary>
    </member>
    <member name="N:System.Media">
      <summary>The <see cref="N:System.Media" /> namespace contains classes for playing sound files and accessing sounds provided by the system.</summary>
    </member>
    <member name="N:System.Messaging">
      <summary>The <see cref="N:System.Messaging" /> namespace provides classes that allow you to connect to, monitor, and administer message queues on the network and send, receive, or peek messages.</summary>
    </member>
    <member name="N:System.Messaging.Design">
      <summary>The <see cref="N:System.Messaging.Design" /> namespace contains classes that can be used to extend design-time support for <see cref="N:System.Messaging" /> classes.</summary>
    </member>
    <member name="N:System.Net">
      <summary>The <see cref="N:System.Net" /> namespace provides a simple programming interface for many of the protocols used on networks today. The <see cref="T:System.Net.WebRequest" /> and <see cref="T:System.Net.WebResponse" /> classes form the basis of what are called pluggable protocols, an implementation of network services that enables you to develop applications that use Internet resources without worrying about the specific details of the individual protocols.Classes in the <see cref="N:System.Net" /> namespace can be used to develop Windows Store apps or desktop apps. When used in a Windows Store app, classes in the <see cref="N:System.Net" /> namespace are affected by network isolation feature, part of the application security model used by the Windows Developer Preview. The appropriate network capabilities must be enabled in the app manifest for a Windows Store app for the system to allow network access by a Windows Store app. For more information, see the Network Isolation for Windows Store Apps. </summary>
    </member>
    <member name="N:System.Net.Cache">
      <summary>The <see cref="N:System.Net.Cache" /> namespace defines the types and enumerations used to define cache policies for resources obtained using the <see cref="T:System.Net.WebRequest" /> and <see cref="T:System.Net.HttpWebRequest" /> classes.</summary>
    </member>
    <member name="N:System.Net.Configuration">
      <summary>The <see cref="N:System.Net.Configuration" /> namespace contains classes that applications use to programmatically access and update configuration settings for the <see cref="N:System.Net" /> namespaces.</summary>
    </member>
    <member name="N:System.Net.Http">
      <summary>The <see cref="N:System.Net.Http" /> namespace provides a programming interface for modern HTTP applications.</summary>
    </member>
    <member name="N:System.Net.Http.Headers">
      <summary>Provides support for collections of HTTP headers used by the <see cref="N:System.Net.Http" /> namespace</summary>
    </member>
    <member name="N:System.Net.Mail">
      <summary>The <see cref="N:System.Net.Mail" /> namespace contains classes used to send electronic mail to a Simple Mail Transfer Protocol (SMTP) server for delivery. </summary>
    </member>
    <member name="N:System.Net.Mime">
      <summary>The <see cref="N:System.Net.Mime" /> namespace holds types that are used to represent Multipurpose Internet Mail Exchange (MIME) headers. These types are used with the types in the <see cref="N:System.Net.Mail" /> namespace to specify Content-Type, Content-Disposition and Content-transfer-Encoding headers when using the <see cref="T:System.Net.Mail.SmtpClient" /> class to send e-mail. </summary>
    </member>
    <member name="N:System.Net.NetworkInformation">
      <summary>The <see cref="N:System.Net.NetworkInformation" /> namespace provides access to network traffic data, network address information, and notification of address changes for the local computer. The namespace also contains classes that implement the Ping utility. You can use <see cref="T:System.Net.NetworkInformation.Ping" /> and related classes to check whether a computer is reachable across the network.</summary>
    </member>
    <member name="N:System.Net.PeerToPeer">
      <summary>The <see cref="N:System.Net.PeerToPeer" /> namespace provides access to peer networking functionality.</summary>
    </member>
    <member name="N:System.Net.PeerToPeer.Collaboration">
      <summary>The <see cref="N:System.Net.PeerToPeer.Collaboration" /> namespace enhances <see cref="N:System.Net.PeerToPeer" /> networking functionality and provides capabilities for serverless managed collaboration sessions.</summary>
    </member>
    <member name="N:System.Net.Security">
      <summary>The <see cref="N:System.Net.Security" /> namespace provides network streams for secure communications between hosts.</summary>
    </member>
    <member name="N:System.Net.Sockets">
      <summary>The <see cref="N:System.Net.Sockets" /> namespace provides a managed implementation of the Windows Sockets (Winsock) interface for developers who need to tightly control access to the network.</summary>
    </member>
    <member name="N:System.Net.WebSockets">
      <summary>The <see cref="N:System.Net.WebSockets" /> namespace provides a managed implementation of the WebSocket interface for developers.</summary>
    </member>
    <member name="N:System.Numerics">
      <summary>The <see cref="N:System.Numerics" /> namespace contains numeric types that complement the numeric primitives, such as <see cref="T:System.Byte" />, <see cref="T:System.Double" />, and <see cref="T:System.Int32" />, that are defined by the .NET Framework.</summary>
    </member>
    <member name="N:System.Printing">
      <summary>Provides classes that enable you to automate the management of print servers, print queues, and print jobs. </summary>
    </member>
    <member name="N:System.Printing.IndexedProperties">
      <summary>Provides classes that enable rapidly copying the property settings of a print system object to another object of the same type. Also, enables iteration through the properties of print system objects and the discovery of their types at run time, without using reflection. </summary>
    </member>
    <member name="N:System.Printing.Interop">
      <summary>Provides interconversion of managed <see cref="T:System.Printing.PrintTicket" /> objects and unmanaged Graphics Device Interface (GDI) DEVMODE structures.</summary>
    </member>
    <member name="N:System.Reflection">
      <summary>The <see cref="N:System.Reflection" /> namespace contains types that retrieve information about assemblies, modules, members, parameters, and other entities in managed code by examining their metadata. These types also can be used to manipulate instances of loaded types, for example to hook up events or to invoke methods. To dynamically create types, use the <see cref="N:System.Reflection.Emit" /> namespace.</summary>
    </member>
    <member name="N:System.Reflection.Context">
      <summary>The <see cref="N:System.Reflection.Context" /> namespace contains classes that enable customized reflection contexts.</summary>
    </member>
    <member name="N:System.Reflection.Emit">
      <summary>The <see cref="N:System.Reflection.Emit" /> namespace contains classes that allow a compiler or tool to emit metadata and Microsoft intermediate language (MSIL) and optionally generate a PE file on disk. The primary clients of these classes are script engines and compilers.</summary>
    </member>
    <member name="N:System.Resources">
      <summary>The <see cref="N:System.Resources" /> namespace provides classes and interfaces that allow developers to create, store, and manage various culture-specific resources used in an application. One of the most important classes of the <see cref="N:System.Resources" /> namespace is the <see cref="T:System.Resources.ResourceManager" /> class.</summary>
    </member>
    <member name="N:System.Resources.Tools">
      <summary>The <see cref="N:System.Resources.Tools" /> namespace contains the <see cref="T:System.Resources.Tools.StronglyTypedResourceBuilder" /> class, which provides support for strongly-typed resources. Beginning with the .NET Framework version 2.0, this compile-time feature encapsulates access to resources by creating classes that contain a set of static read-only (get) properties, thus making it easier to consume resources.</summary>
    </member>
    <member name="N:System.Runtime">
      <summary>The <see cref="N:System.Runtime" /> namespace contains advanced types that support diverse namespaces such as the System, Runtime, and the Security namespaces.</summary>
    </member>
    <member name="N:System.Runtime.Caching">
      <summary>The <see cref="N:System.Runtime.Caching" /> namespace contains types that let you implement caching in NET Framework applications.</summary>
    </member>
    <member name="N:System.Runtime.Caching.Configuration">
      <summary>The <see cref="N:System.Runtime.Caching.Configuration" /> namespace contains configuration handler classes for the .NET Framework caching feature. </summary>
    </member>
    <member name="N:System.Runtime.Caching.Hosting">
      <summary>The types in the <see cref="N:System.Runtime.Caching.Hosting" /> namespace support .NET Framework hosting environments that use the caching features in ASP.NET. </summary>
    </member>
    <member name="N:System.Runtime.CompilerServices">
      <summary>The <see cref="N:System.Runtime.CompilerServices" /> namespace provides functionality for compiler writers who use managed code to specify attributes in metadata that affect the run-time behavior of the common language runtime.</summary>
    </member>
    <member name="N:System.Runtime.ConstrainedExecution">
      <summary>The <see cref="N:System.Runtime.ConstrainedExecution" /> namespace defines a set of types that enumerate and define a contract for reliability between the author of some code, and the developers who take a dependency on that code. The types in the <see cref="N:System.Runtime.ConstrainedExecution" /> namespace are intended for use in constrained execution regions (CERs).</summary>
    </member>
    <member name="N:System.Runtime.DesignerServices">
      <summary>The <see cref="N:System.Runtime.DesignerServices" /> namespace provides classes that support application and component designers in integrated development environments. </summary>
    </member>
    <member name="N:System.Runtime.DurableInstancing">
      <summary>This namespace contains classes that are used by the Windows Communication Foundation (WCF) infrastructure to implement a persistence provider.</summary>
    </member>
    <member name="N:System.Runtime.ExceptionServices">
      <summary>The <see cref="N:System.Runtime.ExceptionServices" /> namespace provides classes for advanced exception handling.</summary>
    </member>
    <member name="N:System.Runtime.Hosting">
      <summary>The <see cref="N:System.Runtime.Hosting" /> namespace contains advanced types that are used in application activation within application domains. </summary>
    </member>
    <member name="N:System.Runtime.InteropServices">
      <summary>The <see cref="N:System.Runtime.InteropServices" /> namespace provides a wide variety of members that support COM interop and platform invoke services. If you are unfamiliar with these services, see Interoperating with Unmanaged Code.</summary>
    </member>
    <member name="N:System.Runtime.InteropServices.ComTypes">
      <summary>The System.Runtime.InteropServices.ComTypes namespace contains methods that are defintions of COM functions for managed code. These functions replace the now-obsolete UCOM* methods in the <see cref="N:System.Runtime.InteropServices" /> namespace.</summary>
    </member>
    <member name="N:System.Runtime.InteropServices.CustomMarshalers">
      <summary>Provides internal marshaling support for the .NET Framework.</summary>
    </member>
    <member name="N:System.Runtime.InteropServices.Expando">
      <summary>The <see cref="N:System.Runtime.InteropServices.Expando" /> namespace contains the IExpando interface which allows modification of an object by adding or removing its members.</summary>
    </member>
    <member name="N:System.Runtime.InteropServices.WindowsRuntime">
      <summary>The <see cref="N:System.Runtime.InteropServices.WindowsRuntime" /> namespace contains classes that support interoperation between managed code and the Windows Runtime, and that enable the creation of Windows Runtime types with managed code. </summary>
    </member>
    <member name="N:System.Runtime.Remoting">
      <summary>The <see cref="N:System.Runtime.Remoting" /> namespace provides classes and interfaces that allow developers to create and configure distributed applications. Some of the more important classes of the <see cref="N:System.Runtime.Remoting" /> namespace are the <see cref="T:System.Runtime.Remoting.RemotingConfiguration" /> class, the <see cref="T:System.Runtime.Remoting.RemotingServices" /> class, and the <see cref="T:System.Runtime.Remoting.ObjRef" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Activation">
      <summary>The <see cref="N:System.Runtime.Remoting.Activation" /> namespace provides classes and objects that support server and client activation of remote objects.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Channels">
      <summary>The <see cref="N:System.Runtime.Remoting.Channels" /> namespace contains classes that support and handle channels and channel sinks, which are used as the transport medium when a client calls a method on a remote object.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Channels.Http">
      <summary>The <see cref="N:System.Runtime.Remoting.Channels.Http" /> namespace contains channels that use the HTTP protocol to transport messages and objects to and from remote locations. By default, the HTTP channels encode objects and method calls in SOAP format for transmission, but other encoding and decoding formatter sinks can be specified in the configuration properties of a channel. One of the most important classes of the <see cref="N:System.Runtime.Remoting.Channels.Http" /> namespace is the <see cref="T:System.Runtime.Remoting.Channels.Http.HttpChannel" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Channels.Ipc">
      <summary>The <see cref="N:System.Runtime.Remoting.Channels.Ipc" /> namespace defines a communication channel for remoting that uses the interprocess cmmunication (IPC) system of the Windows operating system. Because it does not use network communication, the IPC channel is much faster than the HTTP and TCP channels, but it can only be used for communication between application domains on the same physical computer.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Channels.Tcp">
      <summary>The <see cref="N:System.Runtime.Remoting.Channels.Tcp" /> namespace contains channels that use the TCP protocol to transport messages and objects to and from remote locations. By default, the TCP channels encode objects and method calls in binary format for transmission, but other encoding and decoding formatter sinks can be specified in the configuration properties of a channel. One of the most important classes of the <see cref="N:System.Runtime.Remoting.Channels.Tcp" /> namespace is the <see cref="T:System.Runtime.Remoting.Channels.Tcp.TcpChannel" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Contexts">
      <summary>The <see cref="N:System.Runtime.Remoting.Contexts" /> namespace contains objects that define the contexts all objects reside within. A context is an ordered sequence of properties that defines an environment for the objects within it. Contexts are created during the activation process for objects that are configured to require certain automatic services such synchronization, transactions, just-in-time (JIT) activation, security, and so on. Multiple objects can live inside a context.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Lifetime">
      <summary>The <see cref="N:System.Runtime.Remoting.Lifetime" /> namespace contains classes that manage the lifetime of remote objects.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Messaging">
      <summary>The <see cref="N:System.Runtime.Remoting.Messaging" /> namespace contains classes used to create and transmit messages. The remoting infrastructure uses messages to communicate with remote objects. Messages are used to transmit remote method calls, to activate remote objects, and to communicate information. A message object carries a set of named properties, including action identifiers, envoy information, and parameters. Some of the most important classes of the <see cref="N:System.Runtime.Remoting.Messaging" /> namespace are the <see cref="T:System.Runtime.Remoting.Messaging.AsyncResult" /> class, the <see cref="T:System.Runtime.Remoting.Messaging.RemotingSurrogateSelector" /> class, and the <see cref="T:System.Runtime.Remoting.Messaging.ReturnMessage" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Metadata">
      <summary>The <see cref="N:System.Runtime.Remoting.Metadata" /> namespace contains classes and attributes that can be used to customize generation and processing of SOAP for objects and fields. The classes of this namespace can be used to indicate the SOAPAction header field, type output, XML element name, and the method XML namespace URI. This information is used by the SOAP serializer during SOAP generation and the Soapsuds Tool (Soapsuds.exe) during Web Services Description Language (WSDL) generation. The information specified by the attributes in this namespace can be used on the receiving end of the generated SOAP to customize processing of the remoted method calls and objects. One of the most important classes of the <see cref="N:System.Runtime.Remoting.Metadata" /> namespace is <see cref="T:System.Runtime.Remoting.Metadata.SoapAttribute" /> class. The <see cref="T:System.Runtime.Remoting.Metadata.SoapAttribute" /> class is nonfunctional, and can only be used as the base class for all the metadata attributes. For more information on the SOAPAction HTTP header field, see www.w3.org/tr/soap/#_Toc478383528.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Metadata.W3cXsd2001">
      <summary>The <see cref="N:System.Runtime.Remoting.Metadata.W3cXsd2001" /> namespace contains the XML Schema Definition (XSD) that was defined by the World Wide Web Consortium (W3C) in 2001. The XML Schema Part 2: Datatypes specification from W3C identifies format and behavior of various data types. This namespace contains wrapper classes for the data types that conform to the W3C specification. All date and time types conform to the ISO standards specification.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.MetadataServices">
      <summary>The <see cref="N:System.Runtime.Remoting.MetadataServices" /> namespace contains the classes that are used by the Soapsuds.exe command-line tool and the user code to convert metadata to and from XML schema for the remoting infrastructure. Some of the most important classes of this namespace are the <see cref="T:System.Runtime.Remoting.MetadataServices.SdlChannelSink" /> class and the <see cref="T:System.Runtime.Remoting.MetadataServices.MetaData" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Proxies">
      <summary>The <see cref="N:System.Runtime.Remoting.Proxies" /> namespace contains classes that control and provide functionality for proxies. A proxy is a local object that is an image of a remote object. Proxies enable clients to access objects across remoting boundaries.</summary>
    </member>
    <member name="N:System.Runtime.Remoting.Services">
      <summary>The <see cref="N:System.Runtime.Remoting.Services" /> namespace contains service classes that provide functionality to the .NET Framework. One of the most important classes of this namespace is the <see cref="T:System.Runtime.Remoting.Services.TrackingServices" /> class.</summary>
    </member>
    <member name="N:System.Runtime.Serialization">
      <summary>The <see cref="N:System.Runtime.Serialization" /> namespace contains classes that can be used for serializing and deserializing objects. Serialization is the process of converting an object or a graph of objects into a linear sequence of bytes for either storage or transmission to another location. Deserialization is the process of taking in stored information and recreating objects from it.</summary>
    </member>
    <member name="N:System.Runtime.Serialization.Configuration">
      <summary>The System.Runtime.Serialization.Configuration namespace contains classes used to read the values found in application configuration under system.xml.serialization section.The classes in this namespace are used by the serialization infrastructure to handle the configuration elements and cannot be used directly. Instead, to configure the DataContractSerializer, create an application configuration file and use the following XML elements. &lt;system.xml.serialization&gt;&lt;dataContractSerializer&gt;&lt;declaredTypes&gt;&lt;add&gt; element for &lt;declaredTypes&gt;&lt;knownType&gt; element</summary>
    </member>
    <member name="N:System.Runtime.Serialization.Formatters">
      <summary>The <see cref="N:System.Runtime.Serialization.Formatters" /> namespace provides common enumerations, interfaces, and classes that are used by serialization formatters.</summary>
    </member>
    <member name="N:System.Runtime.Serialization.Formatters.Binary">
      <summary>The <see cref="N:System.Runtime.Serialization.Formatters.Binary" /> namespace contains the <see cref="T:System.Runtime.Serialization.Formatters.Binary.BinaryFormatter" /> class, which can be used to serialize and deserialize objects in binary format.</summary>
    </member>
    <member name="N:System.Runtime.Serialization.Formatters.Soap">
      <summary>The <see cref="N:System.Runtime.Serialization.Formatters.Soap" /> namespace contains the <see cref="T:System.Runtime.Serialization.Formatters.Soap.SoapFormatter" /> class, which can be used to serialize and deserialize objects in the SOAP format. When building applications that uses the types in this namespace, you must reference the System.Runtime.Serialization.Formatters.Soap.dll assembly.</summary>
    </member>
    <member name="N:System.Runtime.Serialization.Json">
      <summary>Provides classes related to Json serialization.</summary>
    </member>
    <member name="N:System.Runtime.Versioning">
      <summary>The <see cref="N:System.Runtime.Versioning" /> namespace contains advanced types that support versioning in side by side implementations of the .NET Framework.</summary>
    </member>
    <member name="N:System.Security">
      <summary>The <see cref="N:System.Security" /> namespace provides the underlying structure of the common language runtime security system, including base classes for permissions.</summary>
    </member>
    <member name="N:System.Security.AccessControl">
      <summary>The <see cref="N:System.Security.AccessControl" /> namespace provides programming elements that control access to and audit security-related actions on securable objects.</summary>
    </member>
    <member name="N:System.Security.Authentication">
      <summary>The Authentication namespace provides a set of enumerations that describe the security of a connection. These enumerations include <see cref="T:System.Security.Authentication.CipherAlgorithmType" />, <see cref="T:System.Security.Authentication.ExchangeAlgorithmType" />, <see cref="T:System.Security.Authentication.HashAlgorithmType" />, and <see cref="T:System.Security.Authentication.SslProtocolType" />.</summary>
    </member>
    <member name="N:System.Security.Authentication.ExtendedProtection">
      <summary>The <see cref="N:System.Security.Authentication.ExtendedProtection" /> namespace provides support for authentication using extended protection for applications. </summary>
    </member>
    <member name="N:System.Security.Authentication.ExtendedProtection.Configuration">
      <summary>The <see cref="N:System.Security.Authentication.ExtendedProtection.Configuration" /> namespace provides support for configuration of authentication using extended protection for applications.</summary>
    </member>
    <member name="N:System.Security.Claims">
      <summary>Contains classes that implement claims-based identity in the .NET Framework, including classes that represent claims, claims-based identities, and claims-based principals.</summary>
    </member>
    <member name="N:System.Security.Cryptography">
      <summary>The <see cref="N:System.Security.Cryptography" /> namespace provides cryptographic services, including secure encoding and decoding of data, as well as many other operations, such as hashing, random number generation, and message authentication. For more information, see Cryptographic Services.</summary>
    </member>
    <member name="N:System.Security.Cryptography.Pkcs">
      <summary>The <see cref="N:System.Security.Cryptography.Pkcs" /> namespace provides programming elements for Public Key Cryptography Standards (PKCS), including methods for signing data, exchanging keys, requesting certificates, public key encryption and decryption, and other security functions.</summary>
    </member>
    <member name="N:System.Security.Cryptography.X509Certificates">
      <summary>The <see cref="N:System.Security.Cryptography.X509Certificates" /> namespace contains the common language runtime implementation of the Authenticode X.509 v.3 certificate. This certificate is signed with a private key that uniquely and positively identifies the holder of the certificate.</summary>
    </member>
    <member name="N:System.Security.Cryptography.Xml">
      <summary>The <see cref="N:System.Security.Cryptography.Xml" /> namespace contains classes to support the creation and validation of XML digital signatures. The classes in this namespace implement the World Wide Web Consortium Recommendation, "XML-Signature Syntax and Processing", described at http://www.w3.org/TR/xmldsig-core/.</summary>
    </member>
    <member name="N:System.Security.Permissions">
      <summary>The <see cref="N:System.Security.Permissions" /> namespace defines classes that control access to operations and resources based on policy.</summary>
    </member>
    <member name="N:System.Security.Policy">
      <summary>The <see cref="N:System.Security.Policy" /> namespace contains code groups, membership conditions, and evidence. These three types of classes are used to create the rules applied by the common language runtime security policy system. Evidence classes are the input to security policy and membership conditions are the switches; together these create policy statements and determine the granted permission set. Policy levels and code groups are the structure of the policy hierarchy. Code groups are the encapsulation of a rule and are arranged hierarchically in a policy level.</summary>
    </member>
    <member name="N:System.Security.Principal">
      <summary>The <see cref="N:System.Security.Principal" /> namespace defines a principal object that represents the security context under which code is running. For more information, see Role-Based Security. </summary>
    </member>
    <member name="N:System.Security.RightsManagement">
      <summary>Provides types to support rights management of application-created content such as that stored in a <see cref="T:System.IO.Packaging.Package" /> or <see cref="T:System.Windows.Xps.Packaging.XpsDocument" />.</summary>
    </member>
    <member name="N:System.ServiceModel">
      <summary>Provides classes related to the service model.</summary>
    </member>
    <member name="N:System.ServiceModel.Activation">
      <summary>Provides classes related to service model activation.</summary>
    </member>
    <member name="N:System.ServiceModel.Activation.Configuration">
      <summary>Represents the configuration settings for the SMSvcHost.exe tool.This namespace represents the configuration elements that can be configured in the SMSvcHost.exe.config file. Specifically, it includes all machine-wide settings that need to be configured.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities">
      <summary>The System.ServiceModel.Activities namespace contains classes that allow you to send messages to and receive messages from workflows. These activities are used when writing workflow services and allow you to define how messages are sent and received in a workflow. </summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Activation">
      <summary>The System.ServiceModel.Activities.Activation namespace contains the <see cref="T:System.ServiceModel.Activities.Activation.WorkflowServiceHostFactory" /> that provides instances of <see cref="T:System.ServiceModel.Activities.WorkflowServiceHost" /> in managed hosting environments where the host instance is created dynamically in response to incoming messages.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Configuration">
      <summary>The System.ServiceModel.Activities.Configuration namespace contains classes used to configure workflow services. These classes allow you to apply service and endpoint behaviors and configure tracking.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Description">
      <summary>The System.ServiceModel.Activities.Description namespace contains classes that enable you to control various behaviors related to workflow services.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Presentation">
      <summary>Provides classes related to service model presentation activities.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Presentation.Factories">
      <summary>Contains factories that create Receive/SendReply and Send/ReceiveReply activity pairs.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Tracking">
      <summary>The System.ServiceModel.Activities.Tracking namespace contains classes used to create tracking records for a workflow service instance. Tracking records are created by created by the workflow runtime during the execution of a workflow instance, and are sent to registered tracking participants.</summary>
    </member>
    <member name="N:System.ServiceModel.Activities.Tracking.Configuration">
      <summary>The System.ServiceModel.Activities.Tracking.Configuration namespace contains classes used to configure tracking for service hosted workflows. These classes allow you to specify the tracking information you want your workflow service to generate.</summary>
    </member>
    <member name="N:System.ServiceModel.Channels">
      <summary>Provides classes related to service model channels.</summary>
    </member>
    <member name="N:System.ServiceModel.ComIntegration">
      <summary>Defines classes needed for integration with COM applications.</summary>
    </member>
    <member name="N:System.ServiceModel.Configuration">
      <summary>Provides classes related to service model configuration.</summary>
    </member>
    <member name="N:System.ServiceModel.Description">
      <summary>Provides classes related to the service model description.</summary>
    </member>
    <member name="N:System.ServiceModel.Diagnostics">
      <summary>The <see cref="N:System.ServiceModel.Diagnostics" /> namespace contains an enumeration that is used to configure performance counters for diagnostics purposes.</summary>
    </member>
    <member name="N:System.ServiceModel.Discovery">
      <summary>The System.ServiceModel.Discovery namespace contains the classes required for the creation and use of discoverable services. These classes implement the WS-Discovery protocol for Windows Communication Foundation (WCF).</summary>
    </member>
    <member name="N:System.ServiceModel.Discovery.Configuration">
      <summary>The System.ServiceModel.Discovery.Configuration namespace contains classes that allow you to configure discovery settings in a configuration file.</summary>
    </member>
    <member name="N:System.ServiceModel.Discovery.Version11">
      <summary>The System.ServiceModel.Discovery.Version11 namespace contains the classes required for the creation and use of discoverable services. These classes implement the WS-Discovery version 1.1 protocol for Windows Communication Foundation (WCF).</summary>
    </member>
    <member name="N:System.ServiceModel.Discovery.VersionApril2005">
      <summary>The System.ServiceModel.Discovery.VersionApril2005 namespace contains the classes required for the creation and use of discoverable services. These classes implement the April 2005 version of the WS-Discovery protocol for Windows Communication Foundation (WCF).</summary>
    </member>
    <member name="N:System.ServiceModel.Discovery.VersionCD1">
      <summary>The System.ServiceModel.Discovery.VersionCD1 namespace contains the classes required for the creation and use of discoverable services. These classes implement the Committee Draft 1 version of the WS-Discovery protocol for Windows Communication Foundation (WCF).</summary>
    </member>
    <member name="N:System.ServiceModel.Dispatcher">
      <summary>Provides classes related to dispatching the service model.</summary>
    </member>
    <member name="N:System.ServiceModel.MsmqIntegration">
      <summary>The <see cref="N:System.ServiceModel.MsmqIntegration" /> namespace contains classes that relate to Microsoft Message Queuing Service (MSMQ) integration, which allows communication between MSMQ applications and Windows Communication Foundation (WCF) applications.</summary>
    </member>
    <member name="N:System.ServiceModel.PeerResolvers">
      <summary>The <see cref="N:System.ServiceModel.PeerResolvers" /> namespace contains classes that allow developers to customize and extend the Peer Name Resolution Protocol service.</summary>
    </member>
    <member name="N:System.ServiceModel.Persistence">
      <summary>The <see cref="N:System.ServiceModel.Persistence" /> namespace contains classes that allow workflow services (WCF services implemented as workflows) to be persisted into the persistence store.</summary>
    </member>
    <member name="N:System.ServiceModel.Routing">
      <summary>This namespace contains classes that are used to configure and manage the WCF <see cref="T:System.ServiceModel.Routing.RoutingService" />. </summary>
    </member>
    <member name="N:System.ServiceModel.Routing.Configuration">
      <summary>This namespace contains configuration classes that are used by the Windows Communication Foundation (WCF) infrastructure to convert XML into Common Language Runtime (CLR) types. You can also manipulate these classes directly for extensibility purposes.</summary>
    </member>
    <member name="N:System.ServiceModel.Security">
      <summary>The <see cref="N:System.ServiceModel.Security" /> namespace deals with general security-related topics, for example, <see cref="T:System.ServiceModel.Security.SecurityAlgorithmSuite" />.</summary>
    </member>
    <member name="N:System.ServiceModel.Security.Tokens">
      <summary>Deals with security tokens and certificates.</summary>
    </member>
    <member name="N:System.ServiceModel.Syndication">
      <summary>Provides classes related to service model syndication.</summary>
    </member>
    <member name="N:System.ServiceModel.Web">
      <summary>Provides classes related to using the service model on the web.</summary>
    </member>
    <member name="N:System.ServiceModel.XamlIntegration">
      <summary>Provides utility classes to enable the serialization of Windows Communication Foundation (WCF) entities to XAML.</summary>
    </member>
    <member name="N:System.ServiceProcess">
      <summary>The <see cref="N:System.ServiceProcess" /> namespace provides classes that allow you to implement, install, and control Windows service applications. Services are long-running executables that run without a user interface. Implementing a service involves inheriting from the <see cref="T:System.ServiceProcess.ServiceBase" /> class and defining specific behavior to process when start, stop, pause, and continue commands are passed in, as well as custom behavior and actions to take when the system shuts down.</summary>
    </member>
    <member name="N:System.ServiceProcess.Design">
      <summary>The <see cref="N:System.ServiceProcess.Design" /> namespace contains classes that can be used to extend design-time support for Windows services applications.</summary>
    </member>
    <member name="N:System.Speech.AudioFormat">
      <summary>The <see cref="N:System.Speech.AudioFormat" /> namespace consists of a single class, <see cref="T:System.Speech.AudioFormat.SpeechAudioFormatInfo" />, which contains information about the format of the audio that is being input to the speech recognition engine, or being output from the speech synthesis engine.</summary>
    </member>
    <member name="N:System.Speech.Recognition">
      <summary>The <see cref="N:System.Speech.Recognition" /> namespace contains Windows Desktop Speech technology types for implementing speech recognition.</summary>
    </member>
    <member name="N:System.Speech.Recognition.SrgsGrammar">
      <summary>With the members of the <see cref="N:System.Speech.Recogntion.SRGSGrammar" /> namespace, you can programmatically create grammars that comply with the W3C Speech Recognition Grammar Specification Version 1.0 (SRGS).</summary>
    </member>
    <member name="N:System.Speech.Synthesis">
      <summary>The N:System.Speech.Synthesis namespace contains classes for initializing and configuring a speech synthesis engine, for creating prompts, for generating speech, for responding to events, and for modifying voice characteristics.</summary>
    </member>
    <member name="N:System.Speech.Synthesis.TtsEngine">
      <summary>Supports the creation of Speech Synthesis Markup Language (SSML) based custom engines for rendering text to speech (TTS).</summary>
    </member>
    <member name="N:System.Text">
      <summary>The <see cref="N:System.Text" /> namespace contains classes that represent ASCII and Unicode character encodings; abstract base classes for converting blocks of characters to and from blocks of bytes; and a helper class that manipulates and formats <see cref="T:System.String" /> objects without creating intermediate instances of <see cref="T:System.String" />.</summary>
    </member>
    <member name="N:System.Text.RegularExpressions">
      <summary>The <see cref="N:System.Text.RegularExpressions" /> namespace contains classes that provide access to the .NET Framework regular expression engine. The namespace provides regular expression functionality that may be used from any platform or language that runs within the Microsoft .NET Framework. In addition to the types contained in this namespace, the <see cref="T:System.Configuration.RegexStringValidator" /> class enables you to determine whether a particular string conforms to a regular expression pattern.</summary>
    </member>
    <member name="N:System.Threading">
      <summary>The <see cref="N:System.Threading" /> namespace provides classes and interfaces that enable multithreaded programming. In addition to classes for synchronizing thread activities and access to data (<see cref="T:System.Threading.Mutex" />, <see cref="T:System.Threading.Monitor" />, <see cref="T:System.Threading.Interlocked" />, <see cref="T:System.Threading.AutoResetEvent" />, and so on), this namespace includes a <see cref="T:System.Threading.ThreadPool" /> class that allows you to use a pool of system-supplied threads, and a <see cref="T:System.Threading.Timer" /> class that executes callback methods on thread pool threads.</summary>
    </member>
    <member name="N:System.Threading.Tasks">
      <summary>The System.Threading.Tasks namespace provides types that simplify the work of writing concurrent and asynchronous code. The main types are <see cref="T:System.Threading.Tasks.Task" /> which represents an asynchronous operation that can be waited on and cancelled, and <see cref="T:System.Threading.Tasks.Task`1" />, which is a task that can return a value. The <see cref="T:System.Threading.Tasks.TaskFactory" /> class provides static methods for creating and starting tasks, and the <see cref="T:System.Threading.Tasks.TaskScheduler" /> class provides the default thread scheduling infrastructure.</summary>
    </member>
    <member name="N:System.Timers">
      <summary>The <see cref="N:System.Timers" /> namespace provides the <see cref="T:System.Timers.Timer" /> component, which allows you to raise an event on a specified interval.</summary>
    </member>
    <member name="N:System.Transactions">
      <summary>The <see cref="N:System.Transactions" /> namespace contains classes that allow you to write your own transactional application and resource manager. Specifically, you can create and participate in a transaction (local or distributed) with one or multiple participants. </summary>
    </member>
    <member name="N:System.Transactions.Configuration">
      <summary>The <see cref="N:System.Transactions.Configuration" /> namespace contains classes that describe configuration options used by <see cref="N:System.Transactions" /> classes.</summary>
    </member>
    <member name="N:System.Web">
      <summary>The <see cref="N:System.Web" /> namespace supplies classes and interfaces that enable browser-server communication. This namespace includes the <see cref="T:System.Web.HttpRequest" /> class, which provides extensive information about the current HTTP request; the <see cref="T:System.Web.HttpResponse" /> class, which manages HTTP output to the client; and the <see cref="T:System.Web.HttpServerUtility" /> class, which provides access to server-side utilities and processes. <see cref="N:System.Web" /> also includes classes for cookie manipulation, file transfer, exception information, and output cache control.</summary>
    </member>
    <member name="N:System.Web.ApplicationServices">
      <summary>The <see cref="N:System.Web.ApplicationServices" /> namespace provides classes that enable you to access ASP.NET forms authentication, roles, and profiles application services as Windows Communication Foundation (WCF) services.</summary>
    </member>
    <member name="N:System.Web.Caching">
      <summary>The <see cref="N:System.Web.Caching" /> namespace provides classes for caching frequently used data on the server. </summary>
    </member>
    <member name="N:System.Web.ClientServices">
      <summary>The <see cref="N:System.Web.ClientServices" /> namespace provides classes that support access in Windows-based applications to the Microsoft Ajax authentication, roles, and profiles services. </summary>
    </member>
    <member name="N:System.Web.ClientServices.Providers">
      <summary>The <see cref="N:System.Web.ClientServices.Providers" /> namespace contains client service providers and other classes that support access in Windows-based applications to the Microsoft Ajax authentication, roles, and profile services. </summary>
    </member>
    <member name="N:System.Web.Compilation">
      <summary>The <see cref="N:System.Web.Compilation" /> namespace contains classes for generating and compiling custom file types within the ASP.NET build environment.</summary>
    </member>
    <member name="N:System.Web.Configuration">
      <summary>The <see cref="N:System.Web.Configuration" /> namespace contains classes that are used to set up ASP.NET configuration.</summary>
    </member>
    <member name="N:System.Web.Configuration.Internal">
      <summary>The <see cref="N:System.Web.Configuration.Internal" /> namespace contains configuration types intended for internal use only.</summary>
    </member>
    <member name="N:System.Web.DynamicData">
      <summary>The <see cref="N:System.Web.DynamicData" /> namespace contains classes that provide the core functionality for ASP.NET Dynamic Data. It also provides extensibility features that let you customize dynamic data behavior.</summary>
    </member>
    <member name="N:System.Web.DynamicData.Design">
      <summary>The <see cref="N:System.Web.DynamicData.Design" /> namespace contains classes that provide design-time support for the classes in the <see cref="N:System.Web.DynamicData" /> namespace. </summary>
    </member>
    <member name="N:System.Web.DynamicData.ModelProviders">
      <summary>The <see cref="N:System.Web.DynamicData.ModelProviders" /> namespace contains classes that define the core functionality for ASP.NET Dynamic Data providers.</summary>
    </member>
    <member name="N:System.Web.Handlers">
      <summary>The <see cref="N:System.Web.Handlers" /> namespace contains HTTP handler classes that process HTTP requests to a Web server.</summary>
    </member>
    <member name="N:System.Web.Hosting">
      <summary>The <see cref="N:System.Web.Hosting" /> namespace provides the functionality for hosting ASP.NET applications from managed applications outside Microsoft Internet Information Services (IIS).</summary>
    </member>
    <member name="N:System.Web.Instrumentation">
      <summary>The <see cref="N:System.Web.Instrumentation" /> namespace contains types that provide information about current page execution, that include execution listeners and instrumentation, and that include methods that are called before and after view engine renders output.</summary>
    </member>
    <member name="N:System.Web.Mail">
      <summary>The classes in this namespace have been deprecated. Use the <see cref="N:System.Net.Mail" /> namespace instead. The <see cref="N:System.Web.Mail" /> namespace contains classes that enable you to construct and send messages using the CDOSYS (Collaboration Data Objects for Windows 2000) message component. The mail message is delivered either through the SMTP mail service built into Microsoft Windows 2000 or through an arbitrary SMTP server. The classes in this namespace can be used from ASP.NET or from any managed application.</summary>
    </member>
    <member name="N:System.Web.Management">
      <summary>The <see cref="N:System.Web.Management" /> namespace contains classes and interfaces for managing and monitoring the health of Web applications.</summary>
    </member>
    <member name="N:System.Web.Mobile">
      <summary>The <see cref="N:System.Web.Mobile" /> namespace contains the core capabilities, including authentication and error handling, required for building ASP.NET mobile Web applications.  For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.ModelBinding">
      <summary>The <see cref="N:System.Web.ModelBinding" /> namespace provides classes that enable you to bind data objects to ASP.NET Web Forms server controls.</summary>
    </member>
    <member name="N:System.Web.Profile">
      <summary>The <see cref="N:System.Web.Profile" /> namespace contains classes that are used to work with ASP.NET user profiles in Web server applications.</summary>
    </member>
    <member name="N:System.Web.Query.Dynamic">
      <summary>The <see cref="N:System.Web.Query.Dynamic" /> namespace contains classes that are used to parse expressions from a <see cref="T:System.Web.UI.WebControls.LinqDataSource" /> control into a language-integrated query (LINQ).</summary>
    </member>
    <member name="N:System.Web.RegularExpressions">
      <summary>The <see cref="N:System.Web.RegularExpressions" /> namespace provides precompiled regular expressions that can be used to parse ASP.NET files. All members of the <see cref="N:System.Web.RegularExpressions" /> namespace are descendants of the <see cref="T:System.Text.RegularExpressions.Regex" /> class.</summary>
    </member>
    <member name="N:System.Web.Routing">
      <summary>The <see cref="N:System.Web.Routing" /> namespace provides classes that are used with URL routing, which enables you to use URLs that do not map to a physical file.</summary>
    </member>
    <member name="N:System.Web.Script">
      <summary>The <see cref="N:System.Web.Script" /> namespace contains classes that provide script resource information.</summary>
    </member>
    <member name="N:System.Web.Script.Serialization">
      <summary>The <see cref="N:System.Web.Script.Serialization" /> namespace contains classes that provide JavaScript Object Notation (JSON) serialization and deserialization for managed types. It also provides extensibility features to customize serialization behavior.</summary>
    </member>
    <member name="N:System.Web.Script.Services">
      <summary>The <see cref="N:System.Web.Script.Services" /> namespace provides attributes that let you customize Web service support for AJAX functionality in ASP.NET.</summary>
    </member>
    <member name="N:System.Web.Security">
      <summary>The <see cref="N:System.Web.Security" /> namespace contains classes that are used to implement ASP.NET security in Web server applications.</summary>
    </member>
    <member name="N:System.Web.Security.AntiXss">
      <summary>Contains methods that you can use to encode strings in order help you protect your application against cross-site scripting (XSS) attacks and LDAP injection attacks.</summary>
    </member>
    <member name="N:System.Web.Services">
      <summary>The <see cref="N:System.Web.Services" /> namespace consists of the classes that enable you to create XML Web services using ASP.NET and XML Web service clients. XML Web services are applications that provide the ability to exchange messages in a loosely coupled environment using standard protocols such as HTTP, XML, XSD, SOAP, and WSDL. XML Web services enable the building of modular applications within and across companies in heterogeneous environments making them interoperable with a broad variety of implementations, platforms and devices. The SOAP-based XML messages of these applications can have well-defined (structured and typed), or loosely defined parts (using arbitrary XML). The ability of the messages to evolve over time without breaking the protocol is fundamental to the flexibility and robustness of XML Web services as a building block for the future of the Web.</summary>
    </member>
    <member name="N:System.Web.Services.Configuration">
      <summary>The <see cref="N:System.Web.Services.Configuration" /> namespace consists of the classes that configure how XML Web services created using ASP.NET run.</summary>
    </member>
    <member name="N:System.Web.Services.Description">
      <summary>The <see cref="N:System.Web.Services.Description" /> namespace consists of the classes that enable you to publicly describe an XML Web service by using the Web Services Description Language (WSDL). Each class in this namespace corresponds to a specific element in the WSDL specification, and the class hierarchy corresponds to the XML structure of a valid WSDL document. For more information about WSDL, see the specification at the W3C Web site (http://www.w3.org/TR/wsdl/).</summary>
    </member>
    <member name="N:System.Web.Services.Discovery">
      <summary>The <see cref="N:System.Web.Services.Discovery" /> namespace consists of the classes that allows XML Web service clients to locate the available XML Web services on a Web server through a process called XML Web services Discovery.</summary>
    </member>
    <member name="N:System.Web.Services.Protocols">
      <summary>The <see cref="N:System.Web.Services.Protocols" /> namespace consists of the classes that define the protocols used to transmit data across the wire during the communication between XML Web service clients and XML Web services created using ASP.NET.</summary>
    </member>
    <member name="N:System.Web.SessionState">
      <summary>The <see cref="N:System.Web.SessionState" /> namespace supplies classes and interfaces that enable storage of data specific to a single client within a Web application on the server. The session-state data is used to give the client the appearance of a persistent connection with the application. State information can be stored within local process memory or, for Web farm configurations, it can be stored out of process using either the ASP.NET State service or a Microsoft SQL Server database.</summary>
    </member>
    <member name="N:System.Web.UI">
      <summary>The <see cref="N:System.Web.UI" /> namespace provides classes and interfaces that enable you to create ASP.NET server controls and ASP.NET Web pages for the user interface of your ASP.NET Web applications. </summary>
    </member>
    <member name="N:System.Web.UI.Adapters">
      <summary>The <see cref="N:System.Web.UI.Adapters" /> namespace contains the base classes for control adapters and page adapters. You can use these adapters to override the life-cycle states of pages and controls. This is useful if you want to modify a control's markup or behavior for new markup standards or for specific browsers.</summary>
    </member>
    <member name="N:System.Web.UI.DataVisualization.Charting">
      <summary>The <see cref="N:System.Web.UI.DataVisualization.Charting" /> namespace contains methods and properties for the Chart Web server control.</summary>
    </member>
    <member name="N:System.Web.UI.Design">
      <summary>The <see cref="N:System.Web.UI.Design" /> namespace contains classes that can be used to extend design-time support for ASP.NET Web pages and for Web server controls.</summary>
    </member>
    <member name="N:System.Web.UI.Design.Directives">
      <summary>The <see cref="N:System.Web.UI.Design.Directives" /> namespace contains classes that can be used to extend design-time support for ASP.NET Web page directives.</summary>
    </member>
    <member name="N:System.Web.UI.Design.MobileControls">
      <summary>The <see cref="N:System.Web.UI.Design.MobileControls" /> namespace contains classes that provide design-time support for the classes in the <see cref="N:System.Web.UI.MobileControls" /> namespace.  For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.UI.Design.MobileControls.Converters">
      <summary>The <see cref="N:System.Web.UI.Design.MobileControls.Converters" /> namespace contains classes that provide design-time support for data type converters in mobile controls. For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.UI.Design.WebControls">
      <summary>The <see cref="N:System.Web.UI.Design.WebControls" /> namespace contains classes that can be used to extend design-time support for Web server controls.</summary>
    </member>
    <member name="N:System.Web.UI.Design.WebControls.WebParts">
      <summary>The <see cref="N:System.Web.UI.Design.WebControls.WebParts" /> namespace contains classes that provide design-time support for controls derived from classes in the <see cref="N:System.Web.UI.WebControls.WebParts" /> namespace.</summary>
    </member>
    <member name="N:System.Web.UI.HtmlControls">
      <summary>The <see cref="N:System.Web.UI.HtmlControls" /> namespace contains classes that allow you to create HTML server controls on a Web Forms page. HTML server controls run on the server and map directly to standard HTML tags supported by most browsers. This allows you to programmatically control the HTML elements on a Web Forms page.</summary>
    </member>
    <member name="N:System.Web.UI.MobileControls">
      <summary>The <see cref="N:System.Web.UI.MobileControls" /> namespace contains a set of ASP.NET server controls that can render your application for different mobile devices. The APIs in this namespace are obsolete. For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.UI.MobileControls.Adapters">
      <summary>The <see cref="N:System.Web.UI.MobileControls.Adapters" /> namespace contains classes you can use to override the life-cycle stages of a mobile control to modify its default HTML, CHTML, or WML markup or behavior for new markup standards or for specific browsers and mobile devices.  The APIs in this namespace are obsolete. For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.UI.MobileControls.Adapters.XhtmlAdapters">
      <summary>The <see cref="N:System.Web.UI.MobileControls.Adapters.XhtmlAdapters" /> namespace contains classes you can use to override the life-cycle stages of a mobile control to modify its default XHTML markup or behavior for new markup standards or for specific browsers and mobile devices. For information about how to develop ASP.NET mobile applications, see the www.asp.net/mobile Web site.</summary>
    </member>
    <member name="N:System.Web.UI.WebControls">
      <summary>The <see cref="N:System.Web.UI.WebControls" /> namespace contains classes that allow you to create Web server controls on a Web page. Web server controls run on the server and include form controls such as buttons and text boxes. They also include special-purpose controls such as a calendar. Because Web server controls run on the server, you can programmatically control these elements. Although Web server controls are rendered as HTML, their object model does not necessarily reflect HTML syntax.</summary>
    </member>
    <member name="N:System.Web.UI.WebControls.Adapters">
      <summary>The <see cref="N:System.Web.UI.WebControls.Adapters" /> namespace contains classes that you can use to override the life-cycle stages of a Web control. The classes enable you to modify a control's markup or behavior for new markup standards or for specific browsers.</summary>
    </member>
    <member name="N:System.Web.UI.WebControls.Expressions">
      <summary>The <see cref="N:System.Web.UI.WebControls.Expressions" /> namespace provides classes that enable you to filter data in a Web page by using the <see cref="T:System.Web.UI.WebControls.QueryExtender" /> control. </summary>
    </member>
    <member name="N:System.Web.UI.WebControls.WebParts">
      <summary>The <see cref="N:System.Web.UI.WebControls.WebParts" /> namespace contains classes and interfaces known collectively as the Web Parts control set. You can use these classes to create Web pages whose appearance and behavior can be modified (personalized) by end users. Each user's personalized settings for a page are saved for future browser sessions.</summary>
    </member>
    <member name="N:System.Web.Util">
      <summary>The <see cref="N:System.Web.Util" /> namespace is a collection of classes that enable callback methods to be run under the scope of a transaction and that enable work to be posted to separate threads.</summary>
    </member>
    <member name="N:System.Web.WebSockets">
      <summary>The <see cref="N:System.Web.WebSockets" /> namespace contains classes that support adding WebSocket functionality to ASP.NET Web Forms applications.</summary>
    </member>
    <member name="N:System.Windows">
      <summary>Provides several important Windows Presentation Foundation (WPF) base element classes, various classes that support the WPF property system and event logic, and other types that are more broadly consumed by the WPF core and framework.</summary>
    </member>
    <member name="N:System.Windows.Annotations">
      <summary>Provides classes to support user-created annotations on content displayed in WPF document viewing controls.</summary>
    </member>
    <member name="N:System.Windows.Annotations.Storage">
      <summary>Provides types that define the storage architecture and mediums to save and retreive user-created annotations.</summary>
    </member>
    <member name="N:System.Windows.Automation">
      <summary>Provides support for Windows Presentation Foundation (WPF) UI Automation clients.</summary>
    </member>
    <member name="N:System.Windows.Automation.Peers">
      <summary>Defines the <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> base class and a set of types that derive from it and that correspond to Microsoft .NET Framework controls. Each <see cref="T:System.Windows.Automation.Peers.AutomationPeer" /> exposes the corresponding Microsoft .NET Framework control to Microsoft UI Automation.</summary>
    </member>
    <member name="N:System.Windows.Automation.Provider">
      <summary>Provides APIs for creating UI Automation providers.</summary>
    </member>
    <member name="N:System.Windows.Automation.Text">
      <summary>Contains enumerations that specify text formatting and related behavior for Windows Presentation Foundation (WPF) UI automation.</summary>
    </member>
    <member name="N:System.Windows.Baml2006">
      <summary>Contains reader and writer classes that can consume XAML in BAML form.</summary>
    </member>
    <member name="N:System.Windows.Controls">
      <summary>Provides classes to create elements, known as controls, that enable a user to interact with an application. The control classes are at the core of the user's experience with any application because they allow a user to view, select, or enter data or other information. </summary>
    </member>
    <member name="N:System.Windows.Controls.Primitives">
      <summary>Contains base classes and controls that are intended to be used as part of other more complex controls.</summary>
    </member>
    <member name="N:System.Windows.Controls.Ribbon">
      <summary>Contains types that are used to create a user interface by using the Microsoft Ribbon for WPF.</summary>
    </member>
    <member name="N:System.Windows.Controls.Ribbon.Primitives">
      <summary>Contains types that are used as layout containers in a Microsoft Ribbon for WPF control.</summary>
    </member>
    <member name="N:System.Windows.Converters">
      <summary>Provides classes to support type conversion during serialization in Windows Presentation Foundation (WPF).</summary>
    </member>
    <member name="N:System.Windows.Data">
      <summary>Contains classes used for binding properties to data sources, data source provider classes, and data-specific implementations of collections and views.</summary>
    </member>
    <member name="N:System.Windows.Diagnostics"></member>
    <member name="N:System.Windows.Documents">
      <summary>Contains types that support <see cref="T:System.Windows.Documents.FixedDocument" />, <see cref="T:System.Windows.Documents.FlowDocument" />, and XML Paper Specification (XPS) document creation. </summary>
    </member>
    <member name="N:System.Windows.Documents.DocumentStructures">
      <summary>Provides classes to describe the structure of an <see cref="T:System.Windows.Xps.Packaging.XpsDocument" /> in terms of sections, paragraphs, figures, bulleted or numbered lists, and tables. </summary>
    </member>
    <member name="N:System.Windows.Documents.Serialization">
      <summary>Provides types that support the creation and use of run-time accessible plug-in serializers that read and write documents in different data formats.</summary>
    </member>
    <member name="N:System.Windows.Forms">
      <summary>The <see cref="N:System.Windows.Forms" /> namespace contains classes for creating Windows-based applications that take full advantage of the rich user interface features available in the Microsoft Windows operating system.</summary>
    </member>
    <member name="N:System.Windows.Forms.ComponentModel.Com2Interop">
      <summary>The <see cref="N:System.Windows.Forms.ComponentModel.Com2Interop" /> namespace contains helper classes that Visual Studio uses to display property pages while in design mode.</summary>
    </member>
    <member name="N:System.Windows.Forms.DataVisualization.Charting">
      <summary>The <see cref="N:System.Windows.Forms.DataVisualization.Charting" /> namespace contains methods and properties for the Chart Windows forms control.</summary>
    </member>
    <member name="N:System.Windows.Forms.Design">
      <summary>The <see cref="N:System.Windows.Forms.Design" /> namespace contains classes that support design-time configuration and behavior for Windows Forms components. These classes consist of designer classes that provide support for Windows Forms components, a set of design-time services; UITypeEditor classes for configuring certain types of properties, and classes for importing ActiveX controls.</summary>
    </member>
    <member name="N:System.Windows.Forms.Design.Behavior">
      <summary>The <see cref="N:System.Windows.Forms.Design.Behavior" /> namespace contains classes for creating custom user interface behavior for components at design time.</summary>
    </member>
    <member name="N:System.Windows.Forms.Integration">
      <summary>Contains classes that support interoperation of Windows Forms and WPF controls.</summary>
    </member>
    <member name="N:System.Windows.Forms.Layout">
      <summary>The <see cref="N:System.Windows.Forms.Layout" /> namespace contains classes for implementing layout behaviors in your form or control.</summary>
    </member>
    <member name="N:System.Windows.Forms.PropertyGridInternal">
      <summary>The PropertyGridInternal namespace provides internal support for the PropertyGrid control. The classes in this namespace support the .NET Framework infrastructure and are not intended to be used directly from your code.</summary>
    </member>
    <member name="N:System.Windows.Forms.VisualStyles">
      <summary>The <see cref="N:System.Windows.Forms.VisualStyles" /> namespace contains classes for rendering controls and other Windows user interface (UI) elements with visual styles in operating systems that support them. </summary>
    </member>
    <member name="N:System.Windows.Ink">
      <summary>Provides classes to interact with and manipulate ink on the Windows Presentation Foundation (WPF) platform.</summary>
    </member>
    <member name="N:System.Windows.Input">
      <summary>Provides types to support the Windows Presentation Foundation (WPF) input system. This includes device abstraction classes for mouse, keyboard, and stylus devices, a common input manager class, support for commanding and custom commands, and various utility classes.</summary>
    </member>
    <member name="N:System.Windows.Input.Manipulations">
      <summary>Provides functionality for monitoring and responding to a collection of manipulators as a single composite, enabling an application to track the changes to the composite instead of the individual components.</summary>
    </member>
    <member name="N:System.Windows.Input.StylusPlugIns">
      <summary>Provides classes that support manipulating data from a tablet pen on the Windows Presentation Foundation (WPF) platform.  These classes offerlow-level control over <see cref="T:System.Windows.Input.Stylus" /> input and the creation of digital ink <see cref="T:System.Windows.Ink.Stroke" /> objects. The <see cref="T:System.Windows.Input.StylusPlugIns.StylusPlugIn" /> class provides a mechanism for you to implement custom behavior and apply that behavior to the stream of data coming from the stylus device for the optimal performance. </summary>
    </member>
    <member name="N:System.Windows.Interop">
      <summary>Provides supporting types for interoperation between Windows Presentation Foundation (WPF) and other technologies such as Win32 APIs and provides base classes for other specific interoperation scenarios involving WPF.</summary>
    </member>
    <member name="N:System.Windows.Markup">
      <summary>Provides types to support XAML. Some of these types are located in WPF assemblies and are specific to WPF scenarios that involve XAML. Other types in this namespace provide support for .NET Framework XAML Services in general, and do not require referencing WPF assemblies.</summary>
    </member>
    <member name="N:System.Windows.Markup.Localizer">
      <summary>Provides types that assist in the localization of binary XAML (BAML) sources.</summary>
    </member>
    <member name="N:System.Windows.Markup.Primitives">
      <summary>Contains classes that support Extensible Application Markup Language (XAML) in Windows Presentation Foundation (WPF).</summary>
    </member>
    <member name="N:System.Windows.Media">
      <summary>Provides types that enable integration of rich media, including drawings, text, and audio/video content in Windows Presentation Foundation (WPF) applications.</summary>
    </member>
    <member name="N:System.Windows.Media.Animation">
      <summary>Provides types that support property animation functionality, including timelines, storyboards, and key frames. </summary>
    </member>
    <member name="N:System.Windows.Media.Converters">
      <summary>Contains classes that are used by the <see cref="T:System.Windows.Markup.Primitives.MarkupWriter" /> for serialization of Extensible Application Markup Language (XAML).</summary>
    </member>
    <member name="N:System.Windows.Media.Effects">
      <summary>Provides types that can be used to apply visual effects to bitmap images.</summary>
    </member>
    <member name="N:System.Windows.Media.Imaging">
      <summary>Provides types that are used to encode and decode bitmap images.</summary>
    </member>
    <member name="N:System.Windows.Media.Media3D">
      <summary>Contains types that support 3-D presentation in Windows Presentation Foundation (WPF) applications.</summary>
    </member>
    <member name="N:System.Windows.Media.Media3D.Converters">
      <summary>Contains classes that are used by the <see cref="T:System.Windows.Markup.Primitives.MarkupWriter" /> for serialization of Extensible Application Markup Language (XAML).</summary>
    </member>
    <member name="N:System.Windows.Media.TextFormatting">
      <summary>Provides types that control formatting of text, typically at a lower level than control-based text presentation or the text object model.</summary>
    </member>
    <member name="N:System.Windows.Navigation">
      <summary>Provides types that support navigation, including navigating between windows and navigation journaling.</summary>
    </member>
    <member name="N:System.Windows.Resources">
      <summary>Provides low-level classes that support the Windows Presentation Foundation (WPF) resource model and the Resources build action.</summary>
    </member>
    <member name="N:System.Windows.Shapes">
      <summary>Provides access to a library of shapes that can be used in Extensible Application Markup Language (XAML) or code.</summary>
    </member>
    <member name="N:System.Windows.Shell">
      <summary>Contains types that provide managed code access to the enhanced functionality of the Windows 7 taskbar.</summary>
    </member>
    <member name="N:System.Windows.Threading">
      <summary>Contains types to support the Windows Presentation Foundation (WPF) threading system.</summary>
    </member>
    <member name="N:System.Windows.Xps">
      <summary>Provides classes that write XPS documents to a data store or print queue.</summary>
    </member>
    <member name="N:System.Windows.Xps.Packaging">
      <summary>Provides types that allow applications to read and write the components of an XPS document.</summary>
    </member>
    <member name="N:System.Windows.Xps.Serialization">
      <summary>Provides types that support serialization of XPS documents.</summary>
    </member>
    <member name="N:System.Workflow.Activities">
      <summary>Provides classes related to Windows Workflow Foundation activities.</summary>
    </member>
    <member name="N:System.Workflow.Activities.Configuration">
      <summary>Provides classes that represent sections of the configuration file.</summary>
    </member>
    <member name="N:System.Workflow.Activities.Rules">
      <summary>Contains a set of classes that define the conditions and actions that form a rule.</summary>
    </member>
    <member name="N:System.Workflow.Activities.Rules.Design">
      <summary>Contains a set of classes that manage the Rule Set Editor and the Rule Condition Editor dialog boxes.</summary>
    </member>
    <member name="N:System.Workflow.ComponentModel">
      <summary>Provides classes related to the Windows Workflow Foundation component model.</summary>
    </member>
    <member name="N:System.Workflow.ComponentModel.Compiler">
      <summary>Provides classes related to the Windows Workflow Foundation component model compiler.</summary>
    </member>
    <member name="N:System.Workflow.ComponentModel.Design">
      <summary>Provides classes related to the Windows Workflow Foundation component model designers.</summary>
    </member>
    <member name="N:System.Workflow.ComponentModel.Serialization">
      <summary>Provides the infrastructure for managing the serialization of activities and workflows to and from extensible Application Markup Language (XAML) and CodeDOM.</summary>
    </member>
    <member name="N:System.Workflow.Runtime">
      <summary>Provides classes related to workflow runtime.</summary>
    </member>
    <member name="N:System.Workflow.Runtime.Configuration">
      <summary>Contains classes that are used to configure the workflow runtime engine.</summary>
    </member>
    <member name="N:System.Workflow.Runtime.DebugEngine">
      <summary>Contains classes and interfaces for use in debugging workflow instances.</summary>
    </member>
    <member name="N:System.Workflow.Runtime.Hosting">
      <summary>Provides classes related to Windows Workflow Foundation runtime hosting.</summary>
    </member>
    <member name="N:System.Workflow.Runtime.Tracking">
      <summary>Contains classes and an interface related to tracking services.</summary>
    </member>
    <member name="N:System.Xaml">
      <summary>Provides types that relate to XAML readers and XAML writers. This includes the default implementations of .NET Framework XAML Services and its XAML readers and XAML writers. Also contains types relevant to the XAML type system and other support types related to XAML and .NET Framework XAML Services concepts. </summary>
    </member>
    <member name="N:System.Xaml.Permissions">
      <summary>Contains types that specify permissions for certain XAML loading scenarios, such as loading under partial trust or loading internal types.</summary>
    </member>
    <member name="N:System.Xaml.Schema">
      <summary>Contains types that support extensibility of the XAML type system.</summary>
    </member>
    <member name="N:System.Xml">
      <summary>The <see cref="N:System.Xml" /> namespace provides standards-based support for processing XML.</summary>
    </member>
    <member name="N:System.Xml.Linq">
      <summary>Contains the classes for LINQ to XML. LINQ to XML is an in-memory XML programming interface that enables you to modify XML documents efficiently and easily.</summary>
    </member>
    <member name="N:System.Xml.Resolvers">
      <summary>The <see cref="N:System.Xml.Resolvers" /> namespace provides support for prepopulating the cache with DTDs or XML streams. </summary>
    </member>
    <member name="N:System.Xml.Schema">
      <summary>The <see cref="N:System.Xml.Schema" /> namespace contains the XML classes that provide standards-based support for XML schema definition language (XSD) schemas.</summary>
    </member>
    <member name="N:System.Xml.Serialization">
      <summary>The <see cref="N:System.Xml.Serialization" /> namespace contains classes that are used to serialize objects into XML format documents or streams.</summary>
    </member>
    <member name="N:System.Xml.Serialization.Advanced">
      <summary>The System.Xml.Serialization.Advanced namespace contains classes that allow you to customize the code generated from a Web Services Description Language (WSDL) document.</summary>
    </member>
    <member name="N:System.Xml.Serialization.Configuration">
      <summary>The <see cref="N:System.Xml.Serialization.Configuration" /> namespace contains classes used to read the values found in application configuration files under the &lt;system.xml.serialization&gt; Element section. </summary>
    </member>
    <member name="N:System.Xml.XmlConfiguration">
      <summary>The <see cref="N:System.Xml.XmlConfiguration" /> namespace that contains sections of XML configuration.</summary>
    </member>
    <member name="N:System.Xml.XPath">
      <summary>The <see cref="N:System.Xml.XPath" /> namespace contains the classes that define a cursor model for navigating and editing XML information items as instances of the XQuery 1.0 and XPath 2.0 Data Model.</summary>
    </member>
    <member name="N:System.Xml.Xsl">
      <summary>The System.Xml.Xsl namespace provides support for Extensible Stylesheet Transformation (XSLT) transforms. It supports the W3C XSL Transformations (XSLT) Version 1.0 Recommendation (www.w3.org/TR/xslt).</summary>
    </member>
    <member name="N:System.Xml.Xsl.Runtime">
      <summary>Contains types that are used by the XSL transformation engine.</summary>
    </member>
    <member name="N:UIAutomationClientsideProviders">
      <summary>Contains a single type that maps client automation providers.</summary>
    </member>
    <member name="N:Windows.Foundation">
      <summary>The <see cref="N:Windows.Foundation" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI">
      <summary>The <see cref="N:Windows.UI" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml">
      <summary>The <see cref="N:Windows.UI.Xaml" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Automation">
      <summary>The <see cref="N:Windows.UI.Xaml.Automation" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Controls.Primitives">
      <summary>The <see cref="N:Windows.UI.Xaml.Controls.Primitives" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Markup">
      <summary>The <see cref="N:Windows.UI.Xaml.Markup" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Media">
      <summary>The <see cref="N:Windows.UI.Xaml.Media" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Media.Animation">
      <summary>The <see cref="N:Windows.UI.Xaml.Media.Animation" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:Windows.UI.Xaml.Media.Media3D">
      <summary>The <see cref="N:Windows.UI.Xaml.Media.Media3D" /> namespace contains types that support the development of Windows Store apps with the .NET Framework. </summary>
    </member>
    <member name="N:XamlGeneratedNamespace">
      <summary>Contains compiler-generated types that are not intended to be used directly from your code. </summary>
    </member>
  </members>
</doc>