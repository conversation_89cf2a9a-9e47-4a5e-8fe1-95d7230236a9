﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>Microsoft.Win32.Primitives</id>
    <version>4.3.0</version>
    <title>Microsoft.Win32.Primitives</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <licenseUrl>http://go.microsoft.com/fwlink/?LinkId=329770</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides common types for Win32-based libraries.

Commonly Used Types:
System.ComponentModel.Win32Exception
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.6" />
      <group targetFramework=".NETStandard1.3">
        <dependency id="Microsoft.NETCore.Platforms" version="1.1.0" />
        <dependency id="Microsoft.NETCore.Targets" version="1.1.0" />
        <dependency id="System.Runtime" version="4.3.0" />
      </group>
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="mscorlib" targetFramework=".NETFramework4.6" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.6" />
    </frameworkAssemblies>
  </metadata>
</package>