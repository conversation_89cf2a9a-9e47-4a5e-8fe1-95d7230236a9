﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>NLog</id>
    <version>5.4.0</version>
    <title>NLog for .NET Framework and .NET Standard</title>
    <authors><PERSON><PERSON><PERSON>,<PERSON>,<PERSON></authors>
    <license type="expression">BSD-3-Clause</license>
    <licenseUrl>https://licenses.nuget.org/BSD-3-Clause</licenseUrl>
    <icon>N.png</icon>
    <projectUrl>https://nlog-project.org/</projectUrl>
    <description>NLog is a logging platform for .NET with rich log routing and management capabilities.
NLog supports traditional logging, structured logging and the combination of both.

Supported platforms:

- .NET 5, 6, 7, 8 and 9
- .NET Core 1, 2 and 3
- .NET Standard 1.3+ and 2.0+
- .NET Framework 3.5 - 4.8
- Xamarin Android + iOS (.NET Standard)
- Mono 4

For ASP.NET Core, check: https://www.nuget.org/packages/NLog.Web.AspNetCore</description>
    <releaseNotes>ChangeLog:

- Fix AppDomain BaseDirectory on NET9 when it returns Long UNC (#5698) (@snakefoot)
- AsyncTaskTarget - Reduce default throttle after failure to 50ms instead of 500ms (#5700) (@snakefoot)
- ColoredConsoleTarget - Recognize NO_COLOR environment variable (#5702) (@snakefoot)
- More InternalLogger output when target disabled because failed to initialize (#5696) (@snakefoot)
- SimpleLayout - FromString avoid first chance exception when unknown layoutrenderer-type (#5681) (@snakefoot)
- ObjectGraphScanner - Handle property-getter that throws when AOT (#5651) (@snakefoot)
- LoggingConfigurationParser - Handle property-getter that throws when AOT (#5656) (@snakefoot)
- Remove Serializable-attribute and SerializationInfo since obsolete in NET8 (#5604) (@snakefoot)
- Marked JsonLayout EscapeForwardSlash as obsolete (#5695) (@snakefoot)
- Marked NLogViewerTarget and IncludeNLogData as obsolete (#5694) (@snakefoot)
- Marked XmlLoggingConfiguration AppConfig as obsolete (#5693) (@snakefoot)
- Marked XmlLoggingConfiguration InitializeSucceeded as obsolete (#5692) (@snakefoot)
- Marked LogEventInfo SetStackTrace with UserStackFrameNumber as obsolete (#5691) (@snakefoot)
- Marked SetupFromEnvironmentVariables and LogToTrace as obsolete (#5689) (@snakefoot)
- FileTarget - Introduced WriteToFile without concurrentWrites (#5687) (@snakefoot)
- TraceTarget - Fixed Header output (#5682) (@snakefoot)
- CounterLayoutRenderer - Lock on readonly field to fix Sonar Code Smell (#5680) (@snakefoot)
- LogAssemblyVersion should check InternalLogger IsInfoEnabled (#5630) (@snakefoot)
- MethodFactory - Only register methods from class-types (#5684) (@snakefoot)
- StringBuilderExt - Updated EqualTo for StringBuilder to foreach-loop (#5686) (@snakefoot)
- SimpleLayout - Refactor RenderAllRenderers to use foreach (#5611) (@snakefoot)
- JsonLayout - Optimize AppendStringEscape for success path (#5610) (@snakefoot)
- JsonLayout - Optimize ExcludeEmptyProperties to skip string cast (#5601) (@snakefoot)

NLog v5.2 changes how to load extensions: https://nlog-project.org/2023/05/30/nlog-5-2-trim-warnings.html

List of major changes in NLog 5.0: https://nlog-project.org/2022/05/16/nlog-5-0-finally-ready.html

Full changelog: https://github.com/NLog/NLog/blob/master/CHANGELOG.md

For all config options and platform support, check https://nlog-project.org/config/</releaseNotes>
    <copyright>Copyright (c) 2004-2025 NLog Project - https://nlog-project.org/</copyright>
    <tags>NLog logging log structured tracing logfiles database eventlog console email</tags>
    <repository type="git" url="https://github.com/NLog/NLog.git" commit="4d45d445601312040107cbe2ac00e56c4bf4a60b" />
    <dependencies>
      <group targetFramework=".NETFramework3.5" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.6" />
      <group targetFramework=".NETStandard1.3">
        <dependency id="NETStandard.Library" version="1.6.0" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.Primitives" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.TypeConverter" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Data.Common" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.StackTrace" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Net.Http" version="4.3.4" exclude="Build,Analyzers" />
        <dependency id="System.Net.NameResolution" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Net.Requests" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Reflection.TypeExtensions" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.RegularExpressions" version="4.3.1" exclude="Build,Analyzers" />
        <dependency id="System.Xml.XmlDocument" version="4.3.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard1.5">
        <dependency id="NETStandard.Library" version="1.6.0" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.Primitives" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.TypeConverter" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Data.Common" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.Process" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.StackTrace" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Diagnostics.TraceSource" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.IO.FileSystem.Watcher" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Net.Http" version="4.3.4" exclude="Build,Analyzers" />
        <dependency id="System.Net.NameResolution" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Net.Requests" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Reflection.TypeExtensions" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.Loader" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Text.RegularExpressions" version="4.3.1" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Thread" version="4.3.0" exclude="Build,Analyzers" />
        <dependency id="System.Xml.XmlDocument" version="4.3.0" exclude="Build,Analyzers" />
      </group>
      <group targetFramework=".NETStandard2.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System.Configuration" targetFramework=".NETFramework3.5, .NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework3.5, .NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework3.5, .NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System.Xml" targetFramework=".NETFramework3.5, .NETFramework4.5, .NETFramework4.6" />
      <frameworkAssembly assemblyName="System.IO.Compression" targetFramework=".NETFramework4.5, .NETFramework4.6" />
    </frameworkAssemblies>
  </metadata>
</package>