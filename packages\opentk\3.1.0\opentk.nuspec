﻿<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<package xmlns="http://schemas.microsoft.com/packaging/2011/10/nuspec.xsd">
  <metadata>
    <id>OpenTK</id>
    <version>3.1.0</version>
    <title>OpenTK</title>
    <authors>The Open Toolkit Team</authors>
    <owners>opentk thefiddler</owners>
    <licenseUrl>http://github.com/opentk/opentk/blob/master/License.txt</licenseUrl>
    <projectUrl>http://www.opentk.com</projectUrl>
    <iconUrl>https://raw.githubusercontent.com/opentk/opentk.net/docfx/assets/opentk.png</iconUrl>
    <description>The Open Toolkit is set of fast, low-level C# bindings for OpenGL, OpenGL ES and OpenAL. It runs on all major platforms and powers hundreds of apps, games and scientific research.
OpenTK provides several utility libraries, including a math/linear algebra package, a windowing system, and input handling.</description>
    <summary>A set of fast, low-level C# bindings for OpenGL, OpenGL ES and OpenAL.</summary>
    <releaseNotes>	General:
	* Fixed problem where OpenGL 3.2 contexts where created using the wrong flags, causing renderdoc error (#790).
	* Fixed problem where Vector3.Transform(Quaternion) returned incorrect results ( #776 ).
	* SDL backend no longer handles exceptions thrown in input events ( #735 #737 )
	* Updated gamepadd mappings for SDL (#927).
	* Alt + numpad now results in correct data being passed to OnKeyPress (f17fa4b).
	Windows:
	* Keypress events are using CharSet.Unicode to allow for UTF characters.
	* If GetMouseMovePointsEx returns "access denied" we fall back to the old mouse move handling ( #883 ).
	* Detect joysticks which declare no valid controls &amp;amp; ignore (Cherry-picked from #819 ).
	* Fix where an invalid joystick axis overwrites the first joystick axis. (Cherry-picked from #819 ).
	* Makes the ArbCreateContext return valid settings ( #754 ).
	MacOS:
	* Unprocessed events no longer put the application into a partially-activated state ( #732 ).
	* Multiple fixes under PR #914:
	* NativeWindow.CursorVisible no longer resets the mouse position ( #668 ).
	* Custom cursors should work now ( e598ab2 ).
	* Command key is now a valid modifier ( 286119e ).
	* Fix where KeyDown events would trigger KeyPress when they shouldn't, e.g. when pasting ( ea3dd48 ).
	* KeyPress now receives the correct keycodes even if modifiers where held when typing ( 91b03dd ).
	Internal:
	* Generator.Build now uses invariant culture for parsing avoiding an exception when the local system uses comma as decimal separator ( #750 ).
	* Removed weird assembly version check fixing #710.
	* Fixed Xamarin project so that it compiles ( See #725 and b16e7fa ).
	* Matrix4 uses unsafe to invert for performace ( #719 ).
	* Removed link to gitter chat, discord is where it's at ( #770 ).</releaseNotes>
    <copyright>Copyright (c) 2006 - 2019 Stefanos Apostolopoulos &lt;<EMAIL>&gt; for the Open Toolkit library.</copyright>
    <tags>OpenTK OpenGL OpenAL OpenCL C# F# VB .Net Mono Graphics Game Scientific Science 3D 2D Math Input Gamepad Joystick</tags>
  </metadata>
</package>