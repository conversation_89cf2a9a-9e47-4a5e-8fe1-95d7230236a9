﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>org.matheval</id>
    <version>*******</version>
    <title>Matheval</title>
    <authors>matheval.org</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <projectUrl>https://matheval.org/</projectUrl>
    <description>Matheval is a mathematical expressions evaluator library for .NET. Allows to evaluate mathematical, boolean, string and datetime expressions on the fly. Official document and usage examples: https://matheval.org/math-expression-eval-for-c-sharp/</description>
    <copyright>Copyright © MathEval.org 2021</copyright>
    <tags>Math Mathematics Mathematical Expression Fomular Eval Evaluator Calculator Solve Unary Decimal</tags>
    <repository type="git" url="https://github.com/matheval/expression-evaluator-c-sharp/" />
    <dependencies>
      <group targetFramework=".NETFramework3.5" />
      <group targetFramework=".NETFramework4.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETFramework4.6" />
      <group targetFramework=".NETFramework4.6.1" />
      <group targetFramework=".NETFramework4.6.2" />
      <group targetFramework=".NETFramework4.7" />
      <group targetFramework=".NETFramework4.7.1" />
      <group targetFramework=".NETFramework4.7.2" />
      <group targetFramework=".NETFramework4.8" />
      <group targetFramework=".NETCoreApp2.1" />
      <group targetFramework=".NETCoreApp3.1" />
      <group targetFramework=".NETStandard2.0" />
    </dependencies>
  </metadata>
</package>