﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>OxyPlot.Core</id>
    <version>2.2.0</version>
    <authors>OxyPlot</authors>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>OxyPlot_128.png</icon>
    <readme>README.md</readme>
    <projectUrl>https://oxyplot.github.io/</projectUrl>
    <description>OxyPlot is a plotting library for .NET. This is the core library including the foundation classes and plot model. To display the plots, you also need to add a platform-specific OxyPlot package that contains a PlotView component.</description>
    <copyright>Copyright (c) 2014-2022 OxyPlot Contributors</copyright>
    <tags>plotting plot charting chart</tags>
    <repository type="git" url="https://github.com/oxyplot/oxyplot.git" commit="74d1600e66199bbf8630c79929e1d0fa46e4101d" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2">
        <dependency id="Microsoft.NETFramework.ReferenceAssemblies" version="1.0.2" exclude="Build,Analyzers" />
      </group>
      <group targetFramework="net6.0" />
      <group targetFramework="net8.0" />
      <group targetFramework=".NETStandard2.0" />
    </dependencies>
  </metadata>
</package>