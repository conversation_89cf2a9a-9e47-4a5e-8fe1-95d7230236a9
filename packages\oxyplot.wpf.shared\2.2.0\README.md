OxyPlot is a cross-platform plotting library for .NET

![Plot](https://oxyplot.github.io/public/images/normal-distributions.png)


#### Getting started

1. Use the NuGet package manager to add a reference to OxyPlot (see details below if you want to use pre-release packages)
2. Add a `PlotView` to your user interface
3. Create a `PlotModel` in your code
4. Bind the `PlotModel` to the `Model` property of your `PlotView`


#### Examples

You can find examples in the `/Source/Examples` folder in the code repository.


#### NuGet packages

The latest pre-release packages are pushed to [myget.org](https://www.myget.org/). To install these packages, set the myget.org package source `https://www.myget.org/F/oxyplot` and remember the "-pre" flag.

The stable release packages will be pushed to [nuget.org](https://www.nuget.org/packages?q=oxyplot). Note that we have  have a lot of old (v2015.\*) and pre-release packages on this feed, and sometimes these show up even if they are unlisted.

See the [wiki](https://github.com/oxyplot/oxyplot/wiki/NuGet-packages) for information about the available packages.


#### More information

- [Web page](https://oxyplot.github.io)
- [Documentation](https://oxyplot.readthedocs.io/en/latest/)
- [NuGet packages](https://www.nuget.org/packages?q=oxyplot)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/oxyplot)
- [Twitter](https://twitter.com/hashtag/oxyplot)
- [Gitter](https://gitter.im/oxyplot/oxyplot) (chat)


#### Contribute

See [Contributing](.github/CONTRIBUTING.md) for information about how to contribute!
