<?xml version="1.0"?>
<doc>
    <assembly>
        <name>OxyPlot.Wpf.Shared</name>
    </assembly>
    <members>
        <member name="T:OxyPlot.Wpf.OxyColorConverter">
            <summary>
            Converts between <see cref="T:OxyPlot.OxyColor" /> and <see cref="T:System.Windows.Media.Color" />.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.OxyColorConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns <c>null</c>, the valid <c>null</c> value is used.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.OxyColorConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns <c>null</c>, the valid <c>null</c> value is used.</returns>
        </member>
        <member name="T:OxyPlot.Wpf.ThicknessConverter">
            <summary>
            Converts from <see cref="T:System.Windows.Thickness" /> to the maximum thicknesses.
            </summary>
            <remarks>This is used in the <see cref="T:OxyPlot.Wpf.TrackerControl" /> to convert BorderThickness properties to Path.StrokeThickness (double).
            The maximum thickness value is used.</remarks>
        </member>
        <member name="M:OxyPlot.Wpf.ThicknessConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns <c>null</c>, the valid <c>null</c> value is used.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ThicknessConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns <c>null</c>, the valid <c>null</c> value is used.</returns>
        </member>
        <member name="T:OxyPlot.Wpf.ExporterExtensions">
            <summary>
            Provides extension methods for exporters.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.ExporterExtensions.ExportToFile(OxyPlot.IExporter,OxyPlot.IPlotModel,System.String)">
            <summary>
            Exports the specified <see cref="T:OxyPlot.PlotModel" /> to a file.
            </summary>
            <param name="exporter">The exporter.</param>
            <param name="model">The model to export.</param>
            <param name="path">The path to the file.</param>
        </member>
        <member name="T:OxyPlot.Wpf.MoreColors">
            <summary>
            Defines additional <see cref="T:System.Windows.Media.Colors" />.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.MoreColors.Undefined">
            <summary>
            The undefined color.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.MoreColors.Automatic">
            <summary>
            The automatic color.
            </summary>
        </member>
        <member name="T:OxyPlot.Wpf.PlotCommands">
            <summary>
            Provides a standard set of commands for the <see cref="T:OxyPlot.Wpf.PlotViewBase" /> control.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotCommands.ResetAxes">
            <summary>
            Gets the value that represents the "Reset all axes" command.
            </summary>
        </member>
        <member name="T:OxyPlot.Wpf.PlotViewBase">
            <summary>
            Base class for WPF PlotView implementations.
            </summary>
            <summary>
            Base class for WPF PlotView implementations.
            </summary>
            <summary>
            Base class for WPF PlotView implementations.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.PartGrid">
            <summary>
            The Grid PART constant.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.grid">
            <summary>
            The grid.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.plotPresenter">
            <summary>
            The plot presenter.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.renderContext">
            <summary>
            The render context
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.modelLock">
            <summary>
            The model lock.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.currentTracker">
            <summary>
            The current tracker.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.currentTrackerTemplate">
            <summary>
            The current tracker template.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.defaultController">
            <summary>
            The default plot controller.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.isInVisualTree">
            <summary>
            Indicates whether the <see cref="T:OxyPlot.Wpf.PlotViewBase"/> was in the visual tree the last time <see cref="M:OxyPlot.Wpf.PlotViewBase.Render"/> was called.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.mouseDownPoint">
            <summary>
            The mouse down point.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.overlays">
            <summary>
            The overlays.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.zoomControl">
            <summary>
            The zoom control.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.#cctor">
            <summary>
            Initializes static members of the <see cref="T:OxyPlot.Wpf.PlotViewBase" /> class.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:OxyPlot.Wpf.PlotViewBase" /> class.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ActualController">
            <summary>
            Gets the actual PlotView controller.
            </summary>
            <value>The actual PlotView controller.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.OxyPlot#IView#ActualController">
            <inheritdoc/>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ActualModel">
            <summary>
            Gets the actual model.
            </summary>
            <value>The actual model.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.OxyPlot#IView#ActualModel">
            <inheritdoc/>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ClientArea">
            <summary>
            Gets the coordinates of the client area of the view.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.TrackerDefinitions">
            <summary>
            Gets the tracker definitions.
            </summary>
            <value>The tracker definitions.</value>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.HideTracker">
            <summary>
            Hides the tracker.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.HideZoomRectangle">
            <summary>
            Hides the zoom rectangle.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.InvalidatePlot(System.Boolean)">
            <summary>
            Invalidate the PlotView (not blocking the UI thread)
            </summary>
            <param name="updateData">The update Data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnApplyTemplate">
            <inheritdoc/>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.PanAllAxes(System.Windows.Vector)">
            <summary>
            Pans all axes.
            </summary>
            <param name="delta">The delta.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ResetAllAxes">
            <summary>
            Resets all axes.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.SetClipboardText(System.String)">
            <summary>
            Stores text on the clipboard.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.SetCursorType(OxyPlot.CursorType)">
            <summary>
            Sets the cursor type.
            </summary>
            <param name="cursorType">The cursor type.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ShowTracker(OxyPlot.TrackerHitResult)">
            <summary>
            Shows the tracker.
            </summary>
            <param name="trackerHitResult">The tracker data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ShowZoomRectangle(OxyPlot.OxyRect)">
            <summary>
            Shows the zoom rectangle.
            </summary>
            <param name="r">The rectangle.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ZoomAllAxes(System.Double)">
            <summary>
            Zooms all axes.
            </summary>
            <param name="factor">The zoom factor.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ClearBackground">
            <summary>
            Clears the background of the plot presenter.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.CreatePlotPresenter">
            <summary>
            Creates the plot presenter.
            </summary>
            <returns>The plot presenter.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.CreateRenderContext">
            <summary>
            Creates the render context.
            </summary>
            <returns>The render context.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnModelChanged">
            <summary>
            Called when the model is changed.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.Render">
            <summary>
            Renders the plot model to the plot presenter.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.RenderOverride">
            <summary>
            Renders the plot model to the plot presenter.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.UpdateDpi">
            <summary>
            Updates the DPI scale of the render context.
            </summary>
            <returns>The DPI scale.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.ModelChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Called when the model is changed.
            </summary>
            <param name="d">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.DependencyPropertyChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.BeginInvoke(System.Action)">
            <summary>
            Invokes the specified action on the dispatcher, if necessary.
            </summary>
            <param name="action">The action.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.IsInVisualTree">
            <summary>
            Gets a value indicating whether the <see cref="T:OxyPlot.Wpf.PlotViewBase"/> is connected to the visual tree.
            </summary>
            <returns><c>true</c> if the PlotViewBase is connected to the visual tree; <c>false</c> otherwise.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnLayoutUpdated(System.Object,System.EventArgs)">
            <summary>
            This event fires every time Layout updates the layout of the trees associated with current Dispatcher.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The event args.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.KeyDown" /> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnManipulationStarted(System.Windows.Input.ManipulationStartedEventArgs)">
            <summary>
            Called when the <see cref="E:System.Windows.UIElement.ManipulationStarted" /> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnManipulationDelta(System.Windows.Input.ManipulationDeltaEventArgs)">
            <summary>
            Called when the <see cref="E:System.Windows.UIElement.ManipulationDelta" /> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnManipulationCompleted(System.Windows.Input.ManipulationCompletedEventArgs)">
            <summary>
            Called when the <see cref="E:System.Windows.UIElement.ManipulationCompleted" /> event occurs.
            </summary>
            <param name="e">The data for the event.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseWheel(System.Windows.Input.MouseWheelEventArgs)">
            <summary>
            Called before the <see cref="E:System.Windows.UIElement.MouseWheel" /> event occurs to provide handling for the event in a derived class without attaching a delegate.
            </summary>
            <param name="e">A <see cref="T:System.Windows.Input.MouseWheelEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseDown(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled MouseDown attached event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. This event data reports details about the mouse button that was pressed and the handled state.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseMove(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled MouseMove attached event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseUp(System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Invoked when an unhandled MouseUp routed event reaches an element in its route that is derived from this class. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> that contains the event data. The event data reports that the mouse button was released.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseEnter(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseEnter" /> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs" /> that contains the event data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.PlotViewBase.OnMouseLeave(System.Windows.Input.MouseEventArgs)">
            <summary>
            Invoked when an unhandled <see cref="E:System.Windows.Input.Mouse.MouseLeave" /> attached event is raised on this element. Implement this method to add class handling for this event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs" /> that contains the event data.</param>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ControllerProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.Controller"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.DefaultTrackerTemplateProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.DefaultTrackerTemplate"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.IsMouseWheelEnabledProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.IsMouseWheelEnabled"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ModelProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.Model"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.PanCursorProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.PanCursor"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ZoomHorizontalCursorProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.ZoomHorizontalCursor"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ZoomRectangleCursorProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.ZoomRectangleCursor"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ZoomRectangleTemplateProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.ZoomRectangleTemplate"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.PlotViewBase.ZoomVerticalCursorProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.PlotViewBase.ZoomVerticalCursor"/> dependency property.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.Controller">
            <summary>
            Gets or sets the Plot controller.
            </summary>
            <value>The Plot controller.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.DefaultTrackerTemplate">
            <summary>
            Gets or sets the default tracker template.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.IsMouseWheelEnabled">
            <summary>
            Gets or sets a value indicating whether IsMouseWheelEnabled.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.Model">
            <summary>
            Gets or sets the model.
            </summary>
            <value>The model.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.PanCursor">
            <summary>
            Gets or sets the pan cursor.
            </summary>
            <value>The pan cursor.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ZoomHorizontalCursor">
            <summary>
            Gets or sets the horizontal zoom cursor.
            </summary>
            <value>The zoom horizontal cursor.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ZoomRectangleCursor">
            <summary>
            Gets or sets the rectangle zoom cursor.
            </summary>
            <value>The zoom rectangle cursor.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ZoomRectangleTemplate">
            <summary>
            Gets or sets the zoom rectangle template.
            </summary>
            <value>The zoom rectangle template.</value>
        </member>
        <member name="P:OxyPlot.Wpf.PlotViewBase.ZoomVerticalCursor">
            <summary>
            Gets or sets the vertical zoom cursor.
            </summary>
            <value>The zoom vertical cursor.</value>
        </member>
        <member name="T:OxyPlot.Wpf.TrackerControl">
            <summary>
            The tracker control.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.HorizontalLineVisibilityProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.HorizontalLineVisibility"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.VerticalLineVisibilityProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.VerticalLineVisibility"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.LineThicknessProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.LineThickness"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.LineStrokeProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.LineStroke"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.LineExtentsProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.LineExtents"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.LineDashArrayProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.LineDashArray"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.BorderEdgeModeProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.BorderEdgeMode"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.ShowPointerProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.ShowPointer"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.CornerRadiusProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.CornerRadius"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.DistanceProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.Distance"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.CanCenterHorizontallyProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.CanCenterHorizontally"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.CanCenterVerticallyProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.CanCenterVertically"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PositionProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerControl.Position"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PartPath">
            <summary>
            The path part string.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PartContent">
            <summary>
            The content part string.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PartContentcontainer">
            <summary>
            The content container part string.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PartHorizontalline">
            <summary>
            The horizontal line part string.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.PartVerticalline">
            <summary>
            The vertical line part string.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.content">
            <summary>
            The content.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.horizontalLine">
            <summary>
            The horizontal line.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.path">
            <summary>
            The path.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.contentContainer">
            <summary>
            The content container.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerControl.verticalLine">
            <summary>
            The vertical line.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.#cctor">
            <summary>
            Initializes static members of the <see cref = "T:OxyPlot.Wpf.TrackerControl" /> class.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.BorderEdgeMode">
            <summary>
            Gets or sets BorderEdgeMode.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.HorizontalLineVisibility">
            <summary>
            Gets or sets HorizontalLineVisibility.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.VerticalLineVisibility">
            <summary>
            Gets or sets VerticalLineVisibility.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.LineThickness">
            <summary>
            Gets or sets LineThickness.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.LineStroke">
            <summary>
            Gets or sets LineStroke.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.LineExtents">
            <summary>
            Gets or sets LineExtents.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.LineDashArray">
            <summary>
            Gets or sets LineDashArray.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.ShowPointer">
            <summary>
            Gets or sets a value indicating whether to show a 'pointer' on the border.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.CornerRadius">
            <summary>
            Gets or sets the corner radius (only used when ShowPoint=<c>false</c>).
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.Distance">
            <summary>
            Gets or sets the distance of the content container from the trackers Position.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.CanCenterHorizontally">
            <summary>
            Gets or sets a value indicating whether the tracker can center its content box horizontally.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.CanCenterVertically">
            <summary>
            Gets or sets a value indicating whether the tracker can center its content box vertically.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerControl.Position">
            <summary>
            Gets or sets Position of the tracker.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.OnApplyTemplate">
            <summary>
            When overridden in a derived class, is invoked whenever application code or internal processes call <see cref="M:System.Windows.FrameworkElement.ApplyTemplate" />.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.PositionChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Called when the position is changed.
            </summary>
            <param name="sender">The sender.</param>
            <param name="e">The <see cref="T:System.Windows.DependencyPropertyChangedEventArgs" /> instance containing the event data.</param>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.OnPositionChanged(System.Windows.DependencyPropertyChangedEventArgs)">
            <summary>
            Called when the position is changed.
            </summary>
            <param name="dependencyPropertyChangedEventArgs">The dependency property changed event args.</param>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.UpdatePositionAndBorder">
            <summary>
            Update the position and border of the tracker.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.CreateBorderGeometry(System.Windows.HorizontalAlignment,System.Windows.VerticalAlignment,System.Double,System.Double,System.Windows.Thickness@)">
            <summary>
            Create the border geometry.
            </summary>
            <param name="ha">The horizontal alignment.</param>
            <param name="va">The vertical alignment.</param>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
            <param name="margin">The margin.</param>
            <returns>The border geometry.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.TrackerControl.CreatePointerBorderGeometry(System.Windows.HorizontalAlignment,System.Windows.VerticalAlignment,System.Double,System.Double,System.Windows.Thickness@)">
            <summary>
            Create a border geometry with a 'pointer'.
            </summary>
            <param name="ha">The horizontal alignment.</param>
            <param name="va">The vertical alignment.</param>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
            <param name="margin">The margin.</param>
            <returns>The border geometry.</returns>
        </member>
        <member name="T:OxyPlot.Wpf.TrackerDefinition">
            <summary>
            Represents a tracker definition.
            </summary>
            <remarks>The tracker definitions make it possible to show different trackers for different series.
            The <see cref="P:OxyPlot.Series.Series.TrackerKey" /> property is matched with the <see cref="P:OxyPlot.Wpf.TrackerDefinition.TrackerKey" />
            in the TrackerDefinitions collection in the <see cref="T:OxyPlot.Wpf.PlotViewBase" /> control.</remarks>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerDefinition.TrackerKeyProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerDefinition.TrackerKey"/> dependency property.
            </summary>
        </member>
        <member name="F:OxyPlot.Wpf.TrackerDefinition.TrackerTemplateProperty">
            <summary>
            Identifies the <see cref="P:OxyPlot.Wpf.TrackerDefinition.TrackerTemplate"/> dependency property.
            </summary>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerDefinition.TrackerKey">
            <summary>
            Gets or sets the tracker key.
            </summary>
            <remarks>The Plot will use this property to find the TrackerDefinition that matches the TrackerKey of the current series.</remarks>
        </member>
        <member name="P:OxyPlot.Wpf.TrackerDefinition.TrackerTemplate">
            <summary>
            Gets or sets the tracker template.
            </summary>
            <remarks>The tracker control will be added/removed from the Tracker overlay as necessary.
            The DataContext of the tracker will be set to a TrackerHitResult with the current tracker data.</remarks>
        </member>
        <member name="T:OxyPlot.Wpf.ConverterExtensions">
            <summary>
            Extension method used to convert to/from Windows/Windows.Media classes.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.DistanceTo(System.Windows.Point,System.Windows.Point)">
            <summary>
            Calculate the distance between two points.
            </summary>
            <param name="p1">The first point.</param>
            <param name="p2">The second point.</param>
            <returns>The distance.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToBrush(OxyPlot.OxyColor)">
            <summary>
            Converts an <see cref="T:OxyPlot.OxyColor" /> to a <see cref="T:System.Windows.Media.Brush" />.
            </summary>
            <param name="c">The color.</param>
            <returns>A <see cref="T:System.Windows.Media.SolidColorBrush" />.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToColor(OxyPlot.OxyColor)">
            <summary>
            Converts an <see cref="T:OxyPlot.OxyColor" /> to a <see cref="T:System.Windows.Media.Color" />.
            </summary>
            <param name="c">The color.</param>
            <returns>A Color.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToThickness(OxyPlot.OxyThickness)">
            <summary>
            Converts an OxyThickness to a Thickness.
            </summary>
            <param name="c">The thickness.</param>
            <returns>A <see cref="T:System.Windows.Thickness" /> instance.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToVector(OxyPlot.ScreenVector)">
            <summary>
            Converts a ScreenVector to a Vector.
            </summary>
            <param name="c">The c.</param>
            <returns>A <see cref="T:System.Windows.Vector" /> instance.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToHorizontalAlignment(System.Windows.HorizontalAlignment)">
            <summary>
            Converts a HorizontalAlignment to a HorizontalAlignment.
            </summary>
            <param name="alignment">The alignment.</param>
            <returns>A HorizontalAlignment.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToVerticalAlignment(System.Windows.VerticalAlignment)">
            <summary>
            Converts a HorizontalAlignment to a VerticalAlignment.
            </summary>
            <param name="alignment">The alignment.</param>
            <returns>A VerticalAlignment.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToOxyColor(System.Windows.Media.Color)">
            <summary>
            Converts a Color to an OxyColor.
            </summary>
            <param name="color">The color.</param>
            <returns>An OxyColor.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToOxyColor(System.Windows.Media.Brush)">
            <summary>
            Converts a <see cref="T:System.Windows.Media.Brush" /> to an <see cref="T:OxyPlot.OxyColor" />.
            </summary>
            <param name="brush">The brush.</param>
            <returns>An <see cref="T:OxyPlot.OxyColor" />.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToOxyThickness(System.Windows.Thickness)">
            <summary>
            Converts a Thickness to an <see cref="T:OxyPlot.OxyThickness" />.
            </summary>
            <param name="t">The thickness.</param>
            <returns>An <see cref="T:OxyPlot.OxyThickness" />.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToScreenPoint(System.Windows.Point)">
            <summary>
            Converts a <see cref="T:System.Windows.Point" /> to a <see cref="T:OxyPlot.ScreenPoint" />.
            </summary>
            <param name="pt">The point.</param>
            <returns>A <see cref="T:OxyPlot.ScreenPoint" />.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToScreenPointArray(System.Windows.Point[])">
            <summary>
            Converts a Point array to a ScreenPoint array.
            </summary>
            <param name="points">The points.</param>
            <returns>A ScreenPoint array.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToScreenVector(System.Windows.Vector)">
            <summary>
            Converts the specified vector to a ScreenVector.
            </summary>
            <param name="vector">The vector.</param>
            <returns>A <see cref="T:OxyPlot.ScreenVector" />.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.Convert(System.Windows.Input.Key)">
            <summary>
            Converts the specified key.
            </summary>
            <param name="k">The key to convert.</param>
            <returns>The converted key.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.Convert(System.Windows.Input.MouseButton)">
            <summary>
            Converts the specified button.
            </summary>
            <param name="button">The button to convert.</param>
            <returns>The converted mouse button.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToMouseWheelEventArgs(System.Windows.Input.MouseWheelEventArgs,System.Windows.IInputElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.MouseWheelEventArgs" /> to <see cref="T:OxyPlot.OxyMouseWheelEventArgs" /> for a mouse wheel event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseWheelEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.IInputElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseWheelEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToMouseDownEventArgs(System.Windows.Input.MouseButtonEventArgs,System.Windows.IInputElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a mouse down event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.IInputElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToMouseReleasedEventArgs(System.Windows.Input.MouseButtonEventArgs,System.Windows.IInputElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a mouse up event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseButtonEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.IInputElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToMouseEventArgs(System.Windows.Input.MouseEventArgs,System.Windows.IInputElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.MouseEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a mouse event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.MouseEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.IInputElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToTouchEventArgs(System.Windows.Input.ManipulationStartedEventArgs,System.Windows.UIElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.ManipulationStartedEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a touch started event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.ManipulationStartedEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.UIElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToTouchEventArgs(System.Windows.Input.ManipulationDeltaEventArgs,System.Windows.UIElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.ManipulationDeltaEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a touch delta event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.ManipulationDeltaEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.UIElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="M:OxyPlot.Wpf.ConverterExtensions.ToTouchEventArgs(System.Windows.Input.ManipulationCompletedEventArgs,System.Windows.UIElement)">
            <summary>
            Converts <see cref="T:System.Windows.Input.ManipulationCompletedEventArgs" /> to <see cref="T:OxyPlot.OxyMouseEventArgs" /> for a touch completed event.
            </summary>
            <param name="e">The <see cref="T:System.Windows.Input.ManipulationCompletedEventArgs" /> instance containing the event data.</param>
            <param name="relativeTo">The <see cref="T:System.Windows.UIElement" /> that the event is relative to.</param>
            <returns>A <see cref="T:OxyPlot.OxyMouseEventArgs" /> containing the converted event arguments.</returns>
        </member>
        <member name="T:OxyPlot.Wpf.Keyboard">
            <summary>
            Provides utility methods related to the keyboard.
            </summary>
        </member>
        <member name="M:OxyPlot.Wpf.Keyboard.GetModifierKeys">
            <summary>
            Gets the current modifier keys.
            </summary>
            <returns>A <see cref="T:OxyPlot.OxyModifierKeys" /> value.</returns>
        </member>
    </members>
</doc>
