<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Content Include="$(MSBuildThisFileDirectory)GenIStreamDotNetInterop.dll">
      <Link>GenIStreamDotNetInterop.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
    <Content Include="$(MSBuildThisFileDirectory)XmlParser_MD_VC141_v3_4.dll">
      <Link>XmlParser_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)NodeMapData_MD_VC141_v3_4.dll">
      <Link>NodeMapData_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)MathParser_MD_VC141_v3_4.dll">
      <Link>MathParser_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)Log_MD_VC141_v3_4.dll">
      <Link>Log_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)log4cpp_MD_VC141_v3_4.dll">
      <Link>log4cpp_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)GenApi_MD_VC141_v3_4.dll">
      <Link>GenApi_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
	<Content Include="$(MSBuildThisFileDirectory)GCBase_MD_VC141_v3_4.dll">
      <Link>GCBase_MD_VC141_v3_4.dll</Link>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Visible>False</Visible>
    </Content>
  </ItemGroup>
</Project>
