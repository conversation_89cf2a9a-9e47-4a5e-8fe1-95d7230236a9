﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>Sick.GenIStreamDotNet</id>
    <version>4.2.0.20192</version>
    <title>Sick.GenIStreamDotNet</title>
    <authors>SICK</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>The GenIStream API provides an interface towards Ranger3 and Ruler3000 cameras. 
	 
	This package includes GenIStream dependencies.</description>
    <copyright>Copyright © SICK AG 2025</copyright>
    <dependencies>
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETFramework4.5" />
    </dependencies>
  </metadata>
</package>