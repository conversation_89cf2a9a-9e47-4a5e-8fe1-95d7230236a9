﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Sick.Stream.Algorithms.DotNet</id>
    <version>********</version>
    <title>Sick.Stream.Algorithms.DotNet</title>
    <authors>SICK</authors>
    <requireLicenseAcceptance>false</requireLicenseAcceptance>
    <description>Stream Editor data types and tools</description>
    <copyright>Copyright © SICK AG 2025</copyright>
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Sick.Stream.Common" version="1.2.1" exclude="Build,Analyzers" />
        <dependency id="Sick.GenIStreamDotNet" version="4.2.0.20192" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>