<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sick.Stream.Common</name>
    </assembly>
    <members>
        <member name="M:Sick.Stream.Common.FunctionalMaybeExtensions.OrFailure``1(Functional.Maybe.Maybe{``0})">
            <summary>
            Unsafe (!) method for getting potential value
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.FunctionalMaybeExtensions.OrFailure``1(Functional.Maybe.Maybe{``0},System.String)">
            <summary>
            Unsafe (!) method for getting potential value
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.CreateDisplayName(System.String)">
            <summary>
            Creates a display name from a property or class name.
            Replace all capital letters excepts the first and add a space.
            Returns empty string if name is null.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.CreateDisplayNameFromUpperCaseEnum(System.Enum)">
            <summary>
            Create a display name from an upper case style enum.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.GetVectorDisplayValue(Sick.Stream.Common.Vector2D)">
            <summary>
            Get a display value from a Vector2D.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.GetVectorDisplayValue(Sick.Stream.Common.Vector3D)">
            <summary>
            Get a display value from a Vector3D.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.GetDecimalDisplayValue(System.Double,System.Boolean)">
            <summary>
            Get a display value from a double.
            </summary>
            <param name="value">The double value.</param>
            <param name="trimZeros">If true, any zeros at the end of the string will be removed.</param>
            <returns>Returns the double value represented as a string.</returns>
        </member>
        <member name="M:Sick.Stream.Common.MessageUtils.GetRotationDisplayValue(System.Double,System.Boolean)">
            <summary>
            Get a display value from an angle in radians.
            </summary>
            <param name="rad">The angle in radians.</param>
            <param name="trimZeros">If true, any zeros at the end of the string will be removed.</param>
            <returns>Returns the angle value in degrees, represented as a string.</returns>
        </member>
        <member name="M:Sick.Stream.Common.ObservableObject.SetProperty``1(``0@,``0,System.String,System.Boolean)">
            <summary>
            Sets the property to the provided value if different. <see cref="E:Sick.Stream.Common.ObservableObject.PropertyChanged"/>
            will only be notified if the value was different.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="field">A reference to the backing field containing the property value</param>
            <param name="value">The value to set</param>
            <param name="propertyName">The name of the property</param>
            <param name="forceUpdate">notify even when value equals old value.
            Call as named parameter to specify forced update without affecting other default arguments.</param>
            <returns>True if the property was changed, false otherwise</returns>
        </member>
        <member name="T:Sick.Stream.Common.StreamEditorException">
            <summary>
            Exception class used by the Stream Editor.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.StreamEditorException.#ctor(System.String)">
            <summary>
            Creates an exception with a message.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="T:Sick.Stream.Common.StreamEditorGrabException">
            <summary>
            Exception class used by the GrabFromCamera tool.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.StreamEditorGrabException.#ctor(System.String)">
            <summary>
            Creates an exception with a message.
            </summary>
            <param name="message">Log message.</param>
        </member>
        <member name="T:Sick.Stream.Common.VariableType">
            <summary>
            Variable types.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.ToolCategory">
            <summary>
            Tool categories.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.LogLevel">
            <summary>
            Log levels.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.ToolLevel">
            <summary>
            Tool levels.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.FeedbackImageFileFormat">
            <summary>
            Image file formats.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.TextFileFormat">
            <summary>
            Text file formats.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.FileWriteMode">
            <summary>
            File write modes.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.ArgumentCategory">
            <summary>
            Argument categories.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Localization.EnumLocalizer">
             <summary>
             Class that is used to read enum localization data.
            
             An enum is localizable when
             * marked with the <see cref="T:Sick.Stream.Common.Localization.LocalizableEnumAttribute"/>,
             * corresponding localized enum strings exist in the projects EnumResources.resx file.
            
             The enum localization can be used by WPF/XAML either using
             <see cref="!:Sick.Stream.Controls.Localization.EnumItemsSource.EnumType"/>, <see cref="!:Sick.Stream.Controls.Localization.LocalizableEnumItem.GetItemsSource"/>
             or the <see cref="!:Sick.Stream.Controls.Localization.LocalizableConverter"/>.
            
             How it works
             The localizer looks for the localization string keys according to a specific formatting.
             First, the enum type itself with the following key format:
            
             {EnumType}_DisplayName - The value displayed in GUI for the Enum type.
            
             {EnumType}_ToolTip - The tooltip shown when hovering over the enum combobox. (Not always
             used)
            
             And two string per enum value of the enum with the following key formats:
            
             {EnumType}_{EnumValue}_DisplayName - The value displayed in GUI for the enum value.
            
             {EnumType}_{EnumValue}_ToolTip - The tooltip shown when hovering over the enum value
             combobox item. (This is not always used)
             </summary>
             <example>
            
             The following enum
            
             <code>
             [LocalizableEnum(typeof(Properties.EnumResources))]
             public enum MyEnum
             {
                Value1,
                Value2,
             }
             </code>
            
             Must have the corresponding enum strings in the projects EnumResources.resx:
            
              MyEnum_DisplayName
              MyEnum_ToolTip
              MyEnum_Value1_DisplayName
              MyEnum_Value1_ToolTip
              MyEnum_Value2_DisplayName
              MyEnum_Value2_ToolTip
            
             If they are not used they can be empty, but must exist to ensure all enums are localized.
             </example>
        </member>
        <member name="M:Sick.Stream.Common.Localization.EnumLocalizer.GetDisplayName``1(``0,System.Resources.ResourceManager)">
            <summary>
            Retrieves the DisplayName (GUI string) to show for the <typeparamref name="T"/>
            enum value <paramref name="value"/>.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Localization.EnumLocalizer.GetDisplayName(System.Enum,System.Resources.ResourceManager)">
            <summary>
            Retrieves the DisplayName (GUI string) to show for the enum value
            <paramref name="value"/>.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Localization.EnumLocalizer.GetToolTip(System.Enum,System.Resources.ResourceManager)">
            <summary>
            Retrieves the ToolTip (GUI string) to show for the <typeparamref name="T"/>
            enum value <paramref name="value"/>.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Localization.LocalizableEnumAttribute">
             <summary>
             Used on Enum types to mark them as localizable.
             <see cref="T:Sick.Stream.Common.Localization.EnumLocalizer"/>
            
             <see cref="!:Behavio"/>
             </summary>
        </member>
        <member name="P:Sick.Stream.Common.Localization.LocalizableEnumAttribute.ResourceType">
            <summary>
            The resource type where resource entries will exist.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Matrix">
            <summary>
            Matrix data type.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.#ctor(System.Double[],System.Int32,System.Int32)">
            <summary>
            Creates a new matrix from a data array and a size. The data array is translated such that
            position (row, col) in the matrix is data[col + row * width].
            </summary>
            <param name="data">The data array</param>
            <param name="numRows">The number of matrix rows</param>
            <param name="numCols">The number of matrix columns</param>
            <exception cref="T:System.ArgumentException">If the size of data is not equal to numRows * numCols</exception>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.#ctor(System.Double[])">
            <summary>
            Creates a new square matrix from a data array. The size of the matrix is inferred from the length
            of the data array, for instance an array of length 9 will create a 3x3 matrix.
            </summary>
            <param name="data">The data array</param>
            <exception cref="T:System.ArgumentException">If the length of the data array is not square.</exception>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates an empty matrix.
            </summary>
            <param name="numRows">The number of rows.</param>
            <param name="numCols">The number of columns.</param>
            <exception cref="T:System.ArgumentException">If numCols or numRows is less than 1.</exception>
        </member>
        <member name="P:Sick.Stream.Common.Matrix.Data">
            <summary>
            The matrix data.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Matrix.Rows">
            <summary>
            The number of rows in this matrix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Matrix.Cols">
            <summary>
            The number of columns in this matrix.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.FromString(System.String)">
             <summary>
             Parses a string into a matrix.
            
             The matrix string is expected to be formatted in such a way where the rows are a
             comma separated list, and each row is separated by a semicolon. For instance the matrix
            
             | a11 a12 |
             | a21 a22 |
            
             is formatted as "a11, a12; a21, a22".
            
             </summary>
             <param name="matrixString">The string representation of the matrix to be parsed.
             Expects the format of the strings provided by <see cref="M:Sick.Stream.Common.Matrix.ToParseableString"/>.
             </param>
             <returns>The matrix represented by <see cref="!:matrixString"/></returns>
             <exception cref="T:System.ArgumentException">If the number of columns is different for each row.</exception>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.ToParseableString">
             <summary>
             Returns a string representation of this matrix, which can be parsed by <see cref="M:Sick.Stream.Common.Matrix.FromString(System.String)"/>.
            
             The matrix is formatted in such a way where the rows are a comma separated list, and
             each row is separated by a semi colon. For instance the matrix
            
             | a11 a12 |
             | a21 a22 |
            
             is formatted as "a11, a12; a21, a22".
            
             </summary>
             <returns>A string representation of the matrix.</returns>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.GetRow(System.Int32)">
            <summary>
            Extracts a single row from the matrix.
            </summary>
            <param name="row">The index of the row to extract.</param>
            <returns>An array containing the values of the row.</returns>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.SetIdentity">
            <summary>
            Sets this matrix to an identity matrix.
            </summary>
            <exception cref="T:System.ArgumentException">If the matrix is not square.</exception>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.SetZero">
            <summary>
            Sets all elements of this matrix to 0.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.op_Equality(Sick.Stream.Common.Matrix,Sick.Stream.Common.Matrix)">
            <summary>
            Equality comparison of two matrices, elementwise.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Matrix.op_Inequality(Sick.Stream.Common.Matrix,Sick.Stream.Common.Matrix)">
            <summary>
            Inequality comparison of two matrices, elementwise.
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Vector2D">
            <summary>
            2D vector data type.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.#ctor(System.Double,System.Double)">
            <summary>
            Creates a 2D vector from an X value and a Y value.
            </summary>
            <param name="x">The X value.</param>
            <param name="y">The Y value.</param>
        </member>
        <member name="F:Sick.Stream.Common.Vector2D.X">
            <summary>
            The X value of the vector.
            </summary>
        </member>
        <member name="F:Sick.Stream.Common.Vector2D.Y">
            <summary>
            The Y value of the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.GetLength">
            <summary>
            Get the length of the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.Parse(System.String)">
            <summary>
            Parses a string to a Vector2D.
            The string should contain a comma separated list of two numbers.
            </summary>
            <example>
            Examples of strings that can be parsed.
            <code>
            "(1, 2)", "[11,-5]", "1,1", "-3;4"
            </code>
            </example>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.Normalize">
            <summary>
            Normalizes the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.Dot(Sick.Stream.Common.Vector2D)">
            <summary>
            Returns the dot product between the vector and an other vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.Equals(System.Object)">
            <summary>
            Check if this vector equals another vector
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.GetHashCode">
            <summary>
            Get a hash code for the vector
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector2D.ToString">
            <summary>
            Get a string representation of the vector
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Vector3D">
            <summary>
            3D vector data type.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.#ctor(System.Double,System.Double,System.Double)">
            <summary>
            Creates a 3D vector from an X value, a Y value and a Z value.
            </summary>
            <param name="x">The X value.</param>
            <param name="y">The Y value.</param>
            <param name="z">The Z value.</param>
        </member>
        <member name="F:Sick.Stream.Common.Vector3D.X">
            <summary>
            The X value of the vector.
            </summary>
        </member>
        <member name="F:Sick.Stream.Common.Vector3D.Y">
            <summary>
            The Y value of the vector.
            </summary>
        </member>
        <member name="F:Sick.Stream.Common.Vector3D.Z">
            <summary>
            The Z value of the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.GetLength">
            <summary>
            Get the length of the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.Parse(System.String)">
            <summary>
            Parses a string to a Vector3D.
            The string should contain a comma separated list of three numbers.
            </summary>
            <example>
            Examples of strings that can be parsed.
            <code>
            "(1, 2, 3)", "[11,-5, 0]", "1,1,2", "-3;4;0"
            </code>
            </example>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.Normalize">
            <summary>
            Normalize the vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.Dot(Sick.Stream.Common.Vector3D)">
            <summary>
            Returns the dot product between the vector and an other vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.Cross(Sick.Stream.Common.Vector3D)">
            <summary>
            Returns the cross product between the vector and an other vector.
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.Equals(System.Object)">
            <summary>
            Check if this vector equals another vector
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.GetHashCode">
            <summary>
            Get a hash code for the vector
            </summary>
        </member>
        <member name="M:Sick.Stream.Common.Vector3D.ToString">
            <summary>
            Get a string representation of the vector
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Properties.EnumResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.ILogger_LogMessageSeparator">
            <summary>
              Looks up a localized string similar to log message details.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Debug_DisplayName">
            <summary>
              Looks up a localized string similar to Debug.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Debug_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Error_DisplayName">
            <summary>
              Looks up a localized string similar to Error.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Error_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Fatal_DisplayName">
            <summary>
              Looks up a localized string similar to Fatal.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Fatal_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Info_DisplayName">
            <summary>
              Looks up a localized string similar to Info.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Info_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Verbose_DisplayName">
            <summary>
              Looks up a localized string similar to Verbose.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Verbose_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Warning_DisplayName">
            <summary>
              Looks up a localized string similar to Warning.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.EnumResources.LogLevel_Warning_ToolTip">
            <summary>
              Looks up a localized string similar to .
            </summary>
        </member>
        <member name="T:Sick.Stream.Common.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Ellipse_XRadiusToolTipString">
            <summary>
              Looks up a localized string similar to X radius: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Ellipse_YRadiusToolTipString">
            <summary>
              Looks up a localized string similar to Y radius: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Cancel">
            <summary>
              Looks up a localized string similar to Cancel.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Clone">
            <summary>
              Looks up a localized string similar to Clone.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Create">
            <summary>
              Looks up a localized string similar to Create.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Delete">
            <summary>
              Looks up a localized string similar to Delete.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Error">
            <summary>
              Looks up a localized string similar to Error.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Import">
            <summary>
              Looks up a localized string similar to Import.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Keep">
            <summary>
              Looks up a localized string similar to Keep.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_No">
            <summary>
              Looks up a localized string similar to No.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_OK">
            <summary>
              Looks up a localized string similar to Ok.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Rename">
            <summary>
              Looks up a localized string similar to Rename.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_SaveAs">
            <summary>
              Looks up a localized string similar to Save as.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Warning">
            <summary>
              Looks up a localized string similar to Warning.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.General_Yes">
            <summary>
              Looks up a localized string similar to Yes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Matrix_FromStringElementIsNotNumber">
            <summary>
              Looks up a localized string similar to &quot;{0}&quot; could not be interpreted as a number..
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Matrix_RowsAreNotEquallyWide">
            <summary>
              Looks up a localized string similar to All rows don&apos;t have the same number of columns..
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.MessageUtils_ArgumentInvalidMessage">
            <summary>
              Looks up a localized string similar to Invalid value &apos;{0}&apos; for {1}. {2}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.PixelRegion_NumberOfPixelsToolTipString">
            <summary>
              Looks up a localized string similar to Number of pixels: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Region_AreaToolTipString">
            <summary>
              Looks up a localized string similar to Number of pixels: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Region_CenterToolTipString">
            <summary>
              Looks up a localized string similar to Center: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Region_HeightToolTipString">
            <summary>
              Looks up a localized string similar to Height: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Region_RotationToolTipString">
            <summary>
              Looks up a localized string similar to Rotation: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Common.Properties.Resources.Region_WidthToolTipString">
            <summary>
              Looks up a localized string similar to Width: {0}.
            </summary>
        </member>
        <member name="T:System.Resources.ResourceManagerExtensions">
            <summary>
            Extension methods for the <see cref="T:System.Resources.ResourceManager"/> type.
            </summary>
        </member>
        <member name="M:System.Resources.ResourceManagerExtensions.HasResourceSet(System.Resources.ResourceManager,System.Globalization.CultureInfo,System.Boolean)">
            <summary>
            Check if this <see cref="T:System.Resources.ResourceManager"/> has a resource set for the given <see cref="T:System.Globalization.CultureInfo"/> or the neutral culture.
            </summary>
            <param name="resourceManager">This resource manager.</param>
            <param name="cultureInfo">Culture to get resource set for.</param>
            <param name="tryParents">true to use fallback resources, false to bypass fallback process.</param>
            <returns>true if a matching resource set exists, false otherwise.</returns>
        </member>
    </members>
</doc>
