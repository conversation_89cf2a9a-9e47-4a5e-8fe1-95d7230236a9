<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sick.Stream.Controls</name>
    </assembly>
    <members>
        <member name="M:Sick.Stream.Controls.Common.Check.ArgumentIsNotNull(System.String,System.Object)">
            <summary>
            Verifies that argument is not null.  Otherwise throws ArgumentNullException
            with the specified argument name.
            </summary>
            <exception cref="T:System.ArgumentNullException"> If the argument is null. </exception>
        </member>
        <member name="M:Sick.Stream.Controls.Common.Check.ArgumentIsValid(System.String,System.Boolean)">
            <summary>
            Verifies that predicate is true. Otherwise throws ArgumentException
            with the specified argument name.
            </summary>
            <exception cref="T:System.ArgumentNullException">For invalid argument</exception>
        </member>
        <member name="T:Sick.Stream.Controls.Common.SickBrushes">
            <summary>
            Brushes using colors taken from SICK Design System
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Common.SickColors">
            <summary>
            Sick colors taken from SICK Design System
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Common.IUiServices.SetBusyStateForAWhile">
            <summary>
            Show user that application is busy for a while
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Range`1">
            <summary>
            Class for storing a min and max value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Converters.BoolToVisibleConverter.Inverted">
            <summary>
            Specify to invert the value during conversion.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Converters.BoolToVisibleConverter.UseHidden">
            <summary>
            Specify to convert to <see cref="F:System.Windows.Visibility.Hidden"/> instead of Collapsed.
            </summary>
            <remarks>
            By default, the converter will convert false to <see cref="F:System.Windows.Visibility.Collapsed"/>,
            and vice versa.
            Back-conversion will treat Collapsed/Hidden as false independent of this setting.
            </remarks>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.ColorMapToGradientConverter">
            <summary>
            Converts a ColorMap enum value to a LinearGradientBrush.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.ColorToBrushConverter">
            <summary>
            Converts between a System.Windows.Media.Color and a SolidColorBrush.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.DisplayNameConverter">
            <summary>
            Converts a string to a display value e.g: VariableName => Variable name.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.IsEqualConverter">
            <summary>
            This converter converts a source value to boolean depending on its equality to
            the converter parameter, using <see cref="M:System.Object.Equals(System.Object)"/> for equality checking.
            The conversion also supports TwoWay or OneWayToSource bindings and back-converts the parameter
            value to source if the target value is set to True.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.IsEqualVisibilityConverter">
            <summary>
            This converter converts a source value to visibility depending on its equality to
            the converter parameter, using <see cref="M:System.Object.Equals(System.Object)"/> for equality checking.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.IsNotEqualConverter">
            <summary>
            This converter converts a source value to boolean depending on its equality to
            the converter parameter, using <see cref="M:System.Object.Equals(System.Object)"/> for equality checking.
            The conversion also supports TwoWay or OneWayToSource bindings and back-converts the parameter
            value to source if the target value is set to True.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.LocalizedDisplayNameConverter">
            <summary>
            Converts an object (enum) to a localized display name
            by fetching a display name from resources, using "Type_Value_DisplayName" as key
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.Log10Converter">
            <summary>
            Converts the value to log10 and back.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.TimeSpanFormatConverter">
            <summary>
            Converts a TimeSpan object to a string.
            The string format is different depending if the time is more or less than one second.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Converters.UpperCaseEnumToDisplayValueConverter">
            <summary>
            Converter for getting a display string from an uppercase enum,
            e.g. SCAN_3D_EXTRACTION_1 => Scan 3D extraction 1.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.AxisType">
            <summary>
            Enum representing what data to visualize along the X axis in the Profile viewer.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Graph2D">
            <summary>
            %Graph control to visualize Profile variable and Point3D variables.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.ModelProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.Model">
            <summary>
            The underlying plot model used to draw the graph
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.ViewerVariableItemsProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.ViewerVariableItems">
            <summary>
            The variables which can be drawn in the graph.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.SelectedAxisProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.SelectedAxis">
            <summary>
            The axis to plot for.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.IsPointsVisibleProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.IsPointsVisible">
            <summary>
            True if each point of the variable should be plotted.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.DrawIndexProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.EnableDrawIndex">
            <summary>
            Set to True if you want to plot for a specific index of the variable.
            Set <see cref="P:Sick.Stream.Controls.Graph2D.Index"/> to the index to plot for.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Graph2D.Index">
            <summary>
            The index of the variable to draw when <see cref="P:Sick.Stream.Controls.Graph2D.EnableDrawIndex"/> is enabled.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Graph2D.IndexProperty">
            \hideinitializer
        </member>
        <member name="M:Sick.Stream.Controls.Graph2D.Refresh">
            <summary>
            Refreshes the plot.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Graph2D.ResetViewer">
            <summary>
            Resets all axes in the plot.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Graph2D.DrawVariable(Sick.Stream.Algorithms.IVariable,System.Windows.Media.Color)">
            <summary>
            Draws the input variable in the graph viewer.
            </summary>
            <param name="variable">A variable of type Profile or Point3D.</param>
            <param name="color">The draw color</param>
        </member>
        <member name="M:Sick.Stream.Controls.Graph2D.ClearVariable(Sick.Stream.Algorithms.IVariable)">
            <summary>
            Removes a variable from the graph viewer.
            </summary>
            <param name="variable"></param>
        </member>
        <member name="T:Sick.Stream.Controls.GraphDataPoint">
            <summary>
            Custom OxyPlot DataPoint class for a profile point.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.GraphDataPoint.Point">
            <summary>
            The profile point.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.GraphDataPoint.SampleIndex">
            <summary>
            Index of the profile point along the profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.GraphDataPoint.X">
            <summary>
             The x-axis value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.GraphDataPoint.Y">
            <summary>
            The y-axis value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.GraphDataPoint.Info">
            <summary>
            Info string with the coordinates of the profile point.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.GraphDataPoint.#ctor(Sick.Stream.Algorithms.ProfilePoint,Sick.Stream.Controls.AxisType,System.Int32)">
            <summary>
            Creates a new GraphDataPoint from a <see cref="T:Sick.Stream.Algorithms.ProfilePoint"/>. The Y value will be set to the
            range from the profile point. The X value will be set depending on the selected axis type.
            </summary>
            <param name="point">The profile point.</param>
            <param name="axis">Selects which data to be represented along the X axis.</param>
            <param name="sampleIndex">The index of the profile point along the profile.</param>
        </member>
        <member name="M:Sick.Stream.Controls.GraphDataPoint.GetDataPoint">
            <inheritdoc/>
        </member>
        <member name="T:Sick.Stream.Controls.Localization.EnumItemsSource">
             <summary>
             Behaviour for ItemControls. Helps to set items source from localized enum values. Make sure
             that the enums are localized and marked with the
             <see cref="T:Sick.Stream.Common.Localization.LocalizableEnumAttribute"/>.
            
             See <see cref="T:Sick.Stream.Common.Localization.EnumLocalizer"/> for how enum localization works in general.
             </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Localization.LocalizableEnumItem">
             <summary>
             An enum item that sets its <see cref="P:Sick.Stream.Controls.Localization.LocalizableEnumItem.DisplayName"/> and <see cref="P:System.Windows.FrameworkContentElement.ToolTip"/>
             from the localizable Enum <see cref="P:Sick.Stream.Controls.Localization.LocalizableEnumItem.Value"/> resource strings.
             Requires that the enum has the <see cref="T:Sick.Stream.Common.Localization.LocalizableEnumAttribute"/>.
            
             See <see cref="T:Sick.Stream.Common.Localization.EnumLocalizer"/> for how enum localization works in general.
             </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Properties.EnumResources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.EnumResources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.EnumResources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ColorSettings_Color">
            <summary>
              Looks up a localized string similar to Color.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ColorSettings_ContrastLabel">
            <summary>
              Looks up a localized string similar to Contrast.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ColorSettings_EnableLightLabel">
            <summary>
              Looks up a localized string similar to Enable lightning.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ComponentType_INTENSITY_DisplayName">
            <summary>
              Looks up a localized string similar to Intensity.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ComponentType_RANGE_DisplayName">
            <summary>
              Looks up a localized string similar to Range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ComponentType_REFLECTANCE_DisplayName">
            <summary>
              Looks up a localized string similar to Reflectance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ComponentType_SCATTER_DisplayName">
            <summary>
              Looks up a localized string similar to Scatter.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.DotWedgePlaneFitMode_ALL_PIXELS_DisplayName">
            <summary>
              Looks up a localized string similar to All pixels.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.DotWedgePlaneFitMode_BACKGROUND_PIXELS_DisplayName">
            <summary>
              Looks up a localized string similar to Background pixels.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.DotWedgePlaneFitMode_DOT_PIXELS_DisplayName">
            <summary>
              Looks up a localized string similar to Dot pixels.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.Graph2D_SampleIndex">
            <summary>
              Looks up a localized string similar to Sample index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_AxisDropDown_ToolTip">
            <summary>
              Looks up a localized string similar to Values to display on the horizontal axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_EnableProfileIndexToolTip">
            <summary>
              Looks up a localized string similar to Enable to only show one profile from each selected variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpButtonToolTip">
            <summary>
              Looks up a localized string similar to Show help..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpHeader">
            <summary>
              Looks up a localized string similar to Viewer navigation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpPanLabel">
            <summary>
              Looks up a localized string similar to Pan.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpPanText">
            <summary>
              Looks up a localized string similar to Right mouse button.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpResetLabel">
            <summary>
              Looks up a localized string similar to Reset axes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpResetText">
            <summary>
              Looks up a localized string similar to Double right click.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpText">
            <summary>
              Looks up a localized string similar to You can zoom/pan/reset a single axis by positioning the mouse cursor over the axis before starting the zoom/pan/reset..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpZoomLabel">
            <summary>
              Looks up a localized string similar to Zoom.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpZoomRectangleLabel">
            <summary>
              Looks up a localized string similar to Zoom by rectangle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpZoomRectangleText">
            <summary>
              Looks up a localized string similar to Left mouse button.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_HelpZoomText">
            <summary>
              Looks up a localized string similar to Mouse wheel.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_ProfileIndexToolTip">
            <summary>
              Looks up a localized string similar to The index of the profile(s) to show in the viewer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_ProfilePointsButtonToolTip">
            <summary>
              Looks up a localized string similar to Draw profile points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphControl_ResetButtonToolTip">
            <summary>
              Looks up a localized string similar to Reset viewer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphDataPoint_Position">
            <summary>
              Looks up a localized string similar to Profile position: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphDataPoint_X">
            <summary>
              Looks up a localized string similar to X: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphDataPoint_Y">
            <summary>
              Looks up a localized string similar to Y: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.GraphDataPoint_Z">
            <summary>
              Looks up a localized string similar to Z: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_Background">
            <summary>
              Looks up a localized string similar to Background.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_DisplayMode">
            <summary>
              Looks up a localized string similar to Display mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_Lighting">
            <summary>
              Looks up a localized string similar to Lighting.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_LightIntensity">
            <summary>
              Looks up a localized string similar to Light intensity.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_MouseoverInfo">
            <summary>
              Looks up a localized string similar to Mouseover info.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_MouseoverInfo_Tooltip">
            <summary>
              Looks up a localized string similar to Select if mouseover info in the 3D viewer should be enabled. Disabling mouseover info can improve the performance of the 3D viewer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_Point">
            <summary>
              Looks up a localized string similar to Point.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_PointSize">
            <summary>
              Looks up a localized string similar to Point size.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_SpecularHighlights">
            <summary>
              Looks up a localized string similar to Specular highlights.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.MiscSettings3D_Surface">
            <summary>
              Looks up a localized string similar to Surface.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.RectificationMethod_BOTTOMMOST_DisplayName">
            <summary>
              Looks up a localized string similar to Bottommost.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.RectificationMethod_BRIGHTEST_DisplayName">
            <summary>
              Looks up a localized string similar to Brightest.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.RectificationMethod_DARKEST_DisplayName">
            <summary>
              Looks up a localized string similar to Darkest.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.RectificationMethod_MEAN_DisplayName">
            <summary>
              Looks up a localized string similar to Mean.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.RectificationMethod_TOPMOST_DisplayName">
            <summary>
              Looks up a localized string similar to Topmost.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ScaleSettings_FlipZLabel">
            <summary>
              Looks up a localized string similar to Flip Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ScaleSettings_ScaleXLabel">
            <summary>
              Looks up a localized string similar to Scale X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ScaleSettings_ScaleZLabel">
            <summary>
              Looks up a localized string similar to Scale Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_EncoderStatusStringFormat">
            <summary>
              Looks up a localized string similar to Encoder = {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_IntensityStatusStringFormat">
            <summary>
              Looks up a localized string similar to Intensity = {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_PixelCoordinatesStatusStringFormat">
            <summary>
              Looks up a localized string similar to (x = {0}, y = {1}).
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_RangeStatusStringFormat">
            <summary>
              Looks up a localized string similar to Range = {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_ReflectanceStatusStringFormat">
            <summary>
              Looks up a localized string similar to Reflectance = {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_RgbStatusStringFormat">
            <summary>
              Looks up a localized string similar to (Red = {0}, Green = {1}, Blue = {2}).
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_ScatterStatusStringFormat">
            <summary>
              Looks up a localized string similar to Scatter = {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.StatusStringFormatter_WorldCoordinatesStatusStringFormat">
            <summary>
              Looks up a localized string similar to (x = {0} mm, y = {1} mm, z = {2} mm).
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.UnitType_DEGREES_DisplayName">
            <summary>
              Looks up a localized string similar to Degrees.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.UnitType_MILLIMETERS_DisplayName">
            <summary>
              Looks up a localized string similar to Millimeters.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.UnitType_MILLISECONDS_DisplayName">
            <summary>
              Looks up a localized string similar to Milliseconds.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.UnitType_OTHER_DisplayName">
            <summary>
              Looks up a localized string similar to Other.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.UnitType_PIXELS_DisplayName">
            <summary>
              Looks up a localized string similar to Pixels.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerAutoSetContrastButtonTooltip">
            <summary>
              Looks up a localized string similar to Automatically adjust contrast..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerBase_IndexToolTipString">
            <summary>
              Looks up a localized string similar to Index {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerToolbar_Display">
            <summary>
              Looks up a localized string similar to Display.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerToolbar_Scale">
            <summary>
              Looks up a localized string similar to Scale.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerToolbarComponentTooltip">
            <summary>
              Looks up a localized string similar to Select which image component to show..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerToolbarResetViewerButtonTooltip">
            <summary>
              Looks up a localized string similar to Reset viewer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerToolbarSettingsButtonTooltip">
            <summary>
              Looks up a localized string similar to Change viewer settings..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ViewerVariableItem_MaxVariableLengthWarning">
            <summary>
              Looks up a localized string similar to The variable &apos;{0}&apos; contains {1} elements. Due to performance reasons the number of elements that can be visualized in the viewer is limited to {2}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.XAxisDirection_LEFT_DisplayName">
            <summary>
              Looks up a localized string similar to Left.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.XAxisDirection_RIGHT_DisplayName">
            <summary>
              Looks up a localized string similar to Right.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ZAxisDirection_DOWN_DisplayName">
            <summary>
              Looks up a localized string similar to Down.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Properties.Resources.ZAxisDirection_UP_DisplayName">
            <summary>
              Looks up a localized string similar to Up.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.RangeSlider.RangeSlider">
            <summary>
            This range slider was taken from Avalon Controls Library:
            https://github.com/jogibear9988/avaloncontrolslib
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStart">
            <summary>
            The min value for the range of the range slider
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStartProperty">
            <summary>
            The min value for the range of the range slider
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStop">
            <summary>
            The max value for the range of the range slider
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStopProperty">
            <summary>
            The max value for the range of the range slider
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStartSelected">
            <summary>
            The min value of the selected range of the range slider
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStartSelectedProperty">
            <summary>
            The min value of the selected range of the range slider
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStopSelected">
            <summary>
            The max value of the selected range of the range slider
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeStopSelectedProperty">
            <summary>
            The max value of the selected range of the range slider
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSlider.MinRange">
            <summary>
            The min range value that you can have for the range slider
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when MinRange is set less than 0</exception>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.MinRangeProperty">
            <summary>
            The min range value that you can have for the range slider
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">Thrown when MinRange is set less than 0</exception>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeSelectionChangedEvent">
            <summary>
            Event raised whenever the selected range is changed
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.RangeSlider.RangeSlider.RangeSelectionChanged">
            <summary>
            Event raised whenever the selected range is changed
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveBack">
            <summary>
            Command to move back the selection
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveForward">
            <summary>
            Command to move forward the selection
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveAllForward">
            <summary>
            Command to move all forward the selection
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveAllBack">
            <summary>
            Command to move all back the selection
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.#cctor">
            <summary>
            Static constructor
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveSelection(System.Boolean)">
            <summary>
            Moves the current selection with x value
            </summary>
            <param name="isLeft">True if you want to move to the left</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.ResetSelection(System.Boolean)">
            <summary>
            Reset the Slider to the Start/End
            </summary>
            <param name="isStart">Pass true to reset to start point</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.MoveSelection(System.Double)">
            <summary>
             Change the range selected
            </summary>
            <param name="span">The steps</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.SetSelectedRange(System.Double,System.Double)">
            <summary>
            Sets the selected range in one go. If the selection is invalid, nothing happens.
            </summary>
            <param name="selectionStart">New selection start value</param>
            <param name="selectionStop">New selection stop value</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.ZoomToSpan(System.Double)">
            <summary>
            Changes the selected range to the supplied range
            </summary>
            <param name="span">The span to zoom</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSlider.OnApplyTemplate">
            <summary>
            Override to get the visuals from the control template
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventHandler">
            <summary>
            Delegate for the RangeSelectionChanged event
            </summary>
            <param name="sender">The object raising the event</param>
            <param name="e">The event arguments</param>
        </member>
        <member name="T:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventArgs">
            <summary>
            Event arguments for the Range slider RangeSelectionChanged event
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventArgs.NewRangeStart">
            <summary>
            The new range start selected in the range slider
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventArgs.NewRangeStop">
            <summary>
            The new range stop selected in the range slider
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventArgs.#ctor(System.Double,System.Double)">
            <summary>
            Sets the range start and range stop for the event args
            </summary>
            <param name="newRangeStart">The new range start set</param>
            <param name="newRangeStop">The new range stop set</param>
        </member>
        <member name="M:Sick.Stream.Controls.RangeSlider.RangeSelectionChangedEventArgs.#ctor(Sick.Stream.Controls.RangeSlider.RangeSlider)">
            <summary>
            Sets the range start and range stop for the event args by using the slider RangeStartSelected and RangeStopSelected properties
            </summary>
            <param name="slider">The slider to get the info from</param>
        </member>
        <member name="P:Sick.Stream.Controls.ViewerVariableItem.CustomizedElements">
            <summary>
            In Stream Setup we need to have the possibility to set color and tooltips of
            individual variable elements.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.AbstractListVariableSceneNode`1">
            <summary>
            Base class for all list variable scene node types.
            </summary>
            <typeparam name="T">Variable type</typeparam>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.AbstractListVariableSceneNode`1.CreateGeometryNodes(`0)">
            <summary>
            Create the child geometry nodes for the variable.
            </summary>
            <param name="variable">Variable to create geometry for.</param>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.AbstractListVariableSceneNode`1.UpdateDecoration(System.Single)">
            <summary>
            Called when the decoration needs to be updated.
            </summary>
            <param name="opacity">The opacity of the parameter in the viewer (percentage in the range 0 to 1).</param>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.AbstractListVariableSceneNode`1.getNodeLocalBounds">
            <inheritdoc/>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.AbstractListVariableSceneNode`1.disposeNode">
            <inheritdoc/>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.AbstractVariableSceneNode`1">
            <summary>
            Base class for all variable scene node types.
            </summary>
            <typeparam name="T">Variable type</typeparam>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.AbstractVariableSceneNode`1.UpdateDecoration(System.Single)">
            <summary>
            Called when the decoration needs to be updated.
            </summary>
            <param name="opacity">The opacity of the parameter in the viewer (percentage in the range 0 to 1).</param>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.Line3DListVariableSceneNode">
            <summary>
            Scene node for <see cref="T:Sick.Stream.Algorithms.Line3DList"/> variables.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.PixelRegionListVariableSceneNode">
            <summary>
            SceneNode for PixelRegions. Unlike all other ListVariableSceneNodes, we cannot create a
            child node for each element in the list, for performance reasons. Instead, a single image node
            is created, and instead of relying on individual scene nodes reporting events when hovering over
            them, it is manually handled.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.PlaneListVariableSceneNode">
            <summary>
            Scene node for <see cref="T:Sick.Stream.Algorithms.PlaneList"/> variables.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.Point3DListVariableSceneNode">
            <summary>
            Scene node for <see cref="T:Sick.Stream.Algorithms.Point3DList"/> variables.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.SceneNodes.PointCloudVariableSceneNode.CreateGeometryNode(Sick.Stream.Algorithms.IPointCloud)">
            <summary>
            Create the child geometry node for the variable.
            </summary>
            <param name="variable">Variable to create geometry for.</param>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.ProfileListVariableSceneNode">
            <summary>
            Scene node for <see cref="T:Sick.Stream.Algorithms.ProfileList"/> variables.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.RoundingUtil">
            <summary>
            Class containing functions for rounding the editable variable to 0.1 micron.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SceneNodes.TextListVariableSceneNode">
            <summary>
            Scene node for <see cref="T:Sick.Stream.Algorithms.TextList"/> variables.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.ColorSettings">
            <summary>
            Interaction logic for ColorSettings.xaml
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.ColorSettings.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.MiscSettings3D">
            <summary>
            Interaction logic for MiscSettings3D.xaml
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.MiscSettings3D.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.ScaleSettings">
            <summary>
            Interaction logic for ScaleSettings.xaml
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.ScaleSettings.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.ScaleSlider">
            <summary>
            Interaction logic for ScaleSlider.xaml
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.ScaleSlider.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SettingsUtil.CreateViewerImageComponents(System.Collections.Generic.List{Sick.GenIStream.ComponentId},System.Boolean,System.Boolean)">
            <summary>
            Creates a list of ViewerImageComponent from the list of GenIStream ComponentIds.
            If addHybridComponents is true, it will also add the hybrid components if possible.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SettingsUtil.ViewerComponentIdFromGenIStreamComponentId(Sick.GenIStream.ComponentId)">
            <summary>
            Get the corresponding ViewerComponentId from a GenIStream ComponentId.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SettingsUtil.GenIStreamComponentIdFromViewerComponentId(Sick.Stream.Controls.Viewer.ViewerComponentId)">
            <summary>
            Get the corresponding GenIStream ComponentId from a ViewerComponentId.
            The hybrid components will return the RANGE id.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SettingsUtil.RegionHasValidViewerComponentId(Sick.GenIStream.IRegion,Sick.Stream.Controls.Viewer.ViewerComponentId)">
            <summary>
            Returns true if the input GenIStream region contains the component(s) that are needed for a specific ViewerComponentId.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SettingsUtil.SuitImageFromGenIStreamComponent(Sick.GenIStream.IComponent,System.UInt32)">
            <summary>
            Get SUIT image from a GenIStream component.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.SliderTickBar">
            <summary>
            Interaction logic for SliderTickBar.xaml
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Settings.SliderTickBar.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Settings.ToggleButtonPopupBehavior">
            <summary>
            Class used to attach a popup to a toggle button.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SuitWrappers.ChangeListener`1">
            <summary>
            Wrapper for the SUIT change listener, allowing you to listen to SUIT events
            which require a ChangeListener.
            </summary>
            <typeparam name="T">The type of the value to listen to.</typeparam>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SuitWrappers.SuitCallback">
            <summary>
            Implements the SUIT ICallback interface.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.SuitWrappers.SuitPredicate`1">
            <summary>
            Implements the SUIT Predicate interface.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.Utility.CoordinateSystemInfo">
            <summary>
            Used to store the previously selected image's coordinate system,
            as that image (and its coordinate system) may be disposed.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.Utility.CoordinateSystemInfo.ShouldReset(Sick.Stream.Controls.Viewer.Utility.CoordinateSystemInfo)">
            <summary>
            Checks if the viewer should reset when showing a new image.
            Reset the viewer if the XZ bounds of the images don't overlap,
            Or if the areas of the bounds differs too much.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.DisplayMode">
            <summary>
            Enum used to control the display mode of the 3D viewer.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.ColorMap">
            <summary>
            The color map used for visualizing the image in the viewer.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.ViewerComponentId">
            <summary>
            The image component used to select the overlay in the viewer.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer.ViewerBase">
            <summary>
            Partial class for ViewerBase, for handling the drawing of variables in the viewer.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Viewer.ViewerBase.IsVariableMouseEventsEnabled">
            <summary>
            Enable or disable variable mouse over events in the viewer.
            If the viewer is used interactively while running a Stream Editor program,
            the variable mouse events should be disabled.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.ViewerBase.ResetCameraView">
            <summary>
            Resets the camera view to fit to the bounds of the currently viewed image.
            </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Viewer.ViewerBase._colorMaps">
            <summary>
            Store the color map for each component. This is needed since we
            reset the HeightMap in SetEmptyHeightMap when deleting an image.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.ViewerBase.SwitchListener(System.Object,System.Object,System.Collections.Specialized.NotifyCollectionChangedEventHandler)">
            <summary>
            Stops listening to old collection (if it implements INotifyCollectionChanged) and starts
            listening to new collection (if it implements INotifyCollectionChanged).
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer.ViewerBase.CreateSceneNodeForVariable(Sick.Stream.Controls.ViewerVariableItem)">
            <summary>
            Create the <see cref="T:de.sick.suit.visualization.scene.ISceneNode"/> for the given <see cref="T:Sick.Stream.Controls.ViewerVariableItem"/>.
            </summary>
            <param name="viewerVariableItem">Variable to create scene node for.</param>
            <returns>The created scene node</returns>
        </member>
        <member name="T:Sick.Stream.Controls.IListVariableSceneNode">
            <summary>
            Interface for SceneNodes representing variables in the viewer.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.IListVariableSceneNode.SelectElement(System.Int32)">
            <summary>
            Selects the variable element with the given index. Does nothing if the variable element is not selectable.
            </summary>
            <param name="index">The index of the variable to select.</param>
            <exception cref="T:System.IndexOutOfRangeException">If the given index is out of range of the variable.</exception>
        </member>
        <member name="M:Sick.Stream.Controls.IListVariableSceneNode.IsSelected(System.Int32)">
            <summary>
            Returns whether the variable element of the given index is selected.
            </summary>
            <param name="index">The index of the element.</param>
            <exception cref="T:System.IndexOutOfRangeException">If the given index is out of range of the variable.</exception>
            <returns>Whether the variable element is selected</returns>
        </member>
        <member name="P:Sick.Stream.Controls.IListVariableSceneNode.Count">
            <summary>
            Returns the number of elements contained in this scene node.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.IListVariableSceneNode.FirstValidElementIndex">
            <summary>
            Returns the index of the first valid element.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.MouseMove">
            <summary>
            Triggered when the mouse is moving over the variable in the viewer. Provides
            the position of the mouse pointer in the viewer's coordinate system.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.VariableChanged">
            <summary>
            Triggered when the underlying variable has been changed by editing it in the viewer.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.MouseEnter">
            <summary>
            Triggered when the mouse enters a variable element in the viewer, providing
            the name of the variable, it's type, the index in it's variable list, and
            the element itself.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.MouseClick">
            <summary>
            Triggered when a visible variable element is clicked, providing
            the name of the variable, it's type, the index in it's variable list, and
            the element itself.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.MouseLeave">
            <summary>
            Triggered when the mouse leaves the variable.
            </summary>
        </member>
        <member name="E:Sick.Stream.Controls.IListVariableSceneNode.EditingStarted">
            <summary>
            Triggered when the variable has started being manipulated in the edit mode.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.IVariableSceneNode.Deselect">
            <summary>
            Deselects all variable elements selected for editing. Does nothing if the variable is
            not editable, if it's not in edit mode or if it's already deselected.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.IVariableSceneNode.Enabled">
            <summary>
            When not enabled, the node becomes less visible and does not produce any mouse events.
            Useful when editing other variables.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.IVariableSceneNode.IsInEditMode">
            <summary>
            Whether the variable is currently in edit mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.IVariableSceneNode.CanBeEdited">
            <summary>
            Whether the variable can be put into edit mode.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.IVariableSceneNode.Refresh">
            <summary>
            Deletes the element with the given index.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer2D">
            <summary>
            2D viewer control to visualize images and variables.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Viewer2D.SelectedVariable">
            <summary>
            The selected variable. This is the variable that will be edited if in edit mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Viewer2D.IsInCreationMode">
            <summary>
            True if in creation mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Viewer2D.IsInEditMode">
            <summary>
            True if in edit mode.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer2D.SelectFirstValidVariableElement(Sick.Stream.Controls.IListVariableSceneNode)">
            <summary>
            Selects the first valid element in a list variable.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer2D.AdjustVariablesToImageCoordinates">
            <summary>
            Flips the Y-axis of the 2D image variables. This is needed to align them with
            the coordinate system of the 2D image, and the height of the image is needed for this.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer2D.ResetCameraView">
            <summary>
            Resets the camera view to fit the current viewed image.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer2D.DrawVariable(Sick.Stream.Algorithms.IVariable,System.Windows.Media.Color,System.Boolean)">
            <summary>
            Draws the input variable in the 2D viewer.
            </summary>
            <param name="variable">A variable of type Point2D, Line2D, Rectangle, Ellipse, or PixelRegion.</param>
            <param name="color">The draw color</param>
            <param name="isEditable">If true, editing of the variable will be enabled.</param>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer2DToolbar">
            <summary>
            Interaction logic for Viewer2DToolbar.xaml
            </summary>
            <summary>
            Viewer2DToolbar
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer2DToolbar.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer3D">
             <summary>
             3D viewer control to visualize images and variables.
            
             <para>
             Use the <see cref="F:Sick.Stream.Controls.Viewer.ViewerBase.ViewerLoaded"/> event to know when the 3D visualization has been completely initialized.
             </para>
             </summary>
        </member>
        <member name="F:Sick.Stream.Controls.Viewer3D.IsDataPickingEnabledProperty">
            \hideinitializer
        </member>
        <member name="P:Sick.Stream.Controls.Viewer3D.IsDataPickingEnabled">
            <summary>
            True if mouseover info in the 3D viewer should be enabled. Disabling mouseover info can improve the performance of the 3D viewer.
            </summary>
        </member>
        <member name="P:Sick.Stream.Controls.Viewer3D.UseWinFormsHost">
            <summary>
            In Stream Editor we use WinFormHost for the 3D viewer for performance reasons.
            However, the WinFomHost does not support multiple instances of the 3D viewer, which
            is something we need in Stream Setup.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3D.ResetCameraView">
            <summary>
            Resets the camera view to fit the current viewed 3D image.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3D.DrawVariable(Sick.Stream.Algorithms.IVariable,System.Windows.Media.Color)">
            <summary>
            Draws the input variable in the 3D viewer.
            </summary>
            <param name="variable">A variable of type Point3D, Plane or PointCloud.</param>
            <param name="color">The draw color</param>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3D.DrawCameraFieldOfView(Sick.Stream.Common.Vector3D,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Function used to visualize the Guaranteed field of view in Stream Setup.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3D.ClearCameraFieldOfView">
            <summary>
            Clear the Guaranteed field of view.
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3D.HideCoordinateAxesAndHomeButton">
            <summary>
            Hides the coordinate axes and home button that are normally located in the left bottom corner of the 3D viewer.
            The coordinate axes will be incorrect, since we mirror the y-axis.
            </summary>
        </member>
        <member name="T:Sick.Stream.Controls.Viewer3DToolbar">
            <summary>
            Interaction logic for Viewer3DToolbar.xaml
            </summary>
            <summary>
            Viewer3DToolbar
            </summary>
        </member>
        <member name="M:Sick.Stream.Controls.Viewer3DToolbar.InitializeComponent">
            <summary>
            InitializeComponent
            </summary>
        </member>
    </members>
</doc>
