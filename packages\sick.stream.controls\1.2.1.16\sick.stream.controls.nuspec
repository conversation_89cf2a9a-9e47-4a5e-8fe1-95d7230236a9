﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Sick.Stream.Controls</id>
    <version>********</version>
    <authors>SICK</authors>
    <description>Stream Editor WPF controls</description>
    <copyright>Copyright © SICK AG 2025</copyright>
    <dependencies>
      <group targetFramework=".NETFramework4.7.2">
        <dependency id="Sick.GenIStreamDotNet" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Sick.Stream.Common" version="********" exclude="Build,Analyzers" />
        <dependency id="Sick.Stream.Processing" version="********" exclude="Build,Analyzers" />
        <dependency id="IKVM.WINDWARD" version="*******" exclude="Build,Analyzers" />
        <dependency id="MaterialDesignColors" version="2.0.6" exclude="Build,Analyzers" />
        <dependency id="MaterialDesignThemes" version="4.5.0" exclude="Build,Analyzers" />
        <dependency id="OpenTK" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="OpenTK.GLControl" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="OxyPlot.Core" version="2.2.0" exclude="Build,Analyzers" />
        <dependency id="OxyPlot.Wpf" version="2.2.0" exclude="Build,Analyzers" />
        <dependency id="OxyPlot.Wpf.Shared" version="2.2.0" exclude="Build,Analyzers" />
        <dependency id="SuitDotNet" version="1.3.190" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.6.1" exclude="Build,Analyzers" />
        <dependency id="System.ComponentModel.Annotations" version="5.0.0" exclude="Build,Analyzers" />
        <dependency id="System.Memory" version="4.6.2" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.1.1" exclude="Build,Analyzers" />
        <dependency id="System.Text.Json" version="9.0.3" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.6.2" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>