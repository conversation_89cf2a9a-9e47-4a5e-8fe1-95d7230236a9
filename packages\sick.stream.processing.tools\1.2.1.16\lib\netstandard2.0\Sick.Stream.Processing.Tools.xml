<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sick.Stream.Processing.Tools</name>
    </assembly>
    <members>
        <member name="T:Sick.Stream.Processing.Tools.CreateRegionOfInterest">
            <summary>
            Internal tool used for Calibration in Stream Setup
            It is located here to enable localization of its parameters
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.DefineDotWedgeWorldPoints">
            <summary>
            Internal tool used for Calibration in Stream Setup
            It is located here to enable localization of its parameters
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.DefineSawToothWorldPoints">
            <summary>
            Internal tool used for Calibration in Stream Setup
            It is located here to enable localization of its parameters
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedSourceImageParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Source Image" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedComponentParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Component" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedKernelSizeParameterAttribute">
             <summary>
            Set display name and description to localized string similar to "Kernel Size" for the parameter.
             </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedRegionOfInterestParameterAttribute">
            <summary>
            Set display name, description, and optional description to localized string similar to "Region" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedFilterTargetImageParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Destination image" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedOptionalEmptyVariableParameterAttribute">
            <summary>
            Set the description to a localized string similar to "Will create an empty variable." for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedSearchDirectionParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Search direction" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedOuterRadiusParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Outer radius" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedInnerRadiusParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Inner radius" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedProbeCountParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Probe count" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedDiffStepPixelParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Difference step" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedRansacIterationsParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Iterations" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedThresholdParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Threshold" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedOutlierMarginParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Outlier margin" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedSlopePolarityParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Edge polarity" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedEdgeSelectionParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Edge selection" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedInterpolationParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Interpolation" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedFitModeParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Fit mode" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedEdgeSideParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Edge side" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedRectificationMethodParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Rectification method" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedRectificationSpreadSizeParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Rectification spread size" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedTranslationXParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Translation X" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedTranslationYParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Translation Y" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedAngleParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Rotation angle" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedMorphologyMethodParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Morphology method" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedInliersParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Inliers" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.LocalizedOutliersParameterAttribute">
            <summary>
            Set display name and description to localized string similar to "Outliers" for the parameter.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Tools.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_ApplyAlignment_Description">
            <summary>
              Looks up a localized string similar to If the calibration file contains an alignment transform it can be applied to the calibrated point cloud before the rectification is applied..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_ApplyAlignment_DisplayName">
            <summary>
              Looks up a localized string similar to Apply alignment.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_CalibrationModel_Description">
            <summary>
              Looks up a localized string similar to The calibration model used when applying the calibration..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_CalibrationModel_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The calibrated output image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_DynamicImageHeight_Description">
            <summary>
              Looks up a localized string similar to If checked, the output image length will be selected to preserve the Y resolution of the input image. If unchecked the output image will have the same number of profiles as the input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_DynamicImageHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Dynamic image height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_RectificationSpread_Description">
            <summary>
              Looks up a localized string similar to To avoid missing data in rectified regions, each calibrated data point can be spread to multiple adjacent rectification bins. This parameter controls the spread width. A higher value includes more bins. Setting this parameter to 0.0 disables spreading..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_RectificationSpread_DisplayName">
            <summary>
              Looks up a localized string similar to Rectification spread.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_RectificationWidth_Description">
             <summary>
               Looks up a localized string similar to The width in pixels of the rectified image. It can sometimes be useful to reduce the width of the rectified image for the following reasons:
            • To reduce the computation time of later image processing steps.
            • To reduce missing data due to rectification phenomenons..
             </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_RectificationWidth_DisplayName">
            <summary>
              Looks up a localized string similar to Rectification width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The uncalibrated input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_YResolution_Description">
            <summary>
              Looks up a localized string similar to The Y resolution of the destination image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyCalibration_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_ReferenceImage_Description">
            <summary>
              Looks up a localized string similar to A reference image needed when transforming pixel regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_ReferenceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_ReferenceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to The image is only needed if the variable is a pixel region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_Transform_Description">
            <summary>
              Looks up a localized string similar to The input transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_Transform_DisplayName">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_TransformedVariable_Description">
            <summary>
              Looks up a localized string similar to The transformed variable. It will be of the same type as the input variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_TransformedVariable_DisplayName">
            <summary>
              Looks up a localized string similar to Transformed variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_TransformLengthDoesNotMatchVariableLength">
            <summary>
              Looks up a localized string similar to Length of Transform and variable {0} does not match, either the length of Transform needs to be 1 or the same as the variable length..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_Variable_Description">
            <summary>
              Looks up a localized string similar to The input variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform2D_VariableRequiresRigidTransform">
            <summary>
              Looks up a localized string similar to Variables of type {0} can only be transformed with a Rigid transform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_Transform_Description">
            <summary>
              Looks up a localized string similar to The input transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_Transform_DisplayName">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_TransformedVariable_Description">
            <summary>
              Looks up a localized string similar to The transformed variable. It will be of the same type as the input variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_TransformedVariable_DisplayName">
            <summary>
              Looks up a localized string similar to Transformed variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_TransformingPointCloudDoesNotSupportMultipleTransforms">
            <summary>
              Looks up a localized string similar to When transforming a PointCloud, only one transform can be supplied..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_TransformLengthDoesNotMatchVariableLength">
            <summary>
              Looks up a localized string similar to Length of Transform and variable {0} does not match, either the length of Transform needs to be 1 or the same as the variable length..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_Variable_Description">
            <summary>
              Looks up a localized string similar to The input variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ApplyTransform3D_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.BilateralFilter_SigmaRange_Description">
            <summary>
              Looks up a localized string similar to Range of pixel values that affect the filtering..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.BilateralFilter_SigmaRange_DisplayName">
            <summary>
              Looks up a localized string similar to Sigma range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_InvalidTransformListLengthException">
            <summary>
              Looks up a localized string similar to Transform variable can only change from one source transform to one target transform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_InvalidVariableType">
            <summary>
              Looks up a localized string similar to Cannot transform variable &quot;{0}&quot;, as variables of type {1} cannot be transformed.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ReferenceImage_Description">
            <summary>
              Looks up a localized string similar to A reference image needed when transforming pixel regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ReferenceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ReferenceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to No image needs to be provided if the list of variables does not contain any pixel regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ResultSuffix_Description">
            <summary>
              Looks up a localized string similar to The new variable names will have the old name with the suffix at the end..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ResultSuffix_DisplayName">
            <summary>
              Looks up a localized string similar to Result suffix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_ResultSuffix_OptionalDescription">
            <summary>
              Looks up a localized string similar to The old variables will be overwritten with the new values..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_SourceTransform_Description">
            <summary>
              Looks up a localized string similar to Transform defining a coordinate system in the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_SourceTransform_DisplayName">
            <summary>
              Looks up a localized string similar to Source transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_TargetTransform_Description">
            <summary>
              Looks up a localized string similar to Transform defining a coordinate system in the destination (live) image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_TargetTransform_DisplayName">
            <summary>
              Looks up a localized string similar to Target transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_TransformIsEmptyException">
            <summary>
              Looks up a localized string similar to The input transform is empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_VariableNames_Description">
            <summary>
              Looks up a localized string similar to A comma separated list with the names of the variables to be transformed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangeCoordinateSystem_VariableNames_DisplayName">
            <summary>
              Looks up a localized string similar to Variable names.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangePixelFormat_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The output image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangePixelFormat_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangePixelFormat_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ChangePixelFormat_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_CombinedRegion_Description">
            <summary>
              Looks up a localized string similar to The output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_CombinedRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Combined region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_InvalidMethodInputException">
            <summary>
              Looks up a localized string similar to {0} is required for method: Difference.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Method_Description">
            <summary>
              Looks up a localized string similar to The method to use when combining the regions. .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Method_DisplayName">
            <summary>
              Looks up a localized string similar to Method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_MissingInputImageException">
            <summary>
              Looks up a localized string similar to An input image is required if any region input is a Rectangle or Ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Region1_Description">
            <summary>
              Looks up a localized string similar to The first region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Region1_DisplayName">
            <summary>
              Looks up a localized string similar to Region 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Region2_Description">
            <summary>
              Looks up a localized string similar to The second region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Region2_DisplayName">
            <summary>
              Looks up a localized string similar to Region 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_Region2_OptionalDescription">
            <summary>
              Looks up a localized string similar to The elements in Region 1 will be combined..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The combined region will be transformed to the coordinate system of this image. Only needed if the input regions are of type Rectangle or Ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CombineRegions_SourceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to The combined region will not be transformed to the coordinate system of an image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Angle_Description">
            <summary>
              Looks up a localized string similar to The desired rotation when creating a rigid transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Angle_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the circular arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Center.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Component_Description">
            <summary>
              Looks up a localized string similar to Select which image component to process.
            A 2D image contains an Intensity component.
            A 3D image can contain one or multiple components: Range, Reflectance, Scatter.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Component_DisplayName">
            <summary>
              Looks up a localized string similar to Component.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_DiffStepPix_Description">
            <summary>
              Looks up a localized string similar to Size of the edge probe steps..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_DiffStepPix_DisplayName">
            <summary>
              Looks up a localized string similar to Difference step.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_FilterTargetImage_Description">
            <summary>
              Looks up a localized string similar to The filtered image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_FilterTargetImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_ImageNoRangeException">
            <summary>
              Looks up a localized string similar to The image needs to have a range component..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_IndexOfOutRangeException">
            <summary>
              Looks up a localized string similar to The index is out of range..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Inliers_Description">
            <summary>
              Looks up a localized string similar to All points that were not rejected in the fitting algorithm..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Inliers_DisplayName">
            <summary>
              Looks up a localized string similar to Inliers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_InnerRadius_Description">
            <summary>
              Looks up a localized string similar to The inner radius used for specifying the search region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_InnerRadius_DisplayName">
            <summary>
              Looks up a localized string similar to Inner radius.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Interpolation_Description">
            <summary>
              Looks up a localized string similar to The interpolation method to use in the edge profiles.
               • Nearest: the fastest interpolation method.
               • Linear: can give slightly better accuracy,
                 but with additional computational cost..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Interpolation_DisplayName">
            <summary>
              Looks up a localized string similar to Interpolation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Iterations_Description">
            <summary>
              Looks up a localized string similar to Set the number of iterations in the point fitting step. A higher value makes the fitting more robust but increases the computation time..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Iterations_DisplayName">
            <summary>
              Looks up a localized string similar to Iterations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_KernelSize_Description">
            <summary>
              Looks up a localized string similar to The width and height of the filter kernel. Must be an odd number larger or equal to 3..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_KernelSize_DisplayName">
            <summary>
              Looks up a localized string similar to Kernel size.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Mode_Description">
            <summary>
              Looks up a localized string similar to The method used for fitting.
               • Ransac: a robust method with outlier rejection. 
               • Least square: a fast method but can produce poor results
                 if there are many spurious edge points nearby. When using
                 this method, the inlier rate will always be 100%. 
               • Trimmed: a two-stage least squares method, where points
                 further away from the fitted shape in the first round
                 than the outlier margin is removed before using Least
                 Squares again to fit a new shape to the remainin [rest of string was truncated]&quot;;.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Mode_DisplayName">
            <summary>
              Looks up a localized string similar to Fit mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_MorphologyMethod_Description">
            <summary>
              Looks up a localized string similar to Specifies the type of morphological operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_MorphologyMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OptionalEmptyVariable">
            <summary>
              Looks up a localized string similar to Will create an empty variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OuterRadius_Description">
            <summary>
              Looks up a localized string similar to The outer radius used for specifying the search region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OuterRadius_DisplayName">
            <summary>
              Looks up a localized string similar to Outer radius.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OutlierMargin_Description">
            <summary>
              Looks up a localized string similar to An outlier margin used to remove outliers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_OutlierMargin_DisplayName">
            <summary>
              Looks up a localized string similar to Outlier margin.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Outliers_Description">
            <summary>
              Looks up a localized string similar to All points that were rejected in the fitting algorithm..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Outliers_DisplayName">
            <summary>
              Looks up a localized string similar to Outliers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Polarity_Description">
            <summary>
              Looks up a localized string similar to The type of edges the probes should search. Sets the edge polarity in the probe direction.
               • Positive: goes from low to high pixel values.
               • Negative: goes from high to low pixel values.
               • Any: both positive and negative edges..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Polarity_DisplayName">
            <summary>
              Looks up a localized string similar to Edge polarity.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_ProbeCount_Description">
            <summary>
              Looks up a localized string similar to The number of edge probes, a high number will result in higher accuracy, but will increase computation time..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_ProbeCount_DisplayName">
            <summary>
              Looks up a localized string similar to Probe count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RectificationMethod_Description">
             <summary>
               Looks up a localized string similar to Defines how the rectification output in a bin is created from (potentially) multiple calibrated entries:
            • Mean - Pick the average of all points that end up in the same bin.
            • Bottommost - Always pick the lowest range value for each bin.
            • Topmost - Always pick the highest range value for each bin.
            • Brightest - Pick the range value with the brightest reflectance value.
            • Darkest - Pick the range value with the darkest reflectance value..
             </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RectificationMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Rectification method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RectificationSpreadSize_Description">
            <summary>
              Looks up a localized string similar to Kernel size parameter used when rectifying the point cloud..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RectificationSpreadSize_DisplayName">
            <summary>
              Looks up a localized string similar to Rectification spread.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RegionOfInterest_Description">
            <summary>
              Looks up a localized string similar to The region of interest in which the image operation is applied..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RegionOfInterest_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_RegionOfInterest_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_SearchDirection_Description">
            <summary>
              Looks up a localized string similar to The search direction, approximately across the edge. The defined edge polarity is interpreted relative to this direction. 0 degrees aligns with the X axis, and the positive direction is counterclockwise..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_SearchDirection_DisplayName">
            <summary>
              Looks up a localized string similar to Search direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Selection_Description">
            <summary>
              Looks up a localized string similar to Decides which edge point to select if there are several candidates available. The selection is related to the probe direction.
               • Strongest: selects the strongest edge along the probe.
               • First: selects the first edge along the probe.
               • Last: selects the last edge along the probe..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Selection_DisplayName">
            <summary>
              Looks up a localized string similar to Edge selection.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Side_Description">
            <summary>
              Looks up a localized string similar to Sets the edge side..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Side_DisplayName">
            <summary>
              Looks up a localized string similar to Edge side.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Threshold_Description">
            <summary>
              Looks up a localized string similar to The required edge step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_Threshold_DisplayName">
            <summary>
              Looks up a localized string similar to Threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_TranslationX_Description">
            <summary>
              Looks up a localized string similar to The translation in the X direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_TranslationX_DisplayName">
            <summary>
              Looks up a localized string similar to Translation X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_TranslationY_Description">
            <summary>
              Looks up a localized string similar to The translation in the Y direction..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Common_TranslationY_DisplayName">
            <summary>
              Looks up a localized string similar to Translation Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_ConcatenatedImage_Description">
            <summary>
              Looks up a localized string similar to  The concatenated image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_ConcatenatedImage_DisplayName">
            <summary>
              Looks up a localized string similar to Concatenated image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_Direction_Description">
            <summary>
              Looks up a localized string similar to The side of the Lead image to stitch the Trailing image to..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_Direction_DisplayName">
            <summary>
              Looks up a localized string similar to Direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_LeadImage_Description">
            <summary>
              Looks up a localized string similar to The image that the Trailing image will be stitched to..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_LeadImage_DisplayName">
            <summary>
              Looks up a localized string similar to Lead image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_TrailingImage_Description">
            <summary>
              Looks up a localized string similar to The image that will be stitched to the Lead image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateImages_TrailingImage_DisplayName">
            <summary>
              Looks up a localized string similar to Trailing image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_IncompatibleListTypesException">
            <summary>
              Looks up a localized string similar to The two input variables need to be of the same type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_InvalidVariableTypeException">
            <summary>
              Looks up a localized string similar to Invalid variable type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Result_Description">
            <summary>
              Looks up a localized string similar to The output variable will be of the same type as the input variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Result_DisplayName">
            <summary>
              Looks up a localized string similar to Result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Variable1_Description">
            <summary>
              Looks up a localized string similar to The variable that will be concatenated. The elements will be placed first in the output variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Variable1_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Variable2_Description">
            <summary>
              Looks up a localized string similar to The variable that will be concatenated. The elements will be placed last in the output variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ConcatenateVariables_Variable2_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Bounds_Description">
            <summary>
              Looks up a localized string similar to The region in which the planes coplanarity will be evaluated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Bounds_DisplayName">
            <summary>
              Looks up a localized string similar to Bounds.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Bounds_OptionalDescription">
            <summary>
              Looks up a localized string similar to Only the deviation angle can be calculated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_BoundsLengthMismatchedException">
            <summary>
              Looks up a localized string similar to The number of bounds do not match the number of planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_CenterDifference_Description">
            <summary>
              Looks up a localized string similar to The height difference between the two planes at the center of each bounds region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_CenterDifference_DisplayName">
            <summary>
              Looks up a localized string similar to Center difference.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_DeviationAngle_Description">
            <summary>
              Looks up a localized string similar to The angle between the plane normals..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_DeviationAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Deviation angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_MaxDifference_Description">
            <summary>
              Looks up a localized string similar to The maximum distance between the planes within each input region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_MaxDifference_DisplayName">
            <summary>
              Looks up a localized string similar to Max difference.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_MinDifference_Description">
            <summary>
              Looks up a localized string similar to The minimum distance between the planes within each input region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_MinDifference_DisplayName">
            <summary>
              Looks up a localized string similar to Min difference.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_MissingBoundsException">
            <summary>
              Looks up a localized string similar to Bounds is missing, but is required when calculating the differences..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_PlaneLengthMismatchedException">
            <summary>
              Looks up a localized string similar to Unsupported number of planes, the number of planes in Plane 1 and Plane 2 must be either one or equal..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Planes1_Description">
            <summary>
              Looks up a localized string similar to The first planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Planes1_DisplayName">
            <summary>
              Looks up a localized string similar to Planes 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Planes2_Description">
            <summary>
              Looks up a localized string similar to The second planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Planes2_DisplayName">
            <summary>
              Looks up a localized string similar to Planes 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Coplanarity_Planes2_OptionalDescription">
            <summary>
              Looks up a localized string similar to A plane at Z=0 will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Centers_Description">
            <summary>
              Looks up a localized string similar to The centers of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Ellipses_Description">
            <summary>
              Looks up a localized string similar to The output ellipse list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Ellipses_DisplayName">
            <summary>
              Looks up a localized string similar to Ellipses.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_RadiusX_Description">
            <summary>
              Looks up a localized string similar to The x radius of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_RadiusX_DisplayName">
            <summary>
              Looks up a localized string similar to Radius X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_RadiusY_Description">
            <summary>
              Looks up a localized string similar to The y radius of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_RadiusY_DisplayName">
            <summary>
              Looks up a localized string similar to Radius Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Rotations_Description">
            <summary>
              Looks up a localized string similar to The rotations of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Rotations_DisplayName">
            <summary>
              Looks up a localized string similar to Rotations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEllipse_Rotations_OptionalDescription">
            <summary>
              Looks up a localized string similar to The ellipses will have their rotation set to 0..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEmptyVariable_Variable_Description">
            <summary>
              Looks up a localized string similar to The new variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEmptyVariable_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEmptyVariable_VariableType_Description">
            <summary>
              Looks up a localized string similar to The type of the new variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateEmptyVariable_VariableType_DisplayName">
            <summary>
              Looks up a localized string similar to Variable type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_EndPoints_Description">
            <summary>
              Looks up a localized string similar to The end points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_EndPoints_DisplayName">
            <summary>
              Looks up a localized string similar to End points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_InvalidArgumentLength">
            <summary>
              Looks up a localized string similar to Either the arguments have to be equally long, or one of them needs to have the length 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_Lines_Description">
            <summary>
              Looks up a localized string similar to The output line list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_StartPoints_Description">
            <summary>
              Looks up a localized string similar to The start points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine2D_StartPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Start points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_EndPoints_Description">
            <summary>
              Looks up a localized string similar to The end points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_EndPoints_DisplayName">
            <summary>
              Looks up a localized string similar to End points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_Lines_Description">
            <summary>
              Looks up a localized string similar to The output line list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_StartPoints_Description">
            <summary>
              Looks up a localized string similar to The start points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateLine3D_StartPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Start points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumber_InputNumbers_Description">
            <summary>
              Looks up a localized string similar to A comma separated string with numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumber_InputNumbers_DisplayName">
            <summary>
              Looks up a localized string similar to Input numbers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumber_InvalidInputException">
            <summary>
              Looks up a localized string similar to The comma separated number list has an error..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumber_OutputNumbers_Description">
            <summary>
              Looks up a localized string similar to The output number list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumber_OutputNumbers_DisplayName">
            <summary>
              Looks up a localized string similar to Output numbers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_Count_Description">
            <summary>
              Looks up a localized string similar to The number of numbers in the range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_Count_DisplayName">
            <summary>
              Looks up a localized string similar to Count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_End_Description">
            <summary>
              Looks up a localized string similar to The last number of the range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_End_DisplayName">
            <summary>
              Looks up a localized string similar to End.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_OutputNumbers_Description">
            <summary>
              Looks up a localized string similar to The output number list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_OutputNumbers_DisplayName">
            <summary>
              Looks up a localized string similar to Output numbers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_Start_Description">
            <summary>
              Looks up a localized string similar to The first number of the range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateNumberRange_Start_DisplayName">
            <summary>
              Looks up a localized string similar to Start.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Bounds_Description">
            <summary>
              Looks up a localized string similar to The bounds of the planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Bounds_DisplayName">
            <summary>
              Looks up a localized string similar to Bounds.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Bounds_OptionalDescription">
            <summary>
              Looks up a localized string similar to The planes will have their bounds set to a rectangle from (0,0) to (1,1)..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Distances_Description">
            <summary>
              Looks up a localized string similar to The distances from origin of the planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Distances_DisplayName">
            <summary>
              Looks up a localized string similar to Distances.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_NormalVectors_Description">
            <summary>
              Looks up a localized string similar to The normal vectors of the planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_NormalVectors_DisplayName">
            <summary>
              Looks up a localized string similar to Normal vectors.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Planes_Description">
            <summary>
              Looks up a localized string similar to The output plane list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePlane_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint_InterpolationPointsInvalidLengthException">
            <summary>
              Looks up a localized string similar to The interpolation points needs to be either length 0, 1 or one less than the length of the input coords..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_InterpolationPoints_Description">
            <summary>
              Looks up a localized string similar to Adds a number of interpolated points between the coordinate positions, can be a single value or a list with the same length as the coordinate lists minus one..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_InterpolationPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Interpolation points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_InterpolationPoints_OptionalDescription">
            <summary>
              Looks up a localized string similar to No extra points will be added..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_Points_Description">
            <summary>
              Looks up a localized string similar to The output point list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_XCoordinates_Description">
            <summary>
              Looks up a localized string similar to The x coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_XCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to X coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_YCoordinates_Description">
            <summary>
              Looks up a localized string similar to The y coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint2D_YCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Y coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_InterpolationPoints_Description">
            <summary>
              Looks up a localized string similar to Adds a number of interpolated points between the coordinate positions, can be a single value or a list with the same length as the coordinate lists minus one..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_InterpolationPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Interpolation points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_InterpolationPoints_OptionalDescription">
            <summary>
              Looks up a localized string similar to No extra points will be added..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_Points_Description">
            <summary>
              Looks up a localized string similar to The output point list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_XCoordinates_Description">
            <summary>
              Looks up a localized string similar to The X coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_XCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to X coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_YCoordinates_Description">
            <summary>
              Looks up a localized string similar to The Y coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_YCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Y coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_ZCoordinates_Description">
            <summary>
              Looks up a localized string similar to The Z coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePoint3D_ZCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Z coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Angle_Description">
            <summary>
              Looks up a localized string similar to The angle of the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Angle_DisplayName">
            <summary>
              Looks up a localized string similar to Angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Corner_Description">
            <summary>
              Looks up a localized string similar to The top left corner of the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Corner_DisplayName">
            <summary>
              Looks up a localized string similar to Corner.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_CountX_Description">
            <summary>
              Looks up a localized string similar to The number of columns in the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_CountX_DisplayName">
            <summary>
              Looks up a localized string similar to Count X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_CountY_Description">
            <summary>
              Looks up a localized string similar to The number of rows in the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_CountY_DisplayName">
            <summary>
              Looks up a localized string similar to Count Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_DistanceX_Description">
            <summary>
              Looks up a localized string similar to The distance between the columns..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_DistanceX_DisplayName">
            <summary>
              Looks up a localized string similar to Distance X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_DistanceY_Description">
            <summary>
              Looks up a localized string similar to The distance between the rows..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_DistanceY_DisplayName">
            <summary>
              Looks up a localized string similar to Distance Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Points_Description">
            <summary>
              Looks up a localized string similar to The grid points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreatePointGrid_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateProfile_Points_Description">
            <summary>
              Looks up a localized string similar to The 3D points which will be the sample points of the new profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateProfile_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateProfile_Profile_Description">
            <summary>
              Looks up a localized string similar to The newly created profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateProfile_Profile_DisplayName">
            <summary>
              Looks up a localized string similar to Profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Centers_Description">
            <summary>
              Looks up a localized string similar to The centers of the rectangles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Heights_Description">
            <summary>
              Looks up a localized string similar to The heights of the rectangles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Heights_DisplayName">
            <summary>
              Looks up a localized string similar to Heights.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Rectangles_Description">
            <summary>
              Looks up a localized string similar to The output rectangle list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Rectangles_DisplayName">
            <summary>
              Looks up a localized string similar to Rectangles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Rotations_Description">
            <summary>
              Looks up a localized string similar to The rotations of the rectangles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Rotations_DisplayName">
            <summary>
              Looks up a localized string similar to Rotations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Rotations_OptionalDescription">
            <summary>
              Looks up a localized string similar to The rectangles will have their rotation set to 0..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Widths_Description">
            <summary>
              Looks up a localized string similar to The widths of the rectangles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRectangle_Widths_DisplayName">
            <summary>
              Looks up a localized string similar to Widths.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Height_Description">
            <summary>
              Looks up a localized string similar to The height of the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Height_DisplayName">
            <summary>
              Looks up a localized string similar to Height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_InvalidArgumentLengthException">
            <summary>
              Looks up a localized string similar to Invalid length of input variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_OutputRegion_Description">
            <summary>
              Looks up a localized string similar to The output pixel region list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_OutputRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Output region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Width_Description">
            <summary>
              Looks up a localized string similar to The width of the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Width_DisplayName">
            <summary>
              Looks up a localized string similar to Width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_X_Description">
            <summary>
              Looks up a localized string similar to The X pixel coordinate of the top left corner..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_X_DisplayName">
            <summary>
              Looks up a localized string similar to X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Y_Description">
            <summary>
              Looks up a localized string similar to The Y pixel coordinate of the top left corner..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegion_Y_DisplayName">
            <summary>
              Looks up a localized string similar to Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_Height_Description">
            <summary>
              Looks up a localized string similar to The height of the region of interest..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_Height_DisplayName">
            <summary>
              Looks up a localized string similar to Height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_Width_Description">
            <summary>
              Looks up a localized string similar to The width of the region of interest..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_Width_DisplayName">
            <summary>
              Looks up a localized string similar to Width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_X_Description">
            <summary>
              Looks up a localized string similar to The X coordinate of the top left corner of the region of interest..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateRegionOfInterest_Y_Description">
            <summary>
              Looks up a localized string similar to The Y coordinate of the top left corner of the region of interest..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateString_String_Description">
            <summary>
              Looks up a localized string similar to The input string value..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateString_String_DisplayName">
            <summary>
              Looks up a localized string similar to String.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateString_StringVariable_Description">
            <summary>
              Looks up a localized string similar to The output string variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateString_StringVariable_DisplayName">
            <summary>
              Looks up a localized string similar to String variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Position_Description">
            <summary>
              Looks up a localized string similar to The position of the string..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Position_DisplayName">
            <summary>
              Looks up a localized string similar to Position.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Text_Description">
            <summary>
              Looks up a localized string similar to The resulting Text variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Text_DisplayName">
            <summary>
              Looks up a localized string similar to Text.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_TextContentEmptyException">
            <summary>
              Looks up a localized string similar to The Title and Variable arguments are both empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_TextUnsupportedVariableTypeException">
            <summary>
              Looks up a localized string similar to Unsupported variable type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Title_Description">
            <summary>
              Looks up a localized string similar to The first part of the text content..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Title_DisplayName">
            <summary>
              Looks up a localized string similar to Title.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Title_OptionalDescription">
            <summary>
              Looks up a localized string similar to The text content will not include any title..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Variable_Description">
            <summary>
              Looks up a localized string similar to The second part of the text content..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_Variable_OptionalDescription">
            <summary>
              Looks up a localized string similar to The text content will not include any variable info..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateText_VariableLengthMismatchedException">
            <summary>
              Looks up a localized string similar to Unsupported variable lengths..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_AffineMatrix_Description">
            <summary>
              Looks up a localized string similar to The affine part of the transform, a 2x2 matrix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_AffineMatrix_DisplayName">
            <summary>
              Looks up a localized string similar to Affine matrix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_RotationCenter_Description">
            <summary>
              Looks up a localized string similar to The rotation center.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_RotationCenter_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation center.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_RotationCenter_OptionalDescription">
            <summary>
              Looks up a localized string similar to The rotation center will be (0 0).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_Transform_Description">
            <summary>
              Looks up a localized string similar to The created transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_Transform_DisplayName">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_TransformType_Description">
            <summary>
              Looks up a localized string similar to The type of transform to be created. A rigid transform preserves shape and size, while an affine transform can consist of any linear transform and a translation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform2D_TransformType_DisplayName">
            <summary>
              Looks up a localized string similar to Transform type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AffineMatrix_Description">
            <summary>
              Looks up a localized string similar to The affine part of the transform, a 3x3 matrix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AffineMatrix_DisplayName">
            <summary>
              Looks up a localized string similar to Affine matrix.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisCannotBe0">
            <summary>
              Looks up a localized string similar to Axis cannot be a 0 vector.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisX_Description">
            <summary>
              Looks up a localized string similar to The X coordinate of the rotation axis.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisX_DisplayName">
            <summary>
              Looks up a localized string similar to Axis X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisY_Description">
            <summary>
              Looks up a localized string similar to The Y coordinate of the rotation axis.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisY_DisplayName">
            <summary>
              Looks up a localized string similar to Axis Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisZ_Description">
            <summary>
              Looks up a localized string similar to The Z coordinate of the rotation axis.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_AxisZ_DisplayName">
            <summary>
              Looks up a localized string similar to Axis Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationCenter_Description">
            <summary>
              Looks up a localized string similar to The rotation center.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationCenter_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation center.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationCenter_OptionalDescription">
            <summary>
              Looks up a localized string similar to The rotation center will be (0 0 0).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationX_Description">
            <summary>
              Looks up a localized string similar to The rotation around the X axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationX_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationY_Description">
            <summary>
              Looks up a localized string similar to The rotation around the Y axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationY_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationZ_Description">
            <summary>
              Looks up a localized string similar to The rotation around the Z axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_RotationZ_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_Transform_Description">
            <summary>
              Looks up a localized string similar to The created transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_Transform_DisplayName">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_TransformType_Description">
            <summary>
              Looks up a localized string similar to The type of transform to be created. A rigid transform preserves shape and size, while an affine transform can consist of any linear transform and a translation. Rigid transforms can either be defined with a rotation axis and angle, or through a set of Euler angles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_TransformType_DisplayName">
            <summary>
              Looks up a localized string similar to Transform type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_TranslationZ_Description">
            <summary>
              Looks up a localized string similar to The translation in the Z direction..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateTransform3D_TranslationZ_DisplayName">
            <summary>
              Looks up a localized string similar to Translation Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateVariable_ArgumentLengthMismatchException">
            <summary>
              Looks up a localized string similar to Invalid length of input variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CreateVariable_InvalidArgumentException">
            <summary>
              Looks up a localized string similar to When creating an empty variable with empty arguments all arguments have to be empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_CroppedImage_Description">
            <summary>
              Looks up a localized string similar to The cropped image. Will have the same world coordinates as in the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_CroppedImage_DisplayName">
            <summary>
              Looks up a localized string similar to Cropped image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_InvalidRotationException">
            <summary>
              Looks up a localized string similar to Cannot crop using a rotated rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MaxRange_Description">
            <summary>
              Looks up a localized string similar to Maximum range value to be kept in the cropped image. The bound is inclusive..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MaxRange_DisplayName">
            <summary>
              Looks up a localized string similar to Max range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MaxRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to Upper bound is not set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MinRange_Description">
            <summary>
              Looks up a localized string similar to Minimum range value to be kept in the cropped image. The bound is inclusive..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MinRange_DisplayName">
            <summary>
              Looks up a localized string similar to Min range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_MinRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to Lower bound is not set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_Rectangle_Description">
            <summary>
              Looks up a localized string similar to The area to be cropped..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_Rectangle_DisplayName">
            <summary>
              Looks up a localized string similar to Rectangle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_Rectangle_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole width and height of the image is kept..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_RectanglesOutOfRangeException">
            <summary>
              Looks up a localized string similar to Can only crop using a single rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropImage_SourceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to The image on which the operation will be performed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_ClampZ_Description">
            <summary>
              Looks up a localized string similar to If checked, the Z values will be clamped between Min Z and Max Z. If unchecked, the Z values will be replaced with missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_ClampZ_DisplayName">
            <summary>
              Looks up a localized string similar to Clamp Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_CroppedProfile_Description">
            <summary>
              Looks up a localized string similar to The cropped profile. .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_CroppedProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Cropped profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_EndIndex_Description">
            <summary>
              Looks up a localized string similar to The index of the last sample to keep in the cropped profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_EndIndex_DisplayName">
            <summary>
              Looks up a localized string similar to End index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_EndIndex_OptionalDescription">
            <summary>
              Looks up a localized string similar to No end index is set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_InvalidPositionException">
            <summary>
              Looks up a localized string similar to Start index must be smaller than End index..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_InvalidRangeException">
            <summary>
              Looks up a localized string similar to Min Z must be smaller than Max Z..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MaxZ_Description">
            <summary>
              Looks up a localized string similar to The maximum Z value to keep in the cropped profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MaxZ_DisplayName">
            <summary>
              Looks up a localized string similar to Max Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MaxZ_OptionalDescription">
            <summary>
              Looks up a localized string similar to No upper bound is set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MinZ_Description">
            <summary>
              Looks up a localized string similar to The minimum Z value to keep in the cropped profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MinZ_DisplayName">
            <summary>
              Looks up a localized string similar to Min Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_MinZ_OptionalDescription">
            <summary>
              Looks up a localized string similar to No lower bound is set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_Profile_Description">
            <summary>
              Looks up a localized string similar to The profile to crop..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_Profile_DisplayName">
            <summary>
              Looks up a localized string similar to Profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_StartIndex_Description">
            <summary>
              Looks up a localized string similar to The index of the first sample to keep in the cropped profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_StartIndex_DisplayName">
            <summary>
              Looks up a localized string similar to Start index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.CropProfile_StartIndex_OptionalDescription">
            <summary>
              Looks up a localized string similar to No start index is set..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_DotDistanceX_Description">
            <summary>
              Looks up a localized string similar to The distance between two adjacent dots on the same row..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_DotDistanceX_DisplayName">
            <summary>
              Looks up a localized string similar to Dot distance X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_DotDistanceZ_Description">
            <summary>
              Looks up a localized string similar to The height distance between two dots on adjacent rows. The parameter can be positive or negative depending on the orientation of the target and the scan direction. If the parameter is negative the origin is located at the bottom left corner of the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_DotDistanceZ_DisplayName">
            <summary>
              Looks up a localized string similar to Dot distance Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_TranslationX_Description">
            <summary>
              Looks up a localized string similar to X translation of the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_TranslationX_DisplayName">
            <summary>
              Looks up a localized string similar to X translation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_TranslationZ_Description">
            <summary>
              Looks up a localized string similar to Z translation of the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_TranslationZ_DisplayName">
            <summary>
              Looks up a localized string similar to Z translation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_XAxisDirection_Description">
            <summary>
              Looks up a localized string similar to The direction of the X axis in the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_XAxisDirection_DisplayName">
            <summary>
              Looks up a localized string similar to X axis direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_ZAxisDirection_Description">
            <summary>
              Looks up a localized string similar to The direction of the Z axis in the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineDotWedgeWorldPoints_ZAxisDirection_DisplayName">
            <summary>
              Looks up a localized string similar to Z axis direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ToothHeight_Description">
            <summary>
              Looks up a localized string similar to The tooth height of the SawTooth target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ToothHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Tooth height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ToothWidth_Description">
            <summary>
              Looks up a localized string similar to The tooth width of the SawTooth target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ToothWidth_DisplayName">
            <summary>
              Looks up a localized string similar to Tooth width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_TranslationX_Description">
            <summary>
              Looks up a localized string similar to X translation of the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_TranslationX_DisplayName">
            <summary>
              Looks up a localized string similar to X translation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_TranslationZ_Description">
            <summary>
              Looks up a localized string similar to Z translation of the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_TranslationZ_DisplayName">
            <summary>
              Looks up a localized string similar to Z translation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_XAxisDirection_Description">
            <summary>
              Looks up a localized string similar to The direction of the X axis in the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_XAxisDirection_DisplayName">
            <summary>
              Looks up a localized string similar to X axis direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ZAxisDirection_Description">
            <summary>
              Looks up a localized string similar to The direction of the Z axis in the world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineSawToothWorldPoints_ZAxisDirection_DisplayName">
            <summary>
              Looks up a localized string similar to Z axis direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceX_Description">
            <summary>
              Looks up a localized string similar to The distance between each point in the X direction..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceX_DisplayName">
            <summary>
              Looks up a localized string similar to Distance X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceY_Description">
            <summary>
              Looks up a localized string similar to The distance between each point in the Y direction..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceY_DisplayName">
            <summary>
              Looks up a localized string similar to Distance Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceZ_Description">
            <summary>
              Looks up a localized string similar to The height difference between the points on two adjacent rows. The target type defines how the Z distance is applied to the target points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_DistanceZ_DisplayName">
            <summary>
              Looks up a localized string similar to Distance Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_GridLayout_Description">
            <summary>
              Looks up a localized string similar to (X, Y) indices of the grid positions to use when defining the target points. .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_GridLayout_DisplayName">
            <summary>
              Looks up a localized string similar to Grid layout.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_Origin_Description">
            <summary>
              Looks up a localized string similar to The origin of the target point grid. All other points will be located in the positive X and Y direction in relation to this point..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_Origin_DisplayName">
            <summary>
              Looks up a localized string similar to Origin.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_Origin_OptionalDescription">
            <summary>
              Looks up a localized string similar to The origin of the grid will be (0, 0, 0)..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_OriginLengthException">
            <summary>
              Looks up a localized string similar to Origin needs to be a single point, but multiple points were provided..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_TargetPoints_Description">
            <summary>
              Looks up a localized string similar to A world coordinate representation of a calibration target. .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_TargetPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Target points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_TargetType_Description">
            <summary>
              Looks up a localized string similar to The type of calibration or alignment target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DefineTargetPoints_TargetType_DisplayName">
            <summary>
              Looks up a localized string similar to Target type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DeleteVariables_InvalidVariableNameException">
            <summary>
              Looks up a localized string similar to {0}: The variable(s) {1} does not exist..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DeleteVariables_VariableNames_Description">
            <summary>
              Looks up a localized string similar to A comma separated list containing the names of the variables to be removed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.DeleteVariables_VariableNames_DisplayName">
            <summary>
              Looks up a localized string similar to Variable names.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance_InvalidVariableLenghtExcepetion">
            <summary>
              Looks up a localized string similar to Points and Variable2 must either have the same length, or one of the variables must have length 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Distances_Description">
            <summary>
              Looks up a localized string similar to The distance between the two variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Distances_DisplayName">
            <summary>
              Looks up a localized string similar to Distances.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_LineIntersection_Description">
            <summary>
              Looks up a localized string similar to Optional result when calculating the distance between a Point2D and a Line2D. This is the closest 2D-point on the line relative to the input point..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_LineIntersection_DisplayName">
            <summary>
              Looks up a localized string similar to Line intersection.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Points_Description">
            <summary>
              Looks up a localized string similar to The first variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Variable2_Description">
            <summary>
              Looks up a localized string similar to The second variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance2D_Variable2_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Distances_Description">
            <summary>
              Looks up a localized string similar to The distance between the two variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Distances_DisplayName">
            <summary>
              Looks up a localized string similar to Distances.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_PointProjection_Description">
            <summary>
              Looks up a localized string similar to This is the projection of the input point on the second variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_PointProjection_DisplayName">
            <summary>
              Looks up a localized string similar to Point projection.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Points_Description">
            <summary>
              Looks up a localized string similar to The first variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Variable2_Description">
            <summary>
              Looks up a localized string similar to The second variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Distance3D_Variable2_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Ellipses_Description">
            <summary>
              Looks up a localized string similar to The input ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Ellipses_DisplayName">
            <summary>
              Looks up a localized string similar to Ellipses.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Image_Description">
            <summary>
              Looks up a localized string similar to Image used to transform the pixel regions to its own coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Image_DisplayName">
            <summary>
              Looks up a localized string similar to Image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Image_OptionalDescription">
            <summary>
              Looks up a localized string similar to The pixel regions will be in a pixel coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_RadiusX_Description">
            <summary>
              Looks up a localized string similar to The x radius of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_RadiusX_DisplayName">
            <summary>
              Looks up a localized string similar to Radius X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_RadiusY_Description">
            <summary>
              Looks up a localized string similar to The y radius of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_RadiusY_DisplayName">
            <summary>
              Looks up a localized string similar to Radius Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Regions_Description">
            <summary>
              Looks up a localized string similar to The pixel region that makes up the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Rotations_Description">
            <summary>
              Looks up a localized string similar to The rotation of the ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EllipseInfo_Rotations_DisplayName">
            <summary>
              Looks up a localized string similar to Rotations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_IntegerResult_Description">
            <summary>
              Looks up a localized string similar to The result of the operation as an integer. Either 1 if true or 0 if false..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_IntegerResult_DisplayName">
            <summary>
              Looks up a localized string similar to Integer result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_Variable1_Description">
            <summary>
              Looks up a localized string similar to A variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_Variable1_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_Variable2_Description">
            <summary>
              Looks up a localized string similar to A second variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_Variable2_DisplayName">
            <summary>
              Looks up a localized string similar to Variable 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Equals_VariableTypeMismatchedException">
            <summary>
              Looks up a localized string similar to The variable types are not the same..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_AlignedPoints_Description">
            <summary>
              Looks up a localized string similar to The input points transformed to the world coordinate system. These are useful for visualizing that the alignment is correct..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_AlignedPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Aligned points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_AlignmentModel_Description">
            <summary>
              Looks up a localized string similar to The estimated model. It contains the input calibration model plus the estimated alignment transform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_AlignmentModel_DisplayName">
            <summary>
              Looks up a localized string similar to Alignment model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_CalibratedPoints_Description">
            <summary>
              Looks up a localized string similar to A set of points defined in the calibrated coordinate system of the device..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_CalibratedPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Calibrated points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_CalibrationModel_Description">
            <summary>
              Looks up a localized string similar to The calibration model that was used to calibrate the calibrated input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_CalibrationModel_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_SkewMode_Description">
            <summary>
              Looks up a localized string similar to Specifies how the camera was calibrated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_SkewMode_DisplayName">
            <summary>
              Looks up a localized string similar to Skew mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_WorldPoints_Description">
            <summary>
              Looks up a localized string similar to A set of points defined in an external world coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateAlignment_WorldPoints_DisplayName">
            <summary>
              Looks up a localized string similar to World points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_CalibrationModel_Description">
            <summary>
              Looks up a localized string similar to The estimated calibration model..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_CalibrationModel_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_LaserCurve_Description">
            <summary>
              Looks up a localized string similar to If checked, laser curve compensation will be included in the estimation...
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_LaserCurve_DisplayName">
            <summary>
              Looks up a localized string similar to Laser curve compensation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_MaxPointError_Description">
            <summary>
              Looks up a localized string similar to If there are point errors with a magnitude larger than this value, the calibration is reestimated without those points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_MaxPointError_DisplayName">
            <summary>
              Looks up a localized string similar to Max point error.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_MaxPointError_OptionalDescription">
            <summary>
              Looks up a localized string similar to The calibration model is not reestimated. .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_PixelPoints_Description">
            <summary>
              Looks up a localized string similar to One or multiple Point3D variables containing pixel positions of the calibration target, on the sensor..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_PixelPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Pixel points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_PointErrors_Description">
            <summary>
              Looks up a localized string similar to The error in millimeters for each input point pair..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_PointErrors_DisplayName">
            <summary>
              Looks up a localized string similar to Point errors.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_RadialOptimization_Description">
            <summary>
              Looks up a localized string similar to The number of radial distortion coefficients to use in the lens model..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_RadialOptimization_DisplayName">
            <summary>
              Looks up a localized string similar to Radial optimization.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_Scheimpflug_Description">
            <summary>
              Looks up a localized string similar to Should be checked if a Scheimpflug adapter is being used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_Scheimpflug_DisplayName">
            <summary>
              Looks up a localized string similar to Scheimpflug.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_SensorHeight_Description">
            <summary>
              Looks up a localized string similar to The height of the sensor..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_SensorHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Sensor height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_SensorWidth_Description">
            <summary>
              Looks up a localized string similar to The width of the sensor..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_SensorWidth_DisplayName">
            <summary>
              Looks up a localized string similar to Sensor width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_TangentialOptimization_Description">
            <summary>
              Looks up a localized string similar to The number of tangential distortion coefficients to use in the lens model..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_TangentialOptimization_DisplayName">
            <summary>
              Looks up a localized string similar to Tangential optimization.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_WorldPoints_Description">
            <summary>
              Looks up a localized string similar to The corresponding world positions for each pixel point..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateCalibration_WorldPoints_DisplayName">
            <summary>
              Looks up a localized string similar to World points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_AlignmentModel1_Description">
            <summary>
              Looks up a localized string similar to The estimated alignment model for camera 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_AlignmentModel1_DisplayName">
            <summary>
              Looks up a localized string similar to Alignment model 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_AlignmentModel2_Description">
            <summary>
              Looks up a localized string similar to The estimated alignment model for camera 2..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_AlignmentModel2_DisplayName">
            <summary>
              Looks up a localized string similar to Alignment model 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CalibrationModel1_Description">
            <summary>
              Looks up a localized string similar to The calibration model for camera 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CalibrationModel1_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CalibrationModel2_Description">
            <summary>
              Looks up a localized string similar to The calibration model for camera 2..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CalibrationModel2_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CoordinateAxes_Description">
            <summary>
              Looks up a localized string similar to Axes defined in the deskewed estimate image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_CoordinateAxes_DisplayName">
            <summary>
              Looks up a localized string similar to Coordinate axes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_Coordinates_Description">
            <summary>
              Looks up a localized string similar to The pyramid coordinates transformed to the coordinate system defined by the estimate axes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_Coordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimatePlanes1_Description">
            <summary>
              Looks up a localized string similar to The pyramid planes from the estimate image of camera 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimatePlanes1_DisplayName">
            <summary>
              Looks up a localized string similar to Estimate planes 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimatePlanes2_Description">
            <summary>
              Looks up a localized string similar to The pyramid planes from the estimate image of camera 2..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimatePlanes2_DisplayName">
            <summary>
              Looks up a localized string similar to Estimate planes 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimationError_Description">
            <summary>
              Looks up a localized string similar to Estimation error calculated by comparing the planes from the pyramid corners to the input estimate planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_EstimationError_DisplayName">
            <summary>
              Looks up a localized string similar to Estimation error.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_ICPTransform_Description">
            <summary>
              Looks up a localized string similar to Optional transform added to the second alignment model as a refinement. This transform can be estimated using ICP..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_ICPTransform_DisplayName">
            <summary>
              Looks up a localized string similar to ICP transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_ICPTransform_OptionalDescription">
            <summary>
              Looks up a localized string similar to No additional refinement is added to the alignment..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_MeasurePlanes_Description">
            <summary>
              Looks up a localized string similar to The pyramid planes from the measure image of camera 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_MeasurePlanes_DisplayName">
            <summary>
              Looks up a localized string similar to Measure planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_MirrorX_Description">
            <summary>
              Looks up a localized string similar to Should be selected if camera 2 is mounted reversed compared to camera 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_MirrorX_DisplayName">
            <summary>
              Looks up a localized string similar to Mirror X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidAngle_Description">
            <summary>
              Looks up a localized string similar to The angle between the pyramid planes and the vertical axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidCorners1_Description">
            <summary>
              Looks up a localized string similar to Points calculated from the measure planes and the pyramid target dimensions. For an ideal estimation the points should be located at the corners of pyramid in the estimate image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidCorners1_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid corners 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidCorners2_Description">
            <summary>
              Looks up a localized string similar to Points calculated from the measure planes and the pyramid target dimensions. For an ideal estimation the points should be located at the corners of pyramid in the estimate image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidCorners2_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid corners 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidEdges1_Description">
            <summary>
              Looks up a localized string similar to Lines between the pyramid corner points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidEdges1_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid edges 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidEdges2_Description">
            <summary>
              Looks up a localized string similar to Lines between the pyramid corner points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidEdges2_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid edges 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidIndex_Description">
            <summary>
              Looks up a localized string similar to The pyramid index for camera 2..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidIndex_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidSideLength_Description">
            <summary>
              Looks up a localized string similar to The side length of the pyramid target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSideBySide_PyramidSideLength_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid side length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_AlignmentModel_Description">
            <summary>
              Looks up a localized string similar to The estimated alignment model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_AlignmentModel_DisplayName">
            <summary>
              Looks up a localized string similar to Alignment model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_CalibrationModel_Description">
            <summary>
              Looks up a localized string similar to The calibration model for the camera..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_CalibrationModel_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_CreateDeviceModel_Description">
            <summary>
              Looks up a localized string similar to If enabled the alignment model will be modified for partially alignment on device..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_CreateDeviceModel_DisplayName">
            <summary>
              Looks up a localized string similar to Create device model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_Planes_Description">
            <summary>
              Looks up a localized string similar to The pyramid planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_PyramidAngle_Description">
            <summary>
              Looks up a localized string similar to The angle between the pyramid planes and the vertical axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_PyramidAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_PyramidSideLength_Description">
            <summary>
              Looks up a localized string similar to The side length of the pyramid target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.EstimateSkew_PyramidSideLength_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid side length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_AppendNumber_Description">
            <summary>
              Looks up a localized string similar to If true a number will be appended to the file name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_AppendNumber_DisplayName">
            <summary>
              Looks up a localized string similar to Append number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileFormat_Description">
            <summary>
              Looks up a localized string similar to The format used when saving the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileFormat_DisplayName">
            <summary>
              Looks up a localized string similar to File format.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileName_Description">
            <summary>
              Looks up a localized string similar to The name of the file to be saved..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileName_DisplayName">
            <summary>
              Looks up a localized string similar to File name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileNumber_Description">
            <summary>
              Looks up a localized string similar to The number that will be appended to the file name if Append number is enabled. The number is increased every time the step runs..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_FileNumber_DisplayName">
            <summary>
              Looks up a localized string similar to File number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_Folder_Description">
            <summary>
              Looks up a localized string similar to The path to the folder where the image will be saved..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_Folder_DisplayName">
            <summary>
              Looks up a localized string similar to Folder.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_RescaleImage_Description">
            <summary>
              Looks up a localized string similar to If true the image will be resized using the x and y scales..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_RescaleImage_DisplayName">
            <summary>
              Looks up a localized string similar to Rescale image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_UnsupportedVariableTypeException">
            <summary>
              Looks up a localized string similar to Unsupported variable type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_VariableNames_Description">
            <summary>
              Looks up a localized string similar to A comma separated list containing the names of the variables to be added as overlay..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_VariableNames_DisplayName">
            <summary>
              Looks up a localized string similar to Variable names.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportFeedbackImage_VariableNames_OptionalDescription">
            <summary>
              Looks up a localized string similar to The image will be saved without any variable overlay..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_AppendNumber_Description">
            <summary>
              Looks up a localized string similar to If true, a number will be appended to the file name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_AppendNumber_DisplayName">
            <summary>
              Looks up a localized string similar to Append number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileFormat_Description">
            <summary>
              Looks up a localized string similar to The format used when saving the file..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileFormat_DisplayName">
            <summary>
              Looks up a localized string similar to File format.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileName_Description">
            <summary>
              Looks up a localized string similar to The name of the file to save..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileName_DisplayName">
            <summary>
              Looks up a localized string similar to File name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileNumber_Description">
            <summary>
              Looks up a localized string similar to The number that will be appended to the file name if Append number is enabled. The number is increased every time the step runs..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileNumber_DisplayName">
            <summary>
              Looks up a localized string similar to File number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileWriteMode_Description">
            <summary>
              Looks up a localized string similar to The mode used when writing to the file. Overwrite overwrites any existing data in the file. Append adds the variable to the end of the file..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_FileWriteMode_DisplayName">
            <summary>
              Looks up a localized string similar to File write mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_Folder_Description">
            <summary>
              Looks up a localized string similar to The path to the folder where the file will be saved..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_Folder_DisplayName">
            <summary>
              Looks up a localized string similar to Folder.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_Variable_Description">
            <summary>
              Looks up a localized string similar to The variable to export..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExportVariable_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Blue_Description">
            <summary>
              Looks up a localized string similar to The blue channel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Blue_DisplayName">
            <summary>
              Looks up a localized string similar to Blue.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Green_Description">
            <summary>
              Looks up a localized string similar to The green channel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Green_DisplayName">
            <summary>
              Looks up a localized string similar to Green.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Red_Description">
            <summary>
              Looks up a localized string similar to The red channel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_Red_DisplayName">
            <summary>
              Looks up a localized string similar to Red.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractColorChannels_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_ExtractedElements_Description">
            <summary>
              Looks up a localized string similar to The extracted elements..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_ExtractedElements_DisplayName">
            <summary>
              Looks up a localized string similar to Extracted elements.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_Indices_Description">
            <summary>
              Looks up a localized string similar to The indices of the elements to extract, e.g. 0,1,3,5.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_Indices_DisplayName">
            <summary>
              Looks up a localized string similar to Indices.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_Variable_Description">
            <summary>
              Looks up a localized string similar to The variable to extract elements from..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractFromIndices_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_DetectionThreshold_Description">
            <summary>
              Looks up a localized string similar to How bright the laser line has to be in order to be considered as a detection..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_DetectionThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Detection threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_ExtractionMethod_Description">
            <summary>
              Looks up a localized string similar to The extraction method. The M30 option mimics the behavior of the M30 sensor, and Hi3D more accurately finds the center of the laser line..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_ExtractionMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Extraction method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakPoints_Description">
            <summary>
              Looks up a localized string similar to The peak points represented as a Point2D list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Peak points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakProfile_Description">
            <summary>
              Looks up a localized string similar to The peak points represented as a profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Peak profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakRegion_Description">
            <summary>
              Looks up a localized string similar to The peak points represented as a region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Peak region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakThreshold_Description">
            <summary>
              Looks up a localized string similar to The value at which to cap the intensity values when finding the WAM window..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_PeakThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Peak threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_Region_Description">
            <summary>
              Looks up a localized string similar to The region used for locating the laser line. The algorithm is using multiple pixels on each column in order to get subpixel accuracy. Make sure the region covers the line with at least 8 pixels to spare vertically. Only the region&apos;s bounding box will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_Region_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SearchDirection_Description">
            <summary>
              Looks up a localized string similar to The direction along each column to search for the peak..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SearchDirection_DisplayName">
            <summary>
              Looks up a localized string similar to Search direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SearchMode_Description">
            <summary>
              Looks up a localized string similar to The search mode for finding the peak..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SearchMode_DisplayName">
            <summary>
              Looks up a localized string similar to Search mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The image input. Must contain an Intensity component (sensor image)..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_WamSize_Description">
            <summary>
              Looks up a localized string similar to The size of the area around the peak to consider (Window Around Maximum size).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractPeak_WamSize_DisplayName">
            <summary>
              Looks up a localized string similar to Wam size.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_Lines_Description">
            <summary>
              Looks up a localized string similar to The line(s) that are used to calculate the profile points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MaxRange_Description">
            <summary>
              Looks up a localized string similar to Range threshold, range values above this threshold will be treated in the same way as missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MaxRange_DisplayName">
            <summary>
              Looks up a localized string similar to Max range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MaxRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to No upper range limit..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MinRange_Description">
            <summary>
              Looks up a localized string similar to Range threshold, range values below this threshold will be treated in the same way as missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MinRange_DisplayName">
            <summary>
              Looks up a localized string similar to Min range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_MinRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to No lower range limit..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_NumberOfSamples_Description">
            <summary>
              Looks up a localized string similar to The number of samples to be included in the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_NumberOfSamples_DisplayName">
            <summary>
              Looks up a localized string similar to Number of samples.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_Profiles_Description">
            <summary>
              Looks up a localized string similar to A list of the extracted profile(s)..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_Profiles_DisplayName">
            <summary>
              Looks up a localized string similar to Profiles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_SampleResolution_Description">
            <summary>
              Looks up a localized string similar to The resolution of the sample points along the input line..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_SampleResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Sample resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_SamplingMethod_Description">
            <summary>
              Looks up a localized string similar to Selects the sampling method to use when extracting the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractProfile_SamplingMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Sampling method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_ExtractedElements_Description">
            <summary>
              Looks up a localized string similar to The extracted elements..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_ExtractedElements_DisplayName">
            <summary>
              Looks up a localized string similar to Extracted elements.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Index_Description">
            <summary>
              Looks up a localized string similar to The index of the element to extract..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Index_DisplayName">
            <summary>
              Looks up a localized string similar to Index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Length_Description">
            <summary>
              Looks up a localized string similar to The number of elements to extract..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Length_DisplayName">
            <summary>
              Looks up a localized string similar to Length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Variable_Description">
            <summary>
              Looks up a localized string similar to The variable to extract elements from..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ExtractSubset_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The output image, containing the result of the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_KernelSize_Description">
            <summary>
              Looks up a localized string similar to The number of iterations when using the EDGE method..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_Method_Description">
            <summary>
              Looks up a localized string similar to The filtering method to use when filling the missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_Method_DisplayName">
            <summary>
              Looks up a localized string similar to Method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_Region_Description">
            <summary>
              Looks up a localized string similar to The region in which the missing data will be filled..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillMissingData_Region_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The filled image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_Region_Description">
            <summary>
              Looks up a localized string similar to All pixels within this region will be filled..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_Value_Description">
            <summary>
              Looks up a localized string similar to The value to fill the pixels with, needs to be within the Min/Max of the selected image component..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FillPixels_Value_DisplayName">
            <summary>
              Looks up a localized string similar to Value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_MaxLargerThanMin">
            <summary>
              Looks up a localized string similar to Max value must be larger than min value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_MaxValue_Description">
            <summary>
              Looks up a localized string similar to Inclusive upper bound..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_MaxValue_DisplayName">
            <summary>
              Looks up a localized string similar to Max value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_MinValue_Description">
            <summary>
              Looks up a localized string similar to Inclusive lower bound..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_MinValue_DisplayName">
            <summary>
              Looks up a localized string similar to Min value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_OutputRegions_Description">
            <summary>
              Looks up a localized string similar to The filtered regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_OutputRegions_DisplayName">
            <summary>
              Looks up a localized string similar to Output regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_OutputValues_Description">
            <summary>
              Looks up a localized string similar to The actual feature values of the output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_OutputValues_DisplayName">
            <summary>
              Looks up a localized string similar to Output values.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_RegionFeature_Description">
            <summary>
              Looks up a localized string similar to The region feature which the filtering of the regions should be based on..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_RegionFeature_DisplayName">
            <summary>
              Looks up a localized string similar to Region feature.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_Regions_Description">
            <summary>
              Looks up a localized string similar to The list of regions to be sorted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_WithinRange_Description">
            <summary>
              Looks up a localized string similar to If set to true, all regions with values in the range between Min and Max will be kept. If set to false, all regions with values outside the range between Min and Max will be kept..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FilterRegions_WithinRange_DisplayName">
            <summary>
              Looks up a localized string similar to Within min/max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_BackgroundImage_Description">
            <summary>
              Looks up a localized string similar to This image can be used for debugging. It is an up-sampling of the profile to the same size as Source Image. It is recommended to disable this output if it is not needed to improve performance..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_BackgroundImage_DisplayName">
            <summary>
              Looks up a localized string similar to Background image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_BackgroundProfile_Description">
            <summary>
              Looks up a localized string similar to This is the main output of the function. A profile that contains the average range information in the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_BackgroundProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Background profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_Orientation_Description">
            <summary>
              Looks up a localized string similar to The orientation of the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_Orientation_DisplayName">
            <summary>
              Looks up a localized string similar to Orientation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_OutlierDistance_Description">
            <summary>
              Looks up a localized string similar to If set to a positive value a second pass will be made over the image. In this pass all points that are further away from the average than this distance will be ignored and a new average will be calculated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBackgroundProfile_OutlierDistance_DisplayName">
            <summary>
              Looks up a localized string similar to Outlier distance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_Blobs_Description">
            <summary>
              Looks up a localized string similar to A list of regions, describing the found blobs..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_Blobs_DisplayName">
            <summary>
              Looks up a localized string similar to Blobs.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_CenterPoints_Description">
            <summary>
              Looks up a localized string similar to A list of 2D points, describing the center points of each detected blob..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_CenterPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Center points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_DiscardBorderBlobs_Description">
            <summary>
              Looks up a localized string similar to Determines if blobs that are connected to the border of the image (or input region) will be included or not..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_DiscardBorderBlobs_DisplayName">
            <summary>
              Looks up a localized string similar to Discard border blobs.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_FillHoles_Description">
            <summary>
              Looks up a localized string similar to Determines if holes in detected blobs should be included in the blob or not..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_FillHoles_DisplayName">
            <summary>
              Looks up a localized string similar to Fill holes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_HighThreshold_Description">
            <summary>
              Looks up a localized string similar to The highest value for a pixel to be considered part of a blob..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_HighThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to High threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_HighThreshold_OptionalDescription">
            <summary>
              Looks up a localized string similar to No upper limit is used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_LowThreshold_Description">
            <summary>
              Looks up a localized string similar to The lowest value for a pixel to be considered part of a blob..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_LowThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Low threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_LowThreshold_OptionalDescription">
            <summary>
              Looks up a localized string similar to The value will be set to NAN, which means missing data pixels will also be included..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxArea_Description">
            <summary>
              Looks up a localized string similar to A maximum area (in pixels) for the output blobs. Blobs containing more pixels will not be included in the result..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxArea_DisplayName">
            <summary>
              Looks up a localized string similar to Max area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to There will be no maximum area limit..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxBlobCount_Description">
            <summary>
              Looks up a localized string similar to The maximum number of output blob regions, the blobs with the largest area are kept..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxBlobCount_DisplayName">
            <summary>
              Looks up a localized string similar to Max blob count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MaxBlobCount_OptionalDescription">
            <summary>
              Looks up a localized string similar to All found blobs will be kept..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MinArea_Description">
            <summary>
              Looks up a localized string similar to A minimum area (in pixels) for the output blobs. Blobs containing less pixels will not be included in the result..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MinArea_DisplayName">
            <summary>
              Looks up a localized string similar to Min area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_MinArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to The value 1 will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_ReferencePlane_Description">
            <summary>
              Looks up a localized string similar to The plane with which the Low threshold and High threshold are defined in relation to. If more than one Region have been provided, the number of planes can either be 1 or the same as the number of regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_ReferencePlane_DisplayName">
            <summary>
              Looks up a localized string similar to Reference plane.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindBlobs_ReferencePlane_OptionalDescription">
            <summary>
              Looks up a localized string similar to The low and high thresholds will be defined in absolute terms, in relation to no plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenterIndices_Description">
            <summary>
              Looks up a localized string similar to The grid indices of the sorted center points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenterIndices_DisplayName">
            <summary>
              Looks up a localized string similar to Dot center indices.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenters2D_Description">
            <summary>
              Looks up a localized string similar to The XY coordinates of the detected dots..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenters2D_DisplayName">
            <summary>
              Looks up a localized string similar to Dot centers 2D.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenters3D_Description">
            <summary>
              Looks up a localized string similar to The XYZ coordinates of the detected dots..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotCenters3D_DisplayName">
            <summary>
              Looks up a localized string similar to Dot centers 3D.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotPlanes_Description">
            <summary>
              Looks up a localized string similar to The estimated planes used for calculating the 3D position of the target points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotPlanes_DisplayName">
            <summary>
              Looks up a localized string similar to Dot planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotRegions_Description">
            <summary>
              Looks up a localized string similar to The detected dot regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_DotRegions_DisplayName">
            <summary>
              Looks up a localized string similar to Dot regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_MaxArea_Description">
            <summary>
              Looks up a localized string similar to The max area used when finding the dot..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_MaxArea_DisplayName">
            <summary>
              Looks up a localized string similar to Max area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_MinArea_Description">
            <summary>
              Looks up a localized string similar to The min area used when finding the dot..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_MinArea_DisplayName">
            <summary>
              Looks up a localized string similar to Min area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_OriginIndex_Description">
            <summary>
              Looks up a localized string similar to The index of the point to be used as origin..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_OriginIndex_DisplayName">
            <summary>
              Looks up a localized string similar to Origin index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneFitMode_Description">
            <summary>
              Looks up a localized string similar to Which pixels around each dot that are used for the plane fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneFitMode_DisplayName">
            <summary>
              Looks up a localized string similar to Plane fit mode.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneSizeX_Description">
            <summary>
              Looks up a localized string similar to Width of the fitted planes in relation to the dot width..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneSizeX_DisplayName">
            <summary>
              Looks up a localized string similar to Plane size X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneSizeY_Description">
            <summary>
              Looks up a localized string similar to Height of the fitted planes in relation to the dot height..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_PlaneSizeY_DisplayName">
            <summary>
              Looks up a localized string similar to Plane size Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_ThresholdedImage_Description">
            <summary>
              Looks up a localized string similar to The detected dots represented in a binary image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindDotWedgeTarget_ThresholdedImage_DisplayName">
            <summary>
              Looks up a localized string similar to Thresholded image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_DeviationThreshold_Description">
            <summary>
              Looks up a localized string similar to How much the area is allowed to deviate from a flat surface to be considered flat.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_DeviationThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Deviation threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_DownSampleFactor_Description">
            <summary>
              Looks up a localized string similar to The factor by which the image is downsampled before the operation. Using a higher value makes the algorithm faster but less accurate..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_DownSampleFactor_DisplayName">
            <summary>
              Looks up a localized string similar to Down sample factor.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_FillHoles_Description">
            <summary>
              Looks up a localized string similar to Whether to fill the holes of the regions in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_FillHoles_DisplayName">
            <summary>
              Looks up a localized string similar to Fill holes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_FlatRegions_Description">
            <summary>
              Looks up a localized string similar to The resulting pixel region containing the flat regions of the image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_FlatRegions_DisplayName">
            <summary>
              Looks up a localized string similar to Flat regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_HighThreshold_Description">
            <summary>
              Looks up a localized string similar to Pixels with range values over this value will not be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_HighThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to High threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_HighThreshold_OptionalDescription">
            <summary>
              Looks up a localized string similar to Pixels with arbitrarily high range values will be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_LowThreshold_Description">
            <summary>
              Looks up a localized string similar to Pixels with ranges values under this value will not be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_LowThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Low threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_LowThreshold_OptionalDescription">
            <summary>
              Looks up a localized string similar to Pixels with arbitrarily low range values will be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MaxArea_Description">
            <summary>
              Looks up a localized string similar to Regions with an area over this value will not be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MaxArea_DisplayName">
            <summary>
              Looks up a localized string similar to Max area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MaxArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to Arbitrarily large regions will be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_Method_Description">
            <summary>
              Looks up a localized string similar to The method to use for measuring flatness. Central difference is faster, least squares is more accurate..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_Method_DisplayName">
            <summary>
              Looks up a localized string similar to Method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MinArea_Description">
            <summary>
              Looks up a localized string similar to Regions with an area under this value will not be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MinArea_DisplayName">
            <summary>
              Looks up a localized string similar to Min area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MinArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to Arbitrarily small regions will be included in the result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MinAreaMustBeSmallerThanMax">
            <summary>
              Looks up a localized string similar to MinArea must be smaller than MaxArea.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindFlatRegions_MinRangeMustBeSmallerThanMax">
            <summary>
              Looks up a localized string similar to LowThreshold must be smaller than HighThreshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_FirstStepDown_Description">
            <summary>
              Looks up a localized string similar to Location of the first step down along the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_FirstStepDown_DisplayName">
            <summary>
              Looks up a localized string similar to First step down.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_FirstStepUp_Description">
            <summary>
              Looks up a localized string similar to Location of the first step up along the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_FirstStepUp_DisplayName">
            <summary>
              Looks up a localized string similar to First step up.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_LastStepDown_Description">
            <summary>
              Looks up a localized string similar to Location of the last step down along the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_LastStepDown_DisplayName">
            <summary>
              Looks up a localized string similar to Last step down.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_LastStepUp_Description">
            <summary>
              Looks up a localized string similar to Location of the last step up along the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_LastStepUp_DisplayName">
            <summary>
              Looks up a localized string similar to Last step up.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_Profiles_Description">
            <summary>
              Looks up a localized string similar to The input profiles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_Profiles_DisplayName">
            <summary>
              Looks up a localized string similar to Profiles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepHeight_Description">
            <summary>
              Looks up a localized string similar to A range threshold for calculating steps..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Step height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepLength_Description">
            <summary>
              Looks up a localized string similar to The number of profile samples used when calculating the steps..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepLength_DisplayName">
            <summary>
              Looks up a localized string similar to Step length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepSide_Description">
            <summary>
              Looks up a localized string similar to Specifies which side of the edge to choose..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindProfileSteps_StepSide_DisplayName">
            <summary>
              Looks up a localized string similar to Step side.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_DistanceLines_Description">
            <summary>
              Looks up a localized string similar to A line between each intersection point..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_DistanceLines_DisplayName">
            <summary>
              Looks up a localized string similar to Distance lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_Intersections_Description">
            <summary>
              Looks up a localized string similar to The top point of each pyramid. The point is calculated as the intersections of the three surface planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_Intersections_DisplayName">
            <summary>
              Looks up a localized string similar to Intersections.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PlaneOrientations_Description">
            <summary>
              Looks up a localized string similar to The orientation of each plane in the xy-plane. The orientation is the angle between the normal and the x-axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PlaneOrientations_DisplayName">
            <summary>
              Looks up a localized string similar to Plane orientations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_Planes_Description">
            <summary>
              Looks up a localized string similar to Planes estimated to the pyramid surfaces..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PyramidCount_Description">
            <summary>
              Looks up a localized string similar to Optional argument specifying the number of pyramids that should be detected. This helps to avoid false detections. If too many pyramid regions are detected, the regions closest to the center of the image will be kept..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PyramidCount_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PyramidCount_OptionalDescription">
            <summary>
              Looks up a localized string similar to All pyramids in the image will be returned..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PyramidSideLength_Description">
            <summary>
              Looks up a localized string similar to The base side length of the pyramid target..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_PyramidSideLength_DisplayName">
            <summary>
              Looks up a localized string similar to Pyramid side length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_SurfaceRegions_Description">
            <summary>
              Looks up a localized string similar to The detected surfaces of each pyramid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindPyramidTargets_SurfaceRegions_DisplayName">
            <summary>
              Looks up a localized string similar to Surface regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_IntensityThreshold_Description">
            <summary>
              Looks up a localized string similar to Threshold used when detecting the laser line in the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_IntensityThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Intensity threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineLengthFactor_Description">
            <summary>
              Looks up a localized string similar to A factor between 0 and 1 that determines how much of the edge around a target point that is used for the line fitting. Decrease this value if the image contains a lot of distortion, and the edges do not look straight..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineLengthFactor_DisplayName">
            <summary>
              Looks up a localized string similar to Line length factor.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineSegmentProfile_Description">
            <summary>
              Looks up a localized string similar to The line segments represented as a profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineSegmentProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Line segment profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineSegments_Description">
            <summary>
              Looks up a localized string similar to The line segments found from the detected laser peaks..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_LineSegments_DisplayName">
            <summary>
              Looks up a localized string similar to Line segments.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_OriginAlignment_Description">
            <summary>
              Looks up a localized string similar to If set to Top, the X axis will be aligned with the target teeth points. If set to Bottom, the X axis will be aligned with the target valley points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_OriginAlignment_DisplayName">
            <summary>
              Looks up a localized string similar to Origin alignment.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_OriginIndex_Description">
            <summary>
              Looks up a localized string similar to The index of the point to be used as origin..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_OriginIndex_DisplayName">
            <summary>
              Looks up a localized string similar to Origin index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PeakPositions_Description">
            <summary>
              Looks up a localized string similar to The detected laser peaks along each column represented as a pixel region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PeakPositions_DisplayName">
            <summary>
              Looks up a localized string similar to Peak positions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PeakProfile_Description">
            <summary>
              Looks up a localized string similar to The detected laser peaks along each column represented as a profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PeakProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Peak profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PixelPoints2D_Description">
            <summary>
              Looks up a localized string similar to The 2D positions of the detected target points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PixelPoints2D_DisplayName">
            <summary>
              Looks up a localized string similar to Pixel points 2D.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PixelPoints3D_Description">
            <summary>
              Looks up a localized string similar to The 3D positions of the detected target points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PixelPoints3D_DisplayName">
            <summary>
              Looks up a localized string similar to Pixel points 3D.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PointIndices_Description">
            <summary>
              Looks up a localized string similar to The indices for the detected target points, can be used to define the target world coordinates..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_PointIndices_DisplayName">
            <summary>
              Looks up a localized string similar to Point indices.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input sensor image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindSawToothTarget_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MaxValue_Description">
            <summary>
              Looks up a localized string similar to The maximum value for the threshold..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MaxValue_DisplayName">
            <summary>
              Looks up a localized string similar to Max value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MaxValue_OptionalDescription">
            <summary>
              Looks up a localized string similar to The maximum value within the given region (or the whole image if none was given) is used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MinValue_Description">
            <summary>
              Looks up a localized string similar to The minimum value for the threshold..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MinValue_DisplayName">
            <summary>
              Looks up a localized string similar to Min value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MinValue_OptionalDescription">
            <summary>
              Looks up a localized string similar to The minimum value within the given region (or the whole image if none was given) is used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_MinValueMustBeSmallerThanMax">
            <summary>
              Looks up a localized string similar to Min value must be smaller than Max value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_Threshold_Description">
            <summary>
              Looks up a localized string similar to The found threshold..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_Threshold_DisplayName">
            <summary>
              Looks up a localized string similar to Threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_ThresholdedImage_Description">
            <summary>
              Looks up a localized string similar to The original image thresholded by the found threshold..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FindThreshold_ThresholdedImage_DisplayName">
            <summary>
              Looks up a localized string similar to Thresholded image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_CenterPoints_Description">
            <summary>
              Looks up a localized string similar to The approximate center point of the arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_CenterPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Center point.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the circular arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Circles_Description">
            <summary>
              Looks up a localized string similar to The fitted arcs, represented as circles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Circles_DisplayName">
            <summary>
              Looks up a localized string similar to Circles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_DeltaAngle_Description">
            <summary>
              Looks up a localized string similar to The length of circle segment used when fitting an Arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_DeltaAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Delta angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Radius_Description">
            <summary>
              Looks up a localized string similar to The radius of the circular arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_Radius_DisplayName">
            <summary>
              Looks up a localized string similar to Radius.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_StartAngle_Description">
            <summary>
              Looks up a localized string similar to Start angle of the circle segment used when fitting an Arc..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitArc_StartAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Start angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Areas_Description">
            <summary>
              Looks up a localized string similar to The area of the circle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Areas_DisplayName">
            <summary>
              Looks up a localized string similar to Areas.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_CenterPoints_Description">
            <summary>
              Looks up a localized string similar to The approximate center point of the circle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_CenterPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Center points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the circle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Circles_Description">
            <summary>
              Looks up a localized string similar to The fitted circles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Circles_DisplayName">
            <summary>
              Looks up a localized string similar to Circles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Radius_Description">
            <summary>
              Looks up a localized string similar to The radius of the circle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitCircle_Radius_DisplayName">
            <summary>
              Looks up a localized string similar to Radius.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Areas_Description">
            <summary>
              Looks up a localized string similar to The area of the ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Areas_DisplayName">
            <summary>
              Looks up a localized string similar to Areas.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_CenterPoints_Description">
            <summary>
              Looks up a localized string similar to The approximate center point of the ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_CenterPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Center points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Ellipses_Description">
            <summary>
              Looks up a localized string similar to The fitted ellipses..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Ellipses_DisplayName">
            <summary>
              Looks up a localized string similar to Ellipses.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Orientations_Description">
            <summary>
              Looks up a localized string similar to The orientation of the ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_Orientations_DisplayName">
            <summary>
              Looks up a localized string similar to Orientations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_RadiusX_Description">
            <summary>
              Looks up a localized string similar to The radius of the ellipse in the X axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_RadiusX_DisplayName">
            <summary>
              Looks up a localized string similar to Radius X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_RadiusY_Description">
            <summary>
              Looks up a localized string similar to The radius of the ellipse in the Y axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitEllipse_RadiusY_DisplayName">
            <summary>
              Looks up a localized string similar to Radius Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine2D_Lines_Description">
            <summary>
              Looks up a localized string similar to The output lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine2D_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine2D_Region_Description">
            <summary>
              Looks up a localized string similar to The region where to do the line fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine2D_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Inliers_Description">
            <summary>
              Looks up a localized string similar to All points that were used to fit the final line..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Inliers_DisplayName">
            <summary>
              Looks up a localized string similar to Inliers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Lines_Description">
            <summary>
              Looks up a localized string similar to The output lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Region_Description">
            <summary>
              Looks up a localized string similar to The region where to do the line fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMax_Description">
            <summary>
              Looks up a localized string similar to The maximum range value used when fitting the line..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMax_DisplayName">
            <summary>
              Looks up a localized string similar to Z max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMax_OptionalDescription">
            <summary>
              Looks up a localized string similar to No maximum range value used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMin_Description">
            <summary>
              Looks up a localized string similar to The minimum range value used when fitting the line..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMin_DisplayName">
            <summary>
              Looks up a localized string similar to Z min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitLine3D_ZMin_OptionalDescription">
            <summary>
              Looks up a localized string similar to No minimum range value used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Inliers_Description">
            <summary>
              Looks up a localized string similar to All pixels that were considered inliers in the plane estimation, represented as a pixel region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Inliers_DisplayName">
            <summary>
              Looks up a localized string similar to Inliers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Iterations_Description">
            <summary>
              Looks up a localized string similar to The number of Ransac iterations..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Iterations_DisplayName">
            <summary>
              Looks up a localized string similar to Iterations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MaxRange_Description">
            <summary>
              Looks up a localized string similar to The highest range value for a pixel to be included in the plane fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MaxRange_DisplayName">
            <summary>
              Looks up a localized string similar to Max range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MaxRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to No upper limit is used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MinRange_Description">
            <summary>
              Looks up a localized string similar to The lowest range value for a pixel to be included in the plane fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MinRange_DisplayName">
            <summary>
              Looks up a localized string similar to Min range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_MinRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to No lower limit is used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Planes_Description">
            <summary>
              Looks up a localized string similar to The estimated planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Region_Description">
            <summary>
              Looks up a localized string similar to The region that defines which part of the image to use. If the list contains more than one region, one plane per region will be estimated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Region_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The image from where the data is extracted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Threshold_Description">
            <summary>
              Looks up a localized string similar to A threshold used in the Ransac algorithm..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_Threshold_DisplayName">
            <summary>
              Looks up a localized string similar to Threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_XSubsample_Description">
            <summary>
              Looks up a localized string similar to Every X Subsample horizontal pixel within the region will be used in estimating the plane. Increasing this value will reduce the amount of data, this will decrease the processing time of the algorithm..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_XSubsample_DisplayName">
            <summary>
              Looks up a localized string similar to X subsample.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_YSubsample_Description">
            <summary>
              Looks up a localized string similar to Every Y Subsample vertical pixel within the region will be used in estimating the plane. Increasing this value will reduce the amount of data, this will decrease the processing time of the algorithm..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitPlane_YSubsample_DisplayName">
            <summary>
              Looks up a localized string similar to Y subsample.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_FitResult_Description">
            <summary>
              Looks up a localized string similar to The fitted line, circle or ellipse..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_FitResult_DisplayName">
            <summary>
              Looks up a localized string similar to Fit result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_Points_Description">
            <summary>
              Looks up a localized string similar to The input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_ResultType_Description">
            <summary>
              Looks up a localized string similar to The type of shape to fit to the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints2D_ResultType_DisplayName">
            <summary>
              Looks up a localized string similar to Result type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_FitResult_Description">
            <summary>
              Looks up a localized string similar to The fitted line or plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_FitResult_DisplayName">
            <summary>
              Looks up a localized string similar to Fit result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_Points_Description">
            <summary>
              Looks up a localized string similar to The input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_ResultType_Description">
            <summary>
              Looks up a localized string similar to The type of shape to fit to the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FitToPoints3D_ResultType_DisplayName">
            <summary>
              Looks up a localized string similar to Result type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The output image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipX_Description">
            <summary>
              Looks up a localized string similar to If selected, the X axis will be flipped..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipX_DisplayName">
            <summary>
              Looks up a localized string similar to Flip X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipY_Description">
            <summary>
              Looks up a localized string similar to If selected, the Y axis will be flipped..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipY_DisplayName">
            <summary>
              Looks up a localized string similar to Flip Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipZ_Description">
            <summary>
              Looks up a localized string similar to If selected, the Z axis will be flipped..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.FlipCoordinateSystem_FlipZ_DisplayName">
            <summary>
              Looks up a localized string similar to Flip Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_AcquisitionStart_Description">
            <summary>
              Looks up a localized string similar to Start or stop the selected camera..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_AcquisitionStart_DisplayName">
            <summary>
              Looks up a localized string similar to Acquisition start.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_AvailableFrames_Description">
            <summary>
              Looks up a localized string similar to The number of available frames in the buffer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_AvailableFrames_DisplayName">
            <summary>
              Looks up a localized string similar to Available frames.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_CameraSearch_Description">
            <summary>
              Looks up a localized string similar to Search for cameras.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_CameraSearch_DisplayName">
            <summary>
              Looks up a localized string similar to Camera search.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_CameraSelector_Description">
            <summary>
              Looks up a localized string similar to Select which camera to use..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_CameraSelector_DisplayName">
            <summary>
              Looks up a localized string similar to Selected camera.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_connect_button_text">
            <summary>
              Looks up a localized string similar to Connect.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_connect_message">
            <summary>
              Looks up a localized string similar to {0} connected.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_Connection_Description">
            <summary>
              Looks up a localized string similar to Connect or disconnect the selected camera..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_Connection_DisplayName">
            <summary>
              Looks up a localized string similar to Connection.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The grabbed image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_disconnect_button_text">
            <summary>
              Looks up a localized string similar to Disconnect.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_disconnect_message">
            <summary>
              Looks up a localized string similar to {0} disconnected.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_FrameId_Description">
            <summary>
              Looks up a localized string similar to The frame id of the last grabbed frame..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_FrameId_DisplayName">
            <summary>
              Looks up a localized string similar to Frame id.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_GrabFail_Exception">
            <summary>
              Looks up a localized string similar to Failed to grab image, the camera needs to be connected and started..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_GrabTimeOut_Description">
            <summary>
              Looks up a localized string similar to Grab timeout used when running the step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_GrabTimeOut_DisplayName">
            <summary>
              Looks up a localized string similar to Grab timeout.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_lost_frames_exception">
            <summary>
              Looks up a localized string similar to The result contains lost frames..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_search_button_text">
            <summary>
              Looks up a localized string similar to Search.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_start_button_text">
            <summary>
              Looks up a localized string similar to Start.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_start_message">
            <summary>
              Looks up a localized string similar to {0} started.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_stop_button_text">
            <summary>
              Looks up a localized string similar to Stop.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_stop_message">
            <summary>
              Looks up a localized string similar to {0} stopped.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_timeout_exception">
            <summary>
              Looks up a localized string similar to Grab has timed out..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_unexpected_disconnect_message">
            <summary>
              Looks up a localized string similar to Unexpected disconnect of camera: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_YResolution_Description">
            <summary>
              Looks up a localized string similar to The Y resolution of the grabbed image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.GrabFromCamera_YResolution_OptionalDescription">
            <summary>
              Looks up a localized string similar to The Y resolution will be calculated on the camera using the EncoderResolution and EncoderDivider parameters..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The output image containing the result of the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_InvalidContantOperandArgumentException">
            <summary>
              Looks up a localized string similar to The operand needs to be a single constant number..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_InvalidOperandExceptionException">
            <summary>
              Looks up a localized string similar to The operand has to be either a constant number or an image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_InvalidOperandImageComponentException">
            <summary>
              Looks up a localized string similar to The operand image does not contain the selected component..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_InvalidOperandImageSizeException">
            <summary>
              Looks up a localized string similar to The operand image needs to be of the the same size as the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_InvalidSourceImageComponentException">
            <summary>
              Looks up a localized string similar to The source image does not contain the selected component..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_Operand_Description">
            <summary>
              Looks up a localized string similar to The operand with which to apply the operation to the source image. Can either be a constant value or an image with the same size as the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_Operand_DisplayName">
            <summary>
              Looks up a localized string similar to Operand.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_Operation_Description">
            <summary>
              Looks up a localized string similar to The pixelwise operation to be applied with the operand..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageArithmetic_Operation_DisplayName">
            <summary>
              Looks up a localized string similar to Operation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_Height_Description">
            <summary>
              Looks up a localized string similar to The image height..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_Height_DisplayName">
            <summary>
              Looks up a localized string similar to Height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_Width_Description">
            <summary>
              Looks up a localized string similar to The image width..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_Width_DisplayName">
            <summary>
              Looks up a localized string similar to Width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XMax_Description">
            <summary>
              Looks up a localized string similar to The maximum calibrated X value..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XMax_DisplayName">
            <summary>
              Looks up a localized string similar to X max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XMin_Description">
            <summary>
              Looks up a localized string similar to The minimum calibrated X value..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XMin_DisplayName">
            <summary>
              Looks up a localized string similar to X min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XResolution_Description">
            <summary>
              Looks up a localized string similar to The horizontal resolution in the image in mm/pixel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_XResolution_DisplayName">
            <summary>
              Looks up a localized string similar to X resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_YResolution_Description">
            <summary>
              Looks up a localized string similar to The vertical resolution in the image in mm/pixel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZMax_Description">
            <summary>
              Looks up a localized string similar to The maximum possible Z value in the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZMax_DisplayName">
            <summary>
              Looks up a localized string similar to Z max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZMin_Description">
            <summary>
              Looks up a localized string similar to The minimum possible Z value in the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZMin_DisplayName">
            <summary>
              Looks up a localized string similar to Z min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZResolution_Description">
            <summary>
              Looks up a localized string similar to The height resolution in the image in mm/pixel..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageInfo_ZResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Z resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageMorphology_Iterations_Description">
            <summary>
              Looks up a localized string similar to The number of iterations. In the case of opening or closing, it does not simply repeat the operation, rather it controls the number of iterations of the individual dilation and erosion steps..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageMorphology_Iterations_DisplayName">
            <summary>
              Looks up a localized string similar to Iterations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageToPointCloud_PointCloud_Description">
            <summary>
              Looks up a localized string similar to The resulting point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ImageToPointCloud_PointCloud_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_EndPoints_Description">
            <summary>
              Looks up a localized string similar to The end points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_EndPoints_DisplayName">
            <summary>
              Looks up a localized string similar to End points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Lengths_Description">
            <summary>
              Looks up a localized string similar to The lengths of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Lengths_DisplayName">
            <summary>
              Looks up a localized string similar to Length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Lines_Description">
            <summary>
              Looks up a localized string similar to A list of lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_MiddlePoints_Description">
            <summary>
              Looks up a localized string similar to The middle points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_MiddlePoints_DisplayName">
            <summary>
              Looks up a localized string similar to Middle points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Orientations_Description">
            <summary>
              Looks up a localized string similar to The orientation between the start point and end point of the line. The orientation is between 0 and 360 degrees. In this case the X and Y resolution is used in the calculation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_Orientations_DisplayName">
            <summary>
              Looks up a localized string similar to Orientation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_StartPoints_Description">
            <summary>
              Looks up a localized string similar to The start points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line2DInfo_StartPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Start points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_EndPoints_Description">
            <summary>
              Looks up a localized string similar to The end points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_EndPoints_DisplayName">
            <summary>
              Looks up a localized string similar to End points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Lengths_Description">
            <summary>
              Looks up a localized string similar to The lengths of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Lengths_DisplayName">
            <summary>
              Looks up a localized string similar to Length.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Line2D_Description">
            <summary>
              Looks up a localized string similar to The projection of the 3D lines in the XY plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Line2D_DisplayName">
            <summary>
              Looks up a localized string similar to 2D lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Lines_Description">
            <summary>
              Looks up a localized string similar to A list of lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_Lines_DisplayName">
            <summary>
              Looks up a localized string similar to Lines.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_MiddlePoints_Description">
            <summary>
              Looks up a localized string similar to The middle points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_MiddlePoints_DisplayName">
            <summary>
              Looks up a localized string similar to Middle points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_StartPoints_Description">
            <summary>
              Looks up a localized string similar to The start points of the lines..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Line3DInfo_StartPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Start points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Intersections_Description">
            <summary>
              Looks up a localized string similar to The resulting intersections between the two line variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Intersections_DisplayName">
            <summary>
              Looks up a localized string similar to Intersections.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Lines1_Description">
            <summary>
              Looks up a localized string similar to A Line2D variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Lines1_DisplayName">
            <summary>
              Looks up a localized string similar to Lines 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Lines2_Description">
            <summary>
              Looks up a localized string similar to A Line2D variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Lines2_DisplayName">
            <summary>
              Looks up a localized string similar to Lines 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_NoImageException">
            <summary>
              Looks up a localized string similar to Region was argument was given, but image argument is empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Region_Description">
            <summary>
              Looks up a localized string similar to All intersection points outside the region will be discarded..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_Region_OptionalDescription">
            <summary>
              Looks up a localized string similar to All intersection points will be added..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_SourceImage_Description">
            <summary>
              Looks up a localized string similar to Image needed when a region is given..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LineIntersection_SourceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to All intersection points will be added..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The loaded image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_FilePath_Description">
            <summary>
              Looks up a localized string similar to Path to the image file. The path can be absolute or relative to the saved environment..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_FilePath_DisplayName">
            <summary>
              Looks up a localized string similar to File path.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_YResolution_Description">
            <summary>
              Looks up a localized string similar to The Y resolution of the loaded image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFile_YResolution_OptionalDescription">
            <summary>
              Looks up a localized string similar to The Y resolution will be read from the image file..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_AutomaticCounting_Description">
            <summary>
              Looks up a localized string similar to If selected, the File name argument will automatically change to the next image file in the folder every time the step runs..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_AutomaticCounting_DisplayName">
            <summary>
              Looks up a localized string similar to Automatic counting.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The loaded image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_FileName_Description">
            <summary>
              Looks up a localized string similar to The name of the image file to be loaded..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_FileName_DisplayName">
            <summary>
              Looks up a localized string similar to File name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_Folder_Description">
            <summary>
              Looks up a localized string similar to Path to a folder containing image files. The path can be absolute or relative to the saved environment..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_Folder_DisplayName">
            <summary>
              Looks up a localized string similar to Folder.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_Folder_OptionalDescription">
            <summary>
              Looks up a localized string similar to The same directory as the saved environment is used. Requires the environment to have been saved..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_YResolution_Description">
            <summary>
              Looks up a localized string similar to The Y resolution of the loaded image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LoadFromFolder_YResolution_OptionalDescription">
            <summary>
              Looks up a localized string similar to The Y resolution will be read from the image file..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_CandidateSelectionFactor_Description">
            <summary>
              Looks up a localized string similar to A parameter used to calculate a threshold during the localization of the candidates, should normally be as high as possible. In situations when there are more than one object to detect it might be necessary to decrease this value..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_CandidateSelectionFactor_DisplayName">
            <summary>
              Looks up a localized string similar to Candidate selection factor.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_EdgeFeedback_Description">
            <summary>
              Looks up a localized string similar to Optional output containing the transformed teach edges which can be used to visualize the quality of the matching. However, this will increase the processing time of the tool..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_EdgeFeedback_DisplayName">
            <summary>
              Looks up a localized string similar to Edge feedback.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MatchScore_Description">
            <summary>
              Looks up a localized string similar to A value between 0 and 1 indicating the quality of the match. A good match should be close to 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MatchScore_DisplayName">
            <summary>
              Looks up a localized string similar to Match score.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MatchTransform_Description">
            <summary>
              Looks up a localized string similar to The resulting match transform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MatchTransform_DisplayName">
            <summary>
              Looks up a localized string similar to Match transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MinCandidateDistance_Description">
            <summary>
              Looks up a localized string similar to The minimum distance between two candidates..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MinCandidateDistance_DisplayName">
            <summary>
              Looks up a localized string similar to Min candidate distance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MinScore_Description">
            <summary>
              Looks up a localized string similar to Candidates below this score will be discarded..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_MinScore_DisplayName">
            <summary>
              Looks up a localized string similar to Min score.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_NumberOfCandidates_Description">
            <summary>
              Looks up a localized string similar to The maximum number of match candidates to be found..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_NumberOfCandidates_DisplayName">
            <summary>
              Looks up a localized string similar to Number of candidates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ObjectLocator_Description">
            <summary>
              Looks up a localized string similar to The ObjectLocator variable generated from Teach object..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ObjectLocator_DisplayName">
            <summary>
              Looks up a localized string similar to Object locator.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationAngle_Description">
            <summary>
              Looks up a localized string similar to The rotation angle around the Z axis between the teached object and the found object..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationAngle_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationRange_Description">
            <summary>
              Looks up a localized string similar to The maximum rotation of the match object in relation to the teach object. Can be a value between 0 and 180 degrees. A large value will significantly increase the processing time..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationRange_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationResolution_Description">
            <summary>
              Looks up a localized string similar to Determines the number of rotation angles that are tested in the search, for example if Rotation Range is 30 degrees and the Rotation Resolution is 3 degrees, the tested angles will be [-30, -27, -24 ... 24,27,30] degrees..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_RotationResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_SearchRegion_Description">
            <summary>
              Looks up a localized string similar to Optional match region, the center of the match object(s) must be inside this region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_SearchRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Search region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_SearchRegion_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be searched..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ZMax_Description">
            <summary>
              Looks up a localized string similar to Edges that are located above this height are rejected..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ZMax_DisplayName">
            <summary>
              Looks up a localized string similar to Z max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ZMin_Description">
            <summary>
              Looks up a localized string similar to Edges that are located below this height are rejected..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.LocateObject_ZMin_DisplayName">
            <summary>
              Looks up a localized string similar to Z min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_CalibrationModel_Description">
            <summary>
              Looks up a localized string similar to The calibration model to apply to the input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_CalibrationModel_DisplayName">
            <summary>
              Looks up a localized string similar to Calibration model.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_FlipX_Description">
            <summary>
              Looks up a localized string similar to If checked, the uncalibrated points are flipped horizontally. This is needed to handle camera configurations using Reverse X..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_FlipX_DisplayName">
            <summary>
              Looks up a localized string similar to Flip X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_FlipZ_Description">
            <summary>
              Looks up a localized string similar to If checked, the uncalibrated points are flipped vertically. This is needed to handle camera configurations using Reverse Y..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_FlipZ_DisplayName">
            <summary>
              Looks up a localized string similar to Flip Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_InputCoordinates_Description">
            <summary>
              Looks up a localized string similar to The coordinate system of the input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_InputCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Input coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_InputPoints_Description">
            <summary>
              Looks up a localized string similar to The points to be transformed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_InputPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Input points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_OutputCoordinates_Description">
            <summary>
              Looks up a localized string similar to The coordinate system of the output points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_OutputCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Output coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_OutputPoints_Description">
            <summary>
              Looks up a localized string similar to The input points, transformed to the output coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_OutputPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Output points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_SameInputOutputException">
            <summary>
              Looks up a localized string similar to The input coordinates are the same as the output coordinates..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_YResolution_Description">
            <summary>
              Looks up a localized string similar to Desired Y resolution of the world coordinate system when input points are Point3D..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_YResolution_DisplayName">
            <summary>
              Looks up a localized string similar to Y resolution.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MapPoints_YResolution_OptionalDescription">
            <summary>
              Looks up a localized string similar to Y resolution is set to 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The masked image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_Invert_Description">
            <summary>
              Looks up a localized string similar to If true the mask will be inverted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_Invert_DisplayName">
            <summary>
              Looks up a localized string similar to Invert.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_RegionMask_Description">
            <summary>
              Looks up a localized string similar to The regions define the mask that will be applied to the image. The union of all regions will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_RegionMask_DisplayName">
            <summary>
              Looks up a localized string similar to Region mask.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MaskImage_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The image to apply the mask on..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MathExpression_Expression_Description">
            <summary>
              Looks up a localized string similar to The expression..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MathExpression_Expression_DisplayName">
            <summary>
              Looks up a localized string similar to Expression.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MathExpression_Result_Description">
            <summary>
              Looks up a localized string similar to The result of evaluating the expression..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MathExpression_Result_DisplayName">
            <summary>
              Looks up a localized string similar to Result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_MergedPointCloud_Description">
            <summary>
              Looks up a localized string similar to The merged point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_MergedPointCloud_DisplayName">
            <summary>
              Looks up a localized string similar to Merged point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_PointCloud1_Description">
            <summary>
              Looks up a localized string similar to The first point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_PointCloud1_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_PointCloud2_Description">
            <summary>
              Looks up a localized string similar to The second point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MergePointClouds_PointCloud2_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The mirrored image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorX_Description">
            <summary>
              Looks up a localized string similar to If true, the image will be mirrored horizontally..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorX_DisplayName">
            <summary>
              Looks up a localized string similar to Mirror X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorY_Description">
            <summary>
              Looks up a localized string similar to If true, the image will be mirrored vertically..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorY_DisplayName">
            <summary>
              Looks up a localized string similar to Mirror Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorZ_Description">
            <summary>
              Looks up a localized string similar to If true, the range component of the image will be inverted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.MirrorImage_MirrorZ_DisplayName">
            <summary>
              Looks up a localized string similar to Mirror Z.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Average_Description">
            <summary>
              Looks up a localized string similar to The average value of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Average_DisplayName">
            <summary>
              Looks up a localized string similar to Average.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Max_Description">
            <summary>
              Looks up a localized string similar to The maximum value of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Max_DisplayName">
            <summary>
              Looks up a localized string similar to Max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Median_Description">
            <summary>
              Looks up a localized string similar to The median value of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Median_DisplayName">
            <summary>
              Looks up a localized string similar to Median.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Min_Description">
            <summary>
              Looks up a localized string similar to The minimum value of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Min_DisplayName">
            <summary>
              Looks up a localized string similar to Min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Numbers_Description">
            <summary>
              Looks up a localized string similar to A list of numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Numbers_DisplayName">
            <summary>
              Looks up a localized string similar to Numbers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Range_Description">
            <summary>
              Looks up a localized string similar to The difference between the minimum and the maximum..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Range_DisplayName">
            <summary>
              Looks up a localized string similar to Range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_StandardDeviation_Description">
            <summary>
              Looks up a localized string similar to The population standard deviation of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_StandardDeviation_DisplayName">
            <summary>
              Looks up a localized string similar to Standard deviation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Variance_Description">
            <summary>
              Looks up a localized string similar to The population variance of the numbers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.NumberStatistics_Variance_DisplayName">
            <summary>
              Looks up a localized string similar to Variance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_EulerX_Description">
            <summary>
              Looks up a localized string similar to The Euler X angle in degrees calculated from the normal vector..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_EulerX_DisplayName">
            <summary>
              Looks up a localized string similar to Euler X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_EulerY_Description">
            <summary>
              Looks up a localized string similar to The Euler Y angle in degrees calculated from the normal vector..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_EulerY_DisplayName">
            <summary>
              Looks up a localized string similar to Euler Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_InvalidPlaneLengthsException">
            <summary>
              Looks up a localized string similar to The number of planes and points must be the same..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_NoPointsException">
            <summary>
              Looks up a localized string similar to Point argument is empty, but is needed for the plane heights and 3D points results..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_PlaneHeights_Description">
            <summary>
              Looks up a localized string similar to The resulting plane heights..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_PlaneHeights_DisplayName">
            <summary>
              Looks up a localized string similar to Plane heights.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Planes_Description">
            <summary>
              Looks up a localized string similar to The input plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Points_Description">
            <summary>
              Looks up a localized string similar to The input points. If both the plane and the point lists have more than one element, the number of elements must be the same..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Points_OptionalDescription">
            <summary>
              Looks up a localized string similar to Plane heights and Points 3D cannot be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Points3D_Description">
            <summary>
              Looks up a localized string similar to A 3D-point defined from the input 2D-point and the calculated height..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_Points3D_DisplayName">
            <summary>
              Looks up a localized string similar to Points 3D.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PlaneInfo_SourceImage_OptionalDescription">
            <summary>
              Looks up a localized string similar to Plane heights and Points 3D cannot be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_Points_Description">
            <summary>
              Looks up a localized string similar to A list of points, located in the input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_XCoordinates_Description">
            <summary>
              Looks up a localized string similar to The X coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_XCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to X coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_YCoordinates_Description">
            <summary>
              Looks up a localized string similar to The Y coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point2DInfo_YCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Y coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_Points_Description">
            <summary>
              Looks up a localized string similar to A list of points, located in the input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_XCoordinates_Description">
            <summary>
              Looks up a localized string similar to The X coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_XCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to X coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_XYCoordinates_Description">
            <summary>
              Looks up a localized string similar to The XY coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_XYCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to XY Coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_YCoordinates_Description">
            <summary>
              Looks up a localized string similar to The Y coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_YCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Y coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_ZCoordinates_Description">
            <summary>
              Looks up a localized string similar to The Z coordinates of the points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Point3DInfo_ZCoordinates_DisplayName">
            <summary>
              Looks up a localized string similar to Z coordinates.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Correspondences_Description">
            <summary>
              Looks up a localized string similar to The approximate number of correspondences between the two point clouds..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Correspondences_DisplayName">
            <summary>
              Looks up a localized string similar to Correspondences.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_LockXZRotation_Description">
            <summary>
              Looks up a localized string similar to Whether to estimate rotation around X and Z, or not. If true, rotation around x and z axis will be strictly zero. This is useful for example in side-by-side alignment where this rotation should not happen..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_LockXZRotation_DisplayName">
            <summary>
              Looks up a localized string similar to Lock XZ Rotation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_MaxOverlapDistance_Description">
            <summary>
              Looks up a localized string similar to The maximum allowed distance between the overlapping parts of the two point clouds..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_MaxOverlapDistance_DisplayName">
            <summary>
              Looks up a localized string similar to Max overlap distance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Overlap_Description">
            <summary>
              Looks up a localized string similar to The area that was used in the estimation. The area is the part of the input region that is within the bounds of both the point clouds..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Overlap_DisplayName">
            <summary>
              Looks up a localized string similar to Overlap.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_PointCloud1_Description">
            <summary>
              Looks up a localized string similar to The first point cloud..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_PointCloud1_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_PointCloud2_Description">
            <summary>
              Looks up a localized string similar to The second point cloud..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_PointCloud2_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Region_Description">
            <summary>
              Looks up a localized string similar to Region of interest used to calculate where to do the ICP..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Transform_Description">
            <summary>
              Looks up a localized string similar to The calculated transform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudRegistration_Transform_DisplayName">
            <summary>
              Looks up a localized string similar to Transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_Bounds_Description">
            <summary>
              Looks up a localized string similar to Optional xy world bounds.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_Bounds_DisplayName">
            <summary>
              Looks up a localized string similar to Bounds.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_Bounds_OptionalDescription">
            <summary>
              Looks up a localized string similar to The bounds will be the min/max bounds of the point cloud..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The rectified output image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_ImageHeight_Description">
            <summary>
              Looks up a localized string similar to The height of the destination image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_ImageHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Image height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_ImageWidth_Description">
            <summary>
              Looks up a localized string similar to The width of the destination image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_ImageWidth_DisplayName">
            <summary>
              Looks up a localized string similar to Image width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_PointCloud_Description">
            <summary>
              Looks up a localized string similar to The input point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointCloudToImage_PointCloud_DisplayName">
            <summary>
              Looks up a localized string similar to Point cloud.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Heights_Description">
            <summary>
              Looks up a localized string similar to The height measurements that are the result of the operation in the tool..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Heights_DisplayName">
            <summary>
              Looks up a localized string similar to Heights.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MaxRange_Description">
            <summary>
              Looks up a localized string similar to The highest height value to be accepted when plane fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MaxRange_DisplayName">
            <summary>
              Looks up a localized string similar to Max range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MaxRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to The height values can be arbitrarily high..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Method_Description">
            <summary>
              Looks up a localized string similar to Method used to calculate the point height..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Method_DisplayName">
            <summary>
              Looks up a localized string similar to Method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MinRange_Description">
            <summary>
              Looks up a localized string similar to The lowest height value to be accepted when plane fitting..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MinRange_DisplayName">
            <summary>
              Looks up a localized string similar to Min range.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_MinRange_OptionalDescription">
            <summary>
              Looks up a localized string similar to The height values can be arbitrarily low..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_PercentileLevel_Description">
            <summary>
              Looks up a localized string similar to Level between 0 and 1, used for the Percentile method..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_PercentileLevel_DisplayName">
            <summary>
              Looks up a localized string similar to Percentile level.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Planes_Description">
            <summary>
              Looks up a localized string similar to The local planes that are estimated around each point, in the Fit plane method..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Points_Description">
            <summary>
              Looks up a localized string similar to The height measurement points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Points3D_Description">
            <summary>
              Looks up a localized string similar to The full 3D point positions in world units..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Points3D_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Radius_Description">
            <summary>
              Looks up a localized string similar to The radius around each point in which data is extracted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Radius_DisplayName">
            <summary>
              Looks up a localized string similar to Radius.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_ReferencePlane_Description">
            <summary>
              Looks up a localized string similar to The plane which the point height is measured in relation to. If provided, the number of planes must be either 1 or the same as the number of points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_ReferencePlane_DisplayName">
            <summary>
              Looks up a localized string similar to Reference plane.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_ReferencePlane_OptionalDescription">
            <summary>
              Looks up a localized string similar to The height will be in relation to the origin of the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Sigma_Description">
            <summary>
              Looks up a localized string similar to Sigma used in the gauss kernel for the Weighted average method. A low sigma will give low impact from pixels far away from the center..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Sigma_DisplayName">
            <summary>
              Looks up a localized string similar to Sigma.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Threshold_Description">
            <summary>
              Looks up a localized string similar to Is used by the Plane fit method. The threshold is the distance from an estimated plane within which the points are considered to be part of the plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointHeight_Threshold_DisplayName">
            <summary>
              Looks up a localized string similar to Threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_InvalidOperandExceptionException">
            <summary>
              Looks up a localized string similar to The operand has to be either a constant number list or a Point2D list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_InvalidOperandLengthException">
            <summary>
              Looks up a localized string similar to The operand list have length one or the same length as the input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Operands_Description">
            <summary>
              Looks up a localized string similar to The operand with which to apply the operation. Can either be length one or the same length as the input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Operands_DisplayName">
            <summary>
              Looks up a localized string similar to Operands.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Operation_Description">
            <summary>
              Looks up a localized string similar to Which type of operation to perform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Operation_DisplayName">
            <summary>
              Looks up a localized string similar to Operation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Points_Description">
            <summary>
              Looks up a localized string similar to The input points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Result_Description">
            <summary>
              Looks up a localized string similar to The output after the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.PointOperation_Result_DisplayName">
            <summary>
              Looks up a localized string similar to Result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_FirstValidPoints_Description">
            <summary>
              Looks up a localized string similar to The first sample point that is not missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_FirstValidPoints_DisplayName">
            <summary>
              Looks up a localized string similar to First valid points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_InvalidProfileLengthsException">
            <summary>
              Looks up a localized string similar to Only possible to get points from single profiles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_LastValidPoints_Description">
            <summary>
              Looks up a localized string similar to The last sample point that is not missing data..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_LastValidPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Last valid points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Lengths_Description">
            <summary>
              Looks up a localized string similar to The length of the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Lengths_DisplayName">
            <summary>
              Looks up a localized string similar to Lengths.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_MaxPoints_Description">
            <summary>
              Looks up a localized string similar to The locations of the highest range value of the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_MaxPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Max points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_MinPoints_Description">
            <summary>
              Looks up a localized string similar to The locations of the lowest range value of the profile..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_MinPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Min points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_NumberOfSamples_Description">
            <summary>
              Looks up a localized string similar to The number of profile samples..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_NumberOfSamples_DisplayName">
            <summary>
              Looks up a localized string similar to Number of samples.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Points_Description">
            <summary>
              Looks up a localized string similar to The profile represented as a list of points. If this is used there can only be one profile as input..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Profiles_Description">
            <summary>
              Looks up a localized string similar to The input profiles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileInfo_Profiles_DisplayName">
            <summary>
              Looks up a localized string similar to Profiles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Operation_Description">
            <summary>
              Looks up a localized string similar to Which type of operation to perform..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Operation_DisplayName">
            <summary>
              Looks up a localized string similar to Operation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_ProfileLengthMismatchedException">
            <summary>
              Looks up a localized string similar to The number of profiles in Profiles1 and Profiles2 needs to match..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_ProfilePointsLengthMismatchedException">
            <summary>
              Looks up a localized string similar to The number of profile points in each profile needs to match..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Profiles1_Description">
            <summary>
              Looks up a localized string similar to The first input profiles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Profiles1_DisplayName">
            <summary>
              Looks up a localized string similar to Profiles 1.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Profiles2_Description">
            <summary>
              Looks up a localized string similar to The second input profiles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Profiles2_DisplayName">
            <summary>
              Looks up a localized string similar to Profiles 2.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Result_Description">
            <summary>
              Looks up a localized string similar to The output after the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ProfileOperation_Result_DisplayName">
            <summary>
              Looks up a localized string similar to Result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The resulting image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_IncludeMissingData_Description">
            <summary>
              Looks up a localized string similar to Select true to include pixels with missing data when doing the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_IncludeMissingData_DisplayName">
            <summary>
              Looks up a localized string similar to Include missing data.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_InvalidRegionsCount">
            <summary>
              Looks up a localized string similar to If there are more than one plane the number of regions and planes must be the same..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_Planes_Description">
            <summary>
              Looks up a localized string similar to The input planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_Regions_Description">
            <summary>
              Looks up a localized string similar to The regions of the image where the operation will be applied..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RaiseToPlane_Regions_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Centers_Description">
            <summary>
              Looks up a localized string similar to The center of the rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Centers_DisplayName">
            <summary>
              Looks up a localized string similar to Centers.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Heights_Description">
            <summary>
              Looks up a localized string similar to The height of the rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Heights_DisplayName">
            <summary>
              Looks up a localized string similar to Heights.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Image_Description">
            <summary>
              Looks up a localized string similar to The pixel regions are transformed to the coordinate system of this image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Image_DisplayName">
            <summary>
              Looks up a localized string similar to Image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Image_OptionalDescription">
            <summary>
              Looks up a localized string similar to The pixel regions will be in a pixel coordinate system..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Rectangles_Description">
            <summary>
              Looks up a localized string similar to The input rectangles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Rectangles_DisplayName">
            <summary>
              Looks up a localized string similar to Rectangles.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Regions_Description">
            <summary>
              Looks up a localized string similar to The pixel region that makes up the rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Rotations_Description">
            <summary>
              Looks up a localized string similar to The rotation of the rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Rotations_DisplayName">
            <summary>
              Looks up a localized string similar to Rotations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Widths_Description">
            <summary>
              Looks up a localized string similar to The width of the rectangle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RectangleInfo_Widths_DisplayName">
            <summary>
              Looks up a localized string similar to Widths.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_AveragePixelValue_Description">
            <summary>
              Looks up a localized string similar to The average pixel value inside the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_AveragePixelValue_DisplayName">
            <summary>
              Looks up a localized string similar to Average pixel value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBox_Description">
            <summary>
              Looks up a localized string similar to The bounding box of the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBox_DisplayName">
            <summary>
              Looks up a localized string similar to Bounding box.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBoxHeight_Description">
            <summary>
              Looks up a localized string similar to The height of the bounding box..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBoxHeight_DisplayName">
            <summary>
              Looks up a localized string similar to Bounding box height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBoxWidth_Description">
            <summary>
              Looks up a localized string similar to The width of the bounding box..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_BoundingBoxWidth_DisplayName">
            <summary>
              Looks up a localized string similar to Bounding box width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_CenterPoints_Description">
            <summary>
              Looks up a localized string similar to A list of points corresponding to the centers (center of mass) of each region in the list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_CenterPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Center points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_HoleCount_Description">
            <summary>
              Looks up a localized string similar to The number of holes in the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_HoleCount_DisplayName">
            <summary>
              Looks up a localized string similar to Hole count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_LargestSide_Description">
            <summary>
              Looks up a localized string similar to The largest side of the oriented bounding box..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_LargestSide_DisplayName">
            <summary>
              Looks up a localized string similar to Largest side.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MaxPixelPosition_Description">
            <summary>
              Looks up a localized string similar to The position of the maximum pixel value inside the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MaxPixelPosition_DisplayName">
            <summary>
              Looks up a localized string similar to Max pixel position.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MaxPixelValue_Description">
            <summary>
              Looks up a localized string similar to The maximum pixel value in the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MaxPixelValue_DisplayName">
            <summary>
              Looks up a localized string similar to Max pixel value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MinPixelPosition_Description">
            <summary>
              Looks up a localized string similar to The position of the minimum pixel value inside the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MinPixelPosition_DisplayName">
            <summary>
              Looks up a localized string similar to Min pixel position.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MinPixelValue_Description">
            <summary>
              Looks up a localized string similar to The minimum pixel value inside the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_MinPixelValue_DisplayName">
            <summary>
              Looks up a localized string similar to Min pixel value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_OrientedBoundingBox_Description">
            <summary>
              Looks up a localized string similar to The minimal rotated rectangle enclosing the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_OrientedBoundingBox_DisplayName">
            <summary>
              Looks up a localized string similar to Oriented bounding box.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_PixelCount_Description">
            <summary>
              Looks up a localized string similar to The number of pixels in the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_PixelCount_DisplayName">
            <summary>
              Looks up a localized string similar to Pixel count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_Regions_Description">
            <summary>
              Looks up a localized string similar to The variable containing the list of regions to be analysed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_Rotation_Description">
            <summary>
              Looks up a localized string similar to The rotation of the oriented bounding box, 0 degrees is along the X axis..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_Rotation_DisplayName">
            <summary>
              Looks up a localized string similar to Rotation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_SmallestSide_Description">
            <summary>
              Looks up a localized string similar to The smallest side of the oriented bounding box..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_SmallestSide_DisplayName">
            <summary>
              Looks up a localized string similar to Smallest side.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The image where the region is located. This is needed for some calculations like rotation angle..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_StddevPixelValue_Description">
            <summary>
              Looks up a localized string similar to The standard deviation of the pixel values inside the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_StddevPixelValue_DisplayName">
            <summary>
              Looks up a localized string similar to Standard deviation pixel value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_ValidPixelCount_Description">
            <summary>
              Looks up a localized string similar to The number of non-missing data pixels in the region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionInfo_ValidPixelCount_DisplayName">
            <summary>
              Looks up a localized string similar to Valid pixel count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_Iterations_Description">
            <summary>
              Looks up a localized string similar to The number of iterations..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_Iterations_DisplayName">
            <summary>
              Looks up a localized string similar to Iterations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_KernelSize_Description">
            <summary>
              Looks up a localized string similar to The size of the kernel, need to be an odd number..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_KernelSize_DisplayName">
            <summary>
              Looks up a localized string similar to Kernel size.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_Region_Description">
            <summary>
              Looks up a localized string similar to The regions on which the operation will be performed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_TargetPixelRegion_Description">
            <summary>
              Looks up a localized string similar to The output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RegionMorphology_TargetPixelRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Destination region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_Angle_Description">
            <summary>
              Looks up a localized string similar to The angle in degrees between the optical axis and the laser plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_Angle_DisplayName">
            <summary>
              Looks up a localized string similar to Angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The resulting outlier-trimmed image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_ScanDirection_Description">
             <summary>
               Looks up a localized string similar to Select in which direction the image was scanned.
            
            The scan direction can sometimes be determined from the image heightmap view, by looking where the occlusion from objects are located.
            
            Laser first means that the object has passed the laser plane before passing under the camera. The occlusion will then appear below the objects.
            
            Laser last means that the object passed under the camera before passing the laser plane. The occlusion will then appear above the objects..
             </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_ScanDirection_DisplayName">
            <summary>
              Looks up a localized string similar to Scan direction.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_SearchDistance_Description">
            <summary>
              Looks up a localized string similar to The distance in pixels of how far the algorithm should search for potential outliers..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_SearchDistance_DisplayName">
            <summary>
              Looks up a localized string similar to Search distance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_Threshold_Description">
            <summary>
              Looks up a localized string similar to The level of uncertainty required for noise to be removed, 1 means all conflicts are removed, higher numbers means that higher uncertainty is required..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RemoveOutliers_Threshold_DisplayName">
            <summary>
              Looks up a localized string similar to Threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RenameVariable_NewName_Description">
            <summary>
              Looks up a localized string similar to The new name to give to the variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RenameVariable_NewName_DisplayName">
            <summary>
              Looks up a localized string similar to New name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RenameVariable_Variable_Description">
            <summary>
              Looks up a localized string similar to The variable to change the name of..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RenameVariable_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The resized image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_InterpolationMethod_Description">
            <summary>
              Looks up a localized string similar to The method used for interpolation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_InterpolationMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Interpolation method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewHeight_Description">
            <summary>
              Looks up a localized string similar to The height of the destination image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewHeight_DisplayName">
            <summary>
              Looks up a localized string similar to New height.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewHeight_OptionalDescription">
            <summary>
              Looks up a localized string similar to The new height will be the same as the height of the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewWidth_Description">
            <summary>
              Looks up a localized string similar to The width of the destination image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewWidth_DisplayName">
            <summary>
              Looks up a localized string similar to New width.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.ResizeImage_NewWidth_OptionalDescription">
            <summary>
              Looks up a localized string similar to The new width will be the same as the width of the source image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RotateImage_Angle_Description">
            <summary>
              Looks up a localized string similar to The angle by which to rotate clockwise, either 90, 180 or 270 degrees..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RotateImage_Angle_DisplayName">
            <summary>
              Looks up a localized string similar to Angle.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RotateImage_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The rotated image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.RotateImage_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_AppendNumber_Description">
            <summary>
              Looks up a localized string similar to If true a number will be appended to the file name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_AppendNumber_DisplayName">
            <summary>
              Looks up a localized string similar to Append number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_FileName_Description">
            <summary>
              Looks up a localized string similar to The name of the saved image files..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_FileName_DisplayName">
            <summary>
              Looks up a localized string similar to File name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_FileNumber_Description">
            <summary>
              Looks up a localized string similar to The number that will be appended to the file name if AppendNumber is enabled. The number is increased every the step runs..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_FileNumber_DisplayName">
            <summary>
              Looks up a localized string similar to File number.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_Folder_Description">
            <summary>
              Looks up a localized string similar to The path to the folder where the image will be saved. The path can be absolute or relative to the saved environment..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_Folder_DisplayName">
            <summary>
              Looks up a localized string similar to Folder.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_Folder_OptionalDescription">
            <summary>
              Looks up a localized string similar to The same directory as the saved environment is used. Requires the environment to have been saved..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SaveToFolder_RelativePathError">
            <summary>
              Looks up a localized string similar to Save the environment before using a relative path.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_DestinationRegion_Description">
            <summary>
              Looks up a localized string similar to The output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_DestinationRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Destination region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MaxArea_Description">
            <summary>
              Looks up a localized string similar to Maximum area of the output regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MaxArea_DisplayName">
            <summary>
              Looks up a localized string similar to Max area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MaxArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to Area can be as big as possible..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MinArea_Description">
            <summary>
              Looks up a localized string similar to Minimum area of the output regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MinArea_DisplayName">
            <summary>
              Looks up a localized string similar to Min area.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_MinArea_OptionalDescription">
            <summary>
              Looks up a localized string similar to Area can be as small as possible..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_Region_Description">
            <summary>
              Looks up a localized string similar to The regions on which the operation will be performed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SeparateRegions_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SobelEdgeFilter_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to An image containing the detected edges..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SobelEdgeFilter_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SobelEdgeFilter_SobelType_Description">
            <summary>
              Looks up a localized string similar to The sobel operator type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SobelEdgeFilter_SobelType_DisplayName">
            <summary>
              Looks up a localized string similar to Sobel type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_GridType_Description">
            <summary>
              Looks up a localized string similar to The grid layout type..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_GridType_DisplayName">
            <summary>
              Looks up a localized string similar to Grid type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_Margin_Description">
            <summary>
              Looks up a localized string similar to Margin used for the rigid grid layout method..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_Margin_DisplayName">
            <summary>
              Looks up a localized string similar to Margin.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_Points_Description">
            <summary>
              Looks up a localized string similar to The unsorted points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_Points_DisplayName">
            <summary>
              Looks up a localized string similar to Points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_SortedIndices_Description">
            <summary>
              Looks up a localized string similar to The grid indices of the sorted points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_SortedIndices_DisplayName">
            <summary>
              Looks up a localized string similar to Sorted indices.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_SortedPoints_Description">
            <summary>
              Looks up a localized string similar to The sorted points..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortPointGrid_SortedPoints_DisplayName">
            <summary>
              Looks up a localized string similar to Sorted points.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_Ascending_Description">
            <summary>
              Looks up a localized string similar to The sort direction..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_Ascending_DisplayName">
            <summary>
              Looks up a localized string similar to Ascending.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_RegionFeature_Description">
            <summary>
              Looks up a localized string similar to The region feature which the filtering of the regions should be based on..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_RegionFeature_DisplayName">
            <summary>
              Looks up a localized string similar to Region feature.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_Regions_Description">
            <summary>
              Looks up a localized string similar to The list of regions to be sorted..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_SourceImage_Description">
            <summary>
              Looks up a localized string similar to The input image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_SourceImage_DisplayName">
            <summary>
              Looks up a localized string similar to Source image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_TargetPixelRegion_Description">
            <summary>
              Looks up a localized string similar to  The filtered regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SortRegions_TargetPixelRegion_DisplayName">
            <summary>
              Looks up a localized string similar to Output regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_CountX_Description">
            <summary>
              Looks up a localized string similar to The number of columns in the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_CountX_DisplayName">
            <summary>
              Looks up a localized string similar to Count X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_CountY_Description">
            <summary>
              Looks up a localized string similar to The number of rows in the grid..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_CountY_DisplayName">
            <summary>
              Looks up a localized string similar to Count Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_MarginX_Description">
            <summary>
              Looks up a localized string similar to The X margin between the elements in the output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_MarginX_DisplayName">
            <summary>
              Looks up a localized string similar to Margin X.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_MarginY_Description">
            <summary>
              Looks up a localized string similar to The Y margin between the elements in the output region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_MarginY_DisplayName">
            <summary>
              Looks up a localized string similar to Margin Y.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_Region_Description">
            <summary>
              Looks up a localized string similar to The input region. It can only contain one element..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_SplitRegions_Description">
            <summary>
              Looks up a localized string similar to The split regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SplitRegion_SplitRegions_DisplayName">
            <summary>
              Looks up a localized string similar to Split regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Step_InitializeAllResults_SameNameException">
            <summary>
              Looks up a localized string similar to {0}: All resulting variables for a step need to have unique names, the name &apos;{1}&apos; is used for multiple results..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Step_InvalidTypeException">
            <summary>
              Looks up a localized string similar to {0}: Invalid variable type: The variable &apos;{1}&apos; of type {2} is used as input to the argument &apos;{3}&apos;..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The stitched image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Stitched image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_OutputHeightStrategy_Description">
             <summary>
               Looks up a localized string similar to Controls how the height of the stitched image is calculated:
            • Fit to frame - The stitched image contains all data
              from the input images. As the skew angle increases,
              the height of the stitched image will increase.
            • Shift to next - The height of the input image is preserved.
              The excess data will be shifted to the next image..
             </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_OutputHeightStrategy_DisplayName">
            <summary>
              Looks up a localized string similar to Output height strategy.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_RectificationMethod_Description">
             <summary>
               Looks up a localized string similar to Controls how overlapping input data is selected:
            • Bottommost - Always pick the lowest range value for each bin.
            • Topmost - Always pick the highest range value for each bin.
            • Brightest - Pick the range value with brightest reflectance value.
            • Darkest - Pick the range value with darkest reflectance value..
             </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_RectificationMethod_DisplayName">
            <summary>
              Looks up a localized string similar to Rectification method.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_SourceImages_Description">
            <summary>
              Looks up a localized string similar to A comma separated list containing the names of the images that will be stitched..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_SourceImages_DisplayName">
            <summary>
              Looks up a localized string similar to Source images.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_SpreadSize_Description">
            <summary>
              Looks up a localized string similar to To avoid missing data in the stitched image, each data point can be spread to multiple adjacent bins. This parameter controls the spread size, a higher value includes more bins..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StitchPartiallyAligned_SpreadSize_DisplayName">
            <summary>
              Looks up a localized string similar to Spread size.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.StringArgument_FolderDoesNotExist">
            <summary>
              Looks up a localized string similar to Folder does not exist..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The resulting image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_Planes_Description">
            <summary>
              Looks up a localized string similar to The input planes..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_Planes_DisplayName">
            <summary>
              Looks up a localized string similar to Planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_Regions_Description">
            <summary>
              Looks up a localized string similar to The regions of the image where the operation will be applied..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractPlane_Regions_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_DestinationImage_Description">
            <summary>
              Looks up a localized string similar to The output image, containing the result of the operation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_DestinationImage_DisplayName">
            <summary>
              Looks up a localized string similar to Destination image.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_InvalidProfileListLengthException">
            <summary>
              Looks up a localized string similar to Only possible to subtract a single profiles..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_Orientation_Description">
            <summary>
              Looks up a localized string similar to The orientation of the profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_Orientation_DisplayName">
            <summary>
              Looks up a localized string similar to Orientation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_ReferenceProfile_Description">
            <summary>
              Looks up a localized string similar to A profile variable that describes the reference level of the image. The height of the profile will be subtracted from the image, row wise or column wise, depending on the Orientation..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.SubtractProfile_ReferenceProfile_DisplayName">
            <summary>
              Looks up a localized string similar to Reference profile.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_BackgroundClutter_Description">
            <summary>
              Looks up a localized string similar to If the images contain other patterns and structures other than the object of interest, set to true. If the object is on a clean background one can gain speed by setting the background clutter to false..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_BackgroundClutter_DisplayName">
            <summary>
              Looks up a localized string similar to Background clutter.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_EdgeFeedback_Description">
            <summary>
              Looks up a localized string similar to The edges found in the teach region, using the specified edge threshold and scale down factor..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_EdgeFeedback_DisplayName">
            <summary>
              Looks up a localized string similar to Edge feedback.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_EdgeThreshold_Description">
            <summary>
              Looks up a localized string similar to The minimum edge jump to consider something an edge. The threshold is applied to a scaled image that may have more smoothed edges (lower amplitude) than the original image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_EdgeThreshold_DisplayName">
            <summary>
              Looks up a localized string similar to Edge threshold.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ObjectLocator_Description">
            <summary>
              Looks up a localized string similar to A model of the teach object..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ObjectLocator_DisplayName">
            <summary>
              Looks up a localized string similar to Object locator.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_Region_Description">
            <summary>
              Looks up a localized string similar to The region where the teach object is located..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_Region_DisplayName">
            <summary>
              Looks up a localized string similar to Region.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_Region_OptionalDescription">
            <summary>
              Looks up a localized string similar to The whole image will be searched..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ScaleDownFactor_Description">
            <summary>
              Looks up a localized string similar to The resampling factor for the coarse search. Each side of the input image will be scaled with this factor, i.e. if the image is N x M pixels large, the reduced image is N/scaleDownFactor x M/scaleDownFactor..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ScaleDownFactor_DisplayName">
            <summary>
              Looks up a localized string similar to Scale down factor.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_TeachTransform_Description">
            <summary>
              Looks up a localized string similar to The teach transform, created from the teach region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_TeachTransform_DisplayName">
            <summary>
              Looks up a localized string similar to Teach transform.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ZMax_Description">
            <summary>
              Looks up a localized string similar to Edges that are located above this height are rejected..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ZMax_DisplayName">
            <summary>
              Looks up a localized string similar to Z max.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ZMin_Description">
            <summary>
              Looks up a localized string similar to Edges that are located below this height are rejected..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TeachObject_ZMin_DisplayName">
            <summary>
              Looks up a localized string similar to Z min.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.TransformPixelRegion_NoImageProvidedException">
            <summary>
              Looks up a localized string similar to An image needs to be provided when transforming a pixel region..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableCount_Count_Description">
            <summary>
              Looks up a localized string similar to The number of elements in the list..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableCount_Count_DisplayName">
            <summary>
              Looks up a localized string similar to Count.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableCount_Variable_Description">
            <summary>
              Looks up a localized string similar to A list of variables..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableCount_Variable_DisplayName">
            <summary>
              Looks up a localized string similar to Variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableExists_Result_Description">
            <summary>
              Looks up a localized string similar to Returns 1 if a variable with the input name exists, otherwise 0..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableExists_Result_DisplayName">
            <summary>
              Looks up a localized string similar to Result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableExists_VariableName_Description">
            <summary>
              Looks up a localized string similar to The name of the variable..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.VariableExists_VariableName_DisplayName">
            <summary>
              Looks up a localized string similar to Variable name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Abs_Description">
            <summary>
              Looks up a localized string similar to The per region absolute sum of the Positives and Negatives..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Abs_DisplayName">
            <summary>
              Looks up a localized string similar to Abs.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_InvalidPlanesLengthException">
            <summary>
              Looks up a localized string similar to If given the number of planes must be either 1 or the same as the number of regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Negatives_Description">
            <summary>
              Looks up a localized string similar to The sum of the volume below the plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Negatives_DisplayName">
            <summary>
              Looks up a localized string similar to Negatives.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Positives_Description">
            <summary>
              Looks up a localized string similar to The sum of the volume above the plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Positives_DisplayName">
            <summary>
              Looks up a localized string similar to Positives.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Quotas_Description">
            <summary>
              Looks up a localized string similar to The piecewise division of Positive with Negative..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Quotas_DisplayName">
            <summary>
              Looks up a localized string similar to Quotas.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_ReferencePlanes_Description">
            <summary>
              Looks up a localized string similar to Reference planes which will be used as the bottom surface of the volume, if provided the number of planes must be either 1 or the same as the number of regions..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_ReferencePlanes_DisplayName">
            <summary>
              Looks up a localized string similar to Reference planes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_ReferencePlanes_OptionalDescription">
            <summary>
              Looks up a localized string similar to A reference plane with height 0 will be used..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Regions_Description">
            <summary>
              Looks up a localized string similar to A list of regions. The volume will be calculated for each of the regions independently..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Regions_DisplayName">
            <summary>
              Looks up a localized string similar to Regions.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Volumes_Description">
            <summary>
              Looks up a localized string similar to The estimated net volume of the region in relation to the plane..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.Volume_Volumes_DisplayName">
            <summary>
              Looks up a localized string similar to Volumes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.WienerFilter_NoiseVariance_Description">
            <summary>
              Looks up a localized string similar to Manually determined variance of the noise in the image..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.Properties.Resources.WienerFilter_NoiseVariance_DisplayName">
            <summary>
              Looks up a localized string similar to Noise variance.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Tools.ApplyCalibration.LegacyMode">
            <summary>
            For testing purposes against old calibration methods, we need a legacy mode.
            This introduces intentional bugs that have since been fixed.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Tools.CreateProfile.DistanceToFirstPoint(System.Int32)">
            <summary>
            The xy-distance to the first point in the list.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Tools.LoadFromFolder.GetOrderedFileNamesInFolder(System.String)">
            <summary>
            Get all the image files from the folder path, without extensions.
            The file names are ordered by number.
            If there are files that don't contain numbers they will be added last in the list
            </summary>
        </member>
    </members>
</doc>
