<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Sick.Stream.Processing</name>
    </assembly>
    <members>
        <member name="T:Sick.Stream.Processing.ArgumentCategoryAttribute">
            <summary>
            Specifies the <see cref="T:Sick.Stream.Common.ArgumentCategory"/> for the arguments.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ArgumentCategoryAttribute.#ctor(Sick.Stream.Common.ArgumentCategory)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.ArgumentCategoryAttribute"/> with the specified category.
            </summary>
            <param name="category"></param>
        </member>
        <member name="P:Sick.Stream.Processing.ArgumentCategoryAttribute.Category">
            <summary>
            The <see cref="T:Sick.Stream.Common.ToolCategory"/> of the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.AdvancedArgumentAttribute">
            <summary>
            Attribute used specifically for advanced arguments.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.AdvancedArgumentAttribute.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.AdvancedArgumentAttribute"/> with the specified category.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.CategoryAttribute">
            <summary>
            Specifies the <see cref="T:Sick.Stream.Common.ToolCategory"/> for the tool.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.CategoryAttribute.#ctor(Sick.Stream.Common.ToolCategory)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.CategoryAttribute"/> with the specified category.
            </summary>
            <param name="toolCategory"></param>
        </member>
        <member name="P:Sick.Stream.Processing.CategoryAttribute.ToolCategory">
            <summary>
            The <see cref="T:Sick.Stream.Common.ToolCategory"/> of the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.EnableByBooleanAttribute">
            <summary>
            Enable/disable, or show/hide this argument based on another boolean argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.EnableByBooleanAttribute.#ctor(System.String,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.EnableByBooleanAttribute"/> with the specified options.
            </summary>
            <param name="argumentName">The boolean argument which controls the state</param>
            <param name="value">The value to compare the other boolean argument with, e.g. set to true to disable/hide if the other argument is true.</param>
            <param name="hide">Set to true to hide this argument if the other boolean argument matches the specified <paramref name="value"/>.</param>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByBooleanAttribute.ArgumentName">
            <summary>
            Name of the other boolean argument.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByBooleanAttribute.Value">
            <summary>
            The value to compare with, e.g. set to true to disable/hide if the other argument is true.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByBooleanAttribute.Hide">
            <summary>
            Set to true to hide this argument if the other boolean argument matches the specified <see cref="P:Sick.Stream.Processing.EnableByBooleanAttribute.Value"/>.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.EnableByEnumAttribute">
            <summary>
            Enable/disable, or show/hide this argument based on another enum argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.EnableByEnumAttribute.#ctor(System.String,System.Boolean,System.Object[])">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.EnableByEnumAttribute"/> with the specified options.
            </summary>
            <param name="argumentName">The enum argument which controls the state</param>
            <param name="value">The values to compare the other enum argument with.</param>
            <param name="hide">Set to true to hide this argument if the other enum argument matches one of the specified <paramref name="value"/>.</param>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByEnumAttribute.ArgumentName">
            <summary>
            Name of the other enum argument.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByEnumAttribute.Value">
            <summary>
            An array of all values which will hide/disable this argument.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.EnableByEnumAttribute.Hide">
            <summary>
            Set to true to hide this argument if the other enum argument matches one of the enums specified in the <see cref="P:Sick.Stream.Processing.EnableByEnumAttribute.Value"/> array.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.EnumEntriesAttribute">
            <summary>
            Specify the valid enum entries for the enum argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.EnumEntriesAttribute.#ctor(System.Object[])">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.EnableByBooleanAttribute"/> with the specified allowed enum values.
            </summary>
            <param name="entries">Allowed enum values</param>
        </member>
        <member name="P:Sick.Stream.Processing.EnumEntriesAttribute.Entries">
            <summary>
            The allowed enum entries for the argument.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IgnoreAttribute">
            <summary>
            Attribute that can be used on tool properties that are not arguments.
            When building up the argument and result lists these properties will be ignored.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.LevelAttribute">
            <summary>
            Set the required <see cref="P:Sick.Stream.Processing.LevelAttribute.ToolLevel"/> for using the tool.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.LevelAttribute.#ctor(Sick.Stream.Common.ToolLevel)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.LevelAttribute"/> with the specified required license level.
            </summary>
            <param name="toolLevel">The required <see cref="P:Sick.Stream.Processing.LevelAttribute.ToolLevel"/> to use the tool.</param>
        </member>
        <member name="P:Sick.Stream.Processing.LevelAttribute.ToolLevel">
            <summary>
            The required <see cref="P:Sick.Stream.Processing.LevelAttribute.ToolLevel"/> to use the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.LocalizedParameterAttribute">
            <summary>
            Base class for setting the display name, description, and optional description of a tool to predefined values.
            that have the same DisplayName, Description (and if Optional: OptionalDescription).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.LocalizedParameterAttribute.DisplayName">
            <summary>
            The tool display name.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.LocalizedParameterAttribute.Description">
            <summary>
            The tool description.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.LocalizedParameterAttribute.OptionalDescription">
            <summary>
            The tools optional description.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.RangeAttribute">
            <summary>
            Specifies the valid range for an <see cref="T:System.Int32"/> or <see cref="T:System.Double"/> tool argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.RangeAttribute.#ctor(System.Double,System.Double,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.RangeAttribute"/> with the specified
            <paramref name="min"/>, <paramref name="max"/>, <paramref name="excludeMin"/>, <paramref name="excludeMax"/> values.
            </summary>
            <param name="min">The smallest valid value</param>
            <param name="max">The largest valid value</param>
            <param name="excludeMin">If true the Min value will excluded.</param>
            <param name="excludeMax">If true the Max value will be excluded.</param>
            <exception cref="T:System.ArgumentException">Thrown if <paramref name="min"/> >= <paramref name="max"/></exception>
        </member>
        <member name="P:Sick.Stream.Processing.RangeAttribute.Min">
            <summary>
            The smallest valid value
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.RangeAttribute.Max">
            <summary>
            The largest valid value
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.RangeAttribute.ExcludeMin">
            <summary>
            If true the Min value will excluded from the range.
            The input value then needs to be strictly higher than the Min value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.RangeAttribute.ExcludeMax">
            <summary>
            If true the Max value will excluded from the range.
            The input value then needs to be strictly lower than the Max value.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IncrementAttribute">
            <summary>
            Specifies the increment of an <see cref="T:System.Int32"/> tool argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IncrementAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new <see cref="T:Sick.Stream.Processing.IncrementAttribute"/> with the specified <paramref name="increment"/> value.
            </summary>
            <param name="increment">The increment.</param>
        </member>
        <member name="P:Sick.Stream.Processing.IncrementAttribute.Increment">
            <summary>
            The specified increment for the argument.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.UnitAttribute">
            <summary>
            Specifies the <see cref="T:Sick.Stream.Algorithms.UnitType"/> of a tool argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.UnitAttribute.#ctor(Sick.Stream.Algorithms.UnitType)">
            <summary>
            Initializes a new <see cref="T:Sick.Stream.Processing.UnitAttribute"/> with the specified <paramref name="unit"/> value.
            </summary>
            <param name="unit"></param>
        </member>
        <member name="P:Sick.Stream.Processing.UnitAttribute.Unit">
            <summary>
            The specified unit for the argument.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.OptionalAttribute">
            <summary>
            Specifies the argument as optional.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.OptionalAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Sick.Stream.Processing.OptionalAttribute"/>.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.OptionalAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Sick.Stream.Processing.OptionalAttribute"/> with the specified optional description text.
            </summary>
            <param name="optionalDescription">The description displayed to the user.</param>
        </member>
        <member name="P:Sick.Stream.Processing.OptionalAttribute.Description">
            <summary>
            Description text shown to the user.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.ResultAttribute">
            <summary>
            Specifies that the property is a Result (output) of the tool.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ResultAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.ResultAttribute"/> with an optional default value.
            </summary>
            <param name="defaultVariableName">Optional default name for the result variable.</param>
        </member>
        <member name="P:Sick.Stream.Processing.ResultAttribute.DefaultVariableName">
            <summary>
            The default name of the result variable.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.StringTypeAttribute">
            <summary>
            Specifies the property as a string argument of the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.FilePathStringAttribute">
            <summary>
            Specifies the property as a file path string argument of the tool (e.g. adds additional UI input options).
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.FolderPathStringAttribute">
            <summary>
            Specifies the property as a folder path string argument of the tool (e.g. adds additional UI input options).
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.CommaSeparatedStringAttribute">
            <summary>
            Specifies the property as a comma separated string argument of the tool (e.g. adds additional UI input options).
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.CommaSeparatedStringAttribute.#ctor(System.Boolean,Sick.Stream.Common.VariableType[])">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.CommaSeparatedStringAttribute"/> with the specified options.
            </summary>
            <param name="showVariableList">If a variable list should be shown in the UI.</param>
            <param name="types">Variable types shown in the list, only applicable if <paramref name="showVariableList"/> is true.</param>
        </member>
        <member name="P:Sick.Stream.Processing.CommaSeparatedStringAttribute.Types">
            <summary>
            Variable types shown in the list, only applicable if <see cref="P:Sick.Stream.Processing.CommaSeparatedStringAttribute.ShowVariableList"/> is true.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.CommaSeparatedStringAttribute.ShowVariableList">
            <summary>
            If a list of variables should be shown in the UI for this argument.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.MathExpressionStringAttribute">
            <summary>
            Specifies the argument as a math expression (e.g. adds additional UI input options).
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.MatrixStringAttribute">
            <summary>
            Specifies the argument as a matrix string.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.MatrixStringAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.MatrixStringAttribute"/> with the specified <paramref name="size"/>.
            </summary>
            <param name="size">The matrix size.</param>
        </member>
        <member name="P:Sick.Stream.Processing.MatrixStringAttribute.Size">
            <summary>
            The matrix size.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.SerializationIDAttribute">
             <summary>
             Specifies the unique ID identifying the tool.
             This is used for identifying the tool when saving and loading the environment.
            
             The string should be long and random, for instance a uuid. You can for instance
             generate one using powershell:
            
             <code>New-Guid</code>
            
             or python:
             <code>
             python -c "import uuid; print(str(uuid.uuid4()))"
             </code>
            
             After you have created it, never change it. Changing it loses backwards compatibility with
             older environments with that tool.
             </summary>
        </member>
        <member name="M:Sick.Stream.Processing.SerializationIDAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.SerializationIDAttribute"/> with the specified ID.
            </summary>
            <param name="id"></param>
        </member>
        <member name="P:Sick.Stream.Processing.SerializationIDAttribute.ID">
            <summary>
            The specified ID of the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.VariableTypesAttribute">
            <summary>
            Specifies the valid <see cref="T:Sick.Stream.Common.VariableType"/>s for the <see cref="T:Sick.Stream.Algorithms.IVariable"/> argument.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.VariableTypesAttribute.Types">
            <summary>
            The valid variable types.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.VariableTypesAttribute.#ctor(Sick.Stream.Common.VariableType[])">
            <summary>
            Initializes a new instance of <see cref="T:Sick.Stream.Processing.VariableTypesAttribute"/> with the given allowed <see cref="T:Sick.Stream.Common.VariableType"/>s.
            </summary>
            <param name="types"></param>
        </member>
        <member name="T:Sick.Stream.Processing.AllVariableTypesAttribute">
            <summary>
            Specifies that all <see cref="T:Sick.Stream.Common.VariableType"/>s are valid for the given <see cref="T:Sick.Stream.Algorithms.IVariable"/>.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.AllVariableTypesAttribute.#ctor">
            <summary>
            Initializes a new <see cref="T:Sick.Stream.Processing.AllVariableTypesAttribute"/>.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.AllListVariableTypesAttribute">
            <summary>
            Specifies that all variables which supports lists are allowed for the <see cref="T:Sick.Stream.Algorithms.IVariable"/> argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.AllListVariableTypesAttribute.#ctor">
            <summary>
            Initializes a new <see cref="T:Sick.Stream.Processing.AllListVariableTypesAttribute"/>.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Common.Dispatcher.CurrentUiThreadDispatcher">
            <summary>
            Get the current UI dispatcher, might be null if not in a UI application.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Common.ExpressionUtils.IsStringExpression(org.matheval.Expression,System.Collections.Generic.IEnumerable{Sick.Stream.Algorithms.IVariable})">
            <summary>
            Check if it's a valid string expression.
            A valid string expression looks like this: MyString == "string value"
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Common.ExpressionUtils.GetExpressionRoot(org.matheval.Expression)">
            <summary>
            Get the NonPublic field Root from the expression object.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Common.GuiThreadAwareObservableCollection`1">
            <summary>
            Class inherited from ObservableCollection.
            It posts CollectionChanged event on GUI thread..
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IProcessingEnvironment">
            <summary>
            The processing environment is the entry point when working with Stream Editor from the C# API.
            It mainly consists of the list of <see cref="T:Sick.Stream.Processing.IStepProgram"/>s and a list of <see cref="T:Sick.Stream.Algorithms.IVariable"/>s.
            The processing environment contains functions for saving and loading an environment,
            and functions for creating new variables and step programs.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IProcessingEnvironment.Programs">
            <summary>
            List containing all step programs.
            Use the function <see cref="M:Sick.Stream.Processing.IProcessingEnvironment.CreateStepProgram(System.String)"/> to create and add a new step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IProcessingEnvironment.Variables">
            <summary>
            List containing all variables.
            Use the create function for each variable type to create and add new variables.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IProcessingEnvironment.SelectedProgram">
            <summary>
            The currently selected step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IProcessingEnvironment.EnvironmentPath">
            <summary>
            Path to the environment.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.AddVariable(Sick.Stream.Algorithms.IVariable)">
             <summary>
             Adds a variable to the variable list. If the variable has the same name as
             another variable in the environment it will be overwritten.
            
             This transfers ownership of the variable to this ProcessingEnvironment, which will
             then be responsible for disposing it.
             </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateStepProgram(System.String)">
            <summary>
            Creates a new step program and adds it to the program list.
            </summary>
            <param name="name">The name of the new step program.</param>
            <returns>The new step program.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.LoadStepProgram(System.String,System.String)">
            <summary>
            Loads a step program from an xml-file.
            </summary>
            <param name="name">The name of the step program</param>
            <param name="filePath">The file path</param>
            <returns>The loaded step program</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetStepProgram(System.String)">
            <summary>
            Gets the program in the environment with the given name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateImage(System.String)">
            <summary>
            Creates a new Image variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Image variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreatePixelRegion(System.String)">
            <summary>
            Creates a new PixelRegion variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new PixelRegion variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateTransform2D(System.String)">
            <summary>
            Creates a new Transform2D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Transform2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateTransform3D(System.String)">
            <summary>
            Creates a new Transform3D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Transform3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateRectangle(System.String)">
            <summary>
            Creates a new Rectangle variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Rectangle variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateEllipse(System.String)">
            <summary>
            Creates a new Ellipse variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Ellipse variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateNumber(System.String)">
            <summary>
            Creates a new Number variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Number variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreatePoint2D(System.String)">
            <summary>
            Creates a new Point2D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Point2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreatePoint3D(System.String)">
            <summary>
            Creates a new Point3D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Point3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateLine2D(System.String)">
            <summary>
            Creates a new Line2D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Line2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateLine3D(System.String)">
            <summary>
            Creates a new Line3D variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Line3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreatePlane(System.String)">
            <summary>
            Creates a new Plane variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Plane variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateProfile(System.String)">
            <summary>
            Creates a new Profile variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Profile variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateString(System.String)">
            <summary>
            Creates a new String variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new String variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateText(System.String)">
            <summary>
            Creates a new Text variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new Text variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreateCalibrationModel(System.String)">
            <summary>
            Creates a new CalibrationModel variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new CalibrationModel variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.CreatePointCloud(System.String)">
            <summary>
            Creates a new PointCloud variable and adds it to the variable list.
            </summary>
            <param name="name">The variable name.</param>
            <returns>The new PointCloud variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.AddFrame(System.String,Sick.GenIStream.IFrame)">
            <summary>
            Creates an image from a GenIStream frame
            and adds it to the variable list.
            </summary>
            <param name="name">Name of the new image variable.</param>
            <param name="frame">The GenIStream frame.</param>
            <returns>The new image variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.LoadImage(System.String,System.String)">
            <summary>
            Loads an image from file and adds it to the variable list.
            </summary>
            <param name="name">Name of the new image variable.</param>
            <param name="filePath">The image file path.</param>
            <returns></returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.RemoveVariable(Sick.Stream.Algorithms.IVariable)">
             <summary>
             Disposes the input variable and removes it from the environment.
            
             <remarks>The variable is not safe to use after this operation.</remarks>
             </summary>
             <param name="variable">The variable to be removed.</param>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.Save(System.String)">
            <summary>
            Saves the environment to disk.
            </summary>
            <param name="filePath">Path to the file location.</param>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.Load(System.String)">
            <summary>
            Loads an environment from disk.
            </summary>
            <param name="filePath">Path to the env-file.</param>
            <returns>The result of the load operation.
            Contains information about steps and variables that couldn't be loaded.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.SaveVariable(System.String,Sick.Stream.Algorithms.IVariable)">
            <summary>
            Saves a variable to disk, as a .vari file.
            </summary>
            <param name="filePath">Path where the variable will be saved.</param>
            <param name="variable">The variable to save.</param>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.VariableExists(System.String)">
            <summary>
            Checks if a variable with the given name exists in the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>True if the variable exists in the environment.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetVariable(System.String)">
            <summary>
            Gets the variable by the given name.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The variable with the given name.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetImage(System.String)">
            <summary>
            Gets an Image variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Image variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetPixelRegion(System.String)">
            <summary>
            Gets a PixelRegion variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The PixelRegion variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetTransform2D(System.String)">
            <summary>
            Gets a Transform2D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Transform2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetTransform3D(System.String)">
            <summary>
            Gets a Transform3D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Transform3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetRectangle(System.String)">
            <summary>
            Gets a Rectangle variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Rectangle variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetEllipse(System.String)">
            <summary>
            Gets an Ellipse variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Ellipse variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetNumber(System.String)">
            <summary>
            Gets a Number variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Number variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetPoint2D(System.String)">
            <summary>
            Gets a Point2D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Point2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetPoint3D(System.String)">
            <summary>
            Gets a Point3D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Point3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetLine2D(System.String)">
            <summary>
            Gets a Line2D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Line2D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetLine3D(System.String)">
            <summary>
            Gets a Line3D variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Line3D variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetPlane(System.String)">
            <summary>
            Gets a Plane variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Plane variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetProfile(System.String)">
            <summary>
            Gets a Profile variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Profile variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetString(System.String)">
            <summary>
            Gets a String variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The String variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetText(System.String)">
            <summary>
            Gets a Text variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The Text variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetCalibrationModel(System.String)">
            <summary>
            Gets a CalibrationModel variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The CalibrationModel variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetPointCloud(System.String)">
            <summary>
            Gets a PointCloud variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The PointCloud variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.GetObjectLocator(System.String)">
            <summary>
            Gets an ObjectLocator variable from the variable list.
            </summary>
            <param name="name">The name of the variable.</param>
            <returns>The ObjectLocator variable.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.LoadVariable(System.String,System.String)">
            <summary>
            Loads a variable from a .vari or .dat file from disk and adds it to the environment.
            </summary>
            <param name="name">The variable name.</param>
            <param name="filePath">The path to the .vari or .dat file</param>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.SetEmptyEnvironment">
            <summary>
            Clears the environment from variables and programs and creates an empty environment.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IProcessingEnvironment.ClearAllVariables">
            <summary>
            Clear all variables in the variable list.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IStep">
            <summary>
            Generic interface for all step classes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.SerializationID">
            <summary>
            The serialization ID of for the step, a long unique string.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Name">
            <summary>
            The name of the step. Can be used as an ID to find a specific step in a step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.DisplayName">
            <summary>
            A display name created from the TypeName.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Type">
            <summary>
            The step type. For a ToolStep this will be the type of the tool.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.ProgramFlowLevel">
            <summary>
            The step's indentation.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Comment">
            <summary>
            The step's comment.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.BlockComment">
            <summary>
            The step's block comment.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.ToolLevel">
            <summary>
            The step's license level.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Category">
            <summary>
            The step's category.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.IsEnabled">
            <summary>
            If set to false, the tool will be inactive
            and will not run when executed.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.HasError">
            <summary>
            True if the step got an error during the last run.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Arguments">
            <summary>
            List of all the input arguments.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.Results">
            <summary>
            List of all the output results.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IStep.Copy">
            <summary>
            Returns a copy of the step.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStep.RunTime">
            <summary>
            The latest processing time of the step
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IStepProgram">
            <summary>
            Interface for the step programs.
            A step program contains a list of steps and functions for running the step program, or individual steps.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStepProgram.Name">
            <summary>
            The name of the step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStepProgram.Steps">
            <summary>
            List of all the steps in the step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStepProgram.SelectedStep">
            <summary>
            The currently selected step.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.IStepProgram.SelectedIndex">
            <summary>
            The index of the selected step.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.AddStep(Sick.Stream.Processing.IStep)">
            <summary>
            Adds a new step to the end of the program.
            </summary>
            <param name="step">The step to add.</param>
            <exception cref="T:Sick.Stream.Common.StreamEditorException">If the step already exists in the program.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.AddStep``1(``0)">
            <summary>
            Adds a new tool step to the end of the program.
            </summary>
            <param name="tool">The tool to use for the step.</param>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.InsertStep(System.Int32,Sick.Stream.Processing.IStep)">
            <summary>
            Adds a new step at the given index.
            </summary>
            <param name="index">The index where the step should be added.</param>
            <param name="step">The step to add.</param>
            <exception cref="T:Sick.Stream.Common.StreamEditorException">If the step already exists in the program.</exception>
            <exception cref="T:System.IndexOutOfRangeException">If the given index is out of range.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.InsertStep``1(System.Int32,``0)">
            <summary>
            Adds a new tool step at the given index.
            </summary>
            <typeparam name="T">Type of tool</typeparam>
            <param name="index">The index where the step should be added.</param>
            <param name="tool">The tool to run for the inserted step.</param>
            <exception cref="T:Sick.Stream.Common.StreamEditorException">If the step already exists in the program.</exception>
            <exception cref="T:System.IndexOutOfRangeException">If the given index is out of range.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.RemoveStep(Sick.Stream.Processing.IStep)">
            <summary>
            Removes the given step from the StepProgram.
            </summary>
            <param name="step">The step to remove.</param>
            <exception cref="T:Sick.Stream.Common.StreamEditorException">If the step doesn't exist in the program.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.RemoveStepAt(System.Int32)">
            <summary>
            Removes the step at the given index from the StepProgram.
            </summary>
            <param name="index">The index of the step to remove.</param>
            <exception cref="T:System.IndexOutOfRangeException">If the given index is out of range.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.RemoveManySteps(System.Collections.Generic.IEnumerable{Sick.Stream.Processing.IStep})">
            <summary>
            Removes all the steps in the given collection from the StepProgram.
            </summary>
            <exception cref="T:Sick.Stream.Common.StreamEditorException">If any of the step don't exist in the program.</exception>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.ClearSteps">
            <summary>
            Clears the StepProgram from all it's steps.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.RunStep(Sick.Stream.Processing.IStep)">
            <summary>
            Runs a step in the step program.
            </summary>
            <param name="step">The step to run.</param>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.RunProgram">
            <summary>
            Runs all the steps in the step program.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.Copy(System.String)">
            <summary>
            Copies all steps to a new step program.
            </summary>
            <param name="name">The name of the new step program.</param>
            <returns>Returns a copy of the step program.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.IStepProgram.MoveStep(System.Int32,System.Int32)">
            <summary>
            Moves a step to a new position in the step list.
            </summary>
            <param name="oldIndex">The old index.</param>
            <param name="newIndex">The new index.</param>
        </member>
        <member name="T:Sick.Stream.Processing.ITool">
            <summary>
            Generic interface for all tools.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IToolInitialize">
            <summary>
            Optional interface to implement to get notified when the tool has been initialized and the arguments are set up.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.IToolStep`1">
            <summary>
            Interface implemented by steps that contain a tool.
            </summary>
            <typeparam name="T">The type of the tool</typeparam>
        </member>
        <member name="T:Sick.Stream.Processing.IToolStep">
            <summary>
            Interface implemented by steps that contain a tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.ProcessingEnvironment">
            <summary>
            Class implementing <see cref="T:Sick.Stream.Processing.IProcessingEnvironment"/>.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.#ctor">
            <summary>
            Initializes a new instance of the ProcessingEnvironment class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.ProcessingEnvironment.Programs">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.ProcessingEnvironment.Variables">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.ProcessingEnvironment.SelectedProgram">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.ProcessingEnvironment.EnvironmentPath">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateStepProgram(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetStepProgram(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.AddVariable(Sick.Stream.Algorithms.IVariable)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateImage(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateRectangle(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateTransform2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateTransform3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateEllipse(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreatePixelRegion(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateNumber(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreatePoint2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreatePoint3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateLine2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateLine3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreatePlane(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateProfile(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateString(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateText(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreateCalibrationModel(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.CreatePointCloud(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.AddFrame(System.String,Sick.GenIStream.IFrame)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.LoadImage(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.Save(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.Load(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.SaveVariable(System.String,Sick.Stream.Algorithms.IVariable)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.LoadVariable(System.String,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.SetEmptyEnvironment">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.RemoveVariable(Sick.Stream.Algorithms.IVariable)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.VariableExists(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetVariable(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetImage(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetPointCloud(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetPixelRegion(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetTransform2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetTransform3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetRectangle(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetEllipse(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetNumber(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetPoint2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetPoint3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetLine2D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetLine3D(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetPlane(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetProfile(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetString(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetText(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetCalibrationModel(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.ProcessingEnvironment.GetObjectLocator(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.ResultName">
            <summary>
            Class used to initializing tool results.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetImage(System.String)">
            <summary>
            Initializes an Image result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetObjectLocator(System.String)">
            <summary>
            Initializes an ObjectLocator result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetPointCloud(System.String)">
            <summary>
            Initializes a PointCloud result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetCalibrationModel(System.String)">
            <summary>
            Initializes a CalibrationModel result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetPoint2D(System.String)">
            <summary>
            Initializes a Point2D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetPoint3D(System.String)">
            <summary>
            Initializes a Point3D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetLine2D(System.String)">
            <summary>
            Initializes a Line2D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetLine3D(System.String)">
            <summary>
            Initializes a Line3D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetRectangle(System.String)">
            <summary>
            Initializes a Rectangle result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetEllipse(System.String)">
            <summary>
            Initializes an Ellipse result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetPixelRegion(System.String)">
            <summary>
            Initializes a PixelRegion result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetPlane(System.String)">
            <summary>
            Initializes a Plane result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetNumber(System.String)">
            <summary>
            Initializes a Number result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetProfile(System.String)">
            <summary>
            Initializes a Profile result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetString(System.String)">
            <summary>
            Initializes a String result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetText(System.String)">
            <summary>
            Initializes a Text result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetTransform2D(System.String)">
            <summary>
            Initializes a Transform2D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetTransform3D(System.String)">
            <summary>
            Initializes a Transform3D result.
            </summary>
            <param name="name">The name of the result.</param>
        </member>
        <member name="M:Sick.Stream.Processing.ResultName.SetVariable(System.String)">
            <summary>
            Initializes an IVariable result.
            </summary>
            <param name="name">The name of the result</param>
        </member>
        <member name="T:Sick.Stream.Processing.StepBase">
            <summary>
            Abstract base class for all Tool classes.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.SerializationID">
             <summary>
             The unique ID identifying the tool. Set this using the <see cref="P:Sick.Stream.Processing.StepBase.SerializationID"/> attribute.
             This is used for identifying the tool when saving and loading the environment.
            
             The string should be long and random, for instance a uuid. You can for instance
             generate one using python:
             <code>
             python -c "import uuid; print(str(uuid.uuid4()), end='')"
             </code>
            
             After you have created it, never change it. Changing it loses backwards compatibility with
             older environments with that tool.
             </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.#ctor">
            <summary>
            The function name is created from the name of tool.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Name">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.DisplayName">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Type">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.ProgramFlowLevel">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Comment">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.ToolLevel">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Category">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.IsEnabled">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.Apply">
            <summary>
            Function called when running the step.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.Run">
            <summary>
            Function called when running the step.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Arguments">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.Results">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.HasError">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepBase.RunTime">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.UpdateAllArguments(Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Updates the variable references in the Argument list from
            the input variable list.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.UpdateVariableProperty(Sick.Stream.Processing.IProcessingEnvironment,System.Reflection.PropertyInfo)">
            <summary>
            Updates a variable property from the variables in the variable list.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.UpdateNumberProperty(Sick.Stream.Processing.IProcessingEnvironment,Sick.Stream.Processing.Parameters.NumberArgument,System.Reflection.PropertyInfo)">
            <summary>
            Updates and validates a number property.
            If the argument value is not a numeric value, check for a variable in the variable list.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.UpdateStringProperty(Sick.Stream.Processing.IProcessingEnvironment,Sick.Stream.Processing.Parameters.StringArgument,System.Reflection.PropertyInfo)">
            <summary>
            Updates and validates a string property.
            If the argument value is a String variable with a single element, update the property with the element value.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.InitializeAllResults(Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Re-initializes the variables in the Result list.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.UpdateVariableList(Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Update the variable list in the ProcessingEnvironment with the
            non-empty result variables. Remove any existing variables in the
            variable list with the same name.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepBase.SetArgument(System.String,System.String)">
            <summary>
            Function for setting a value to an argument as a string.
            In some cases it is not possible to set an argument value directly to the tool properties. For example,
            if an argument is a Point2DList it can also be parsed from a string, like this (2 3),(2 3).
            </summary>
            <param name="name">The name of the argument</param>
            <param name="value">The argument value</param>
        </member>
        <member name="T:Sick.Stream.Processing.StepProgram">
            <summary>
            Step program class.
            Contains a list of steps and functions for running all steps in the list.
            And function for running individual steps.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.#ctor(System.String,Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Initializes a new instance of the StepProgram class.
            </summary>
            <param name="name">Name of the step program.</param>
            <param name="env">The <see cref="T:Sick.Stream.Processing.ProcessingEnvironment"/> containing this StepProgram.</param>
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.Name">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.Steps">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.SelectedStep">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.SelectedIndex">
            <summary>
            Get the current selected index.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.ResetStepIndex">
            <summary>
            Set the selected step to the first step in the list. Also reset all
            for steps counters.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.RunTime">
            <summary>
            The execution time of the step program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.StepProgram.ProgramCount">
            <summary>
            The number of times the Program has run.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.AddStep(Sick.Stream.Processing.IStep)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.AddStep``1(``0)">
            <inheritdoc/>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.InsertStep(System.Int32,Sick.Stream.Processing.IStep)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RemoveStep(Sick.Stream.Processing.IStep)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RemoveStepAt(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RemoveManySteps(System.Collections.Generic.IEnumerable{Sick.Stream.Processing.IStep})">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.ClearSteps">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RunStep(Sick.Stream.Processing.IStep)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RunSelectedStep">
            <summary>
            Run the currently selected step.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.StepOver">
            <summary>
            Run the currently selected step and move to next step.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.RunProgram">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.Copy(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.StepProgram.UpdateProgramRunTime">
            <summary>
            Update the processing time for the program.
            It is the sum of the processing time for each step.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.StepProgramExtensions">
            <summary>
            Extenstion methods for the StepProgram class.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramExtensions.FindStep``1(Sick.Stream.Processing.IStepProgram)">
            <summary>
            Finds the first step in the step program with the given tool type.
            </summary>
            <typeparam name="T">The tool type</typeparam>
            <param name="stepProgram">The step program</param>
            <returns>The first found step, otherwise null.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramExtensions.FindStep``1(Sick.Stream.Processing.IStepProgram,System.String)">
            <summary>
            Finds the first step in the step program with the given name and tool type.
            </summary>
            <typeparam name="T">The tool type</typeparam>
            <param name="stepProgram">The step program</param>
            <param name="name">The name of the step</param>
            <returns>The first found step, otherwise null.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramExtensions.FindStep(Sick.Stream.Processing.IStepProgram,System.String)">
            <summary>
            Finds the first step in the step program with the given name and tool type.
            </summary>
            <param name="stepProgram">The step program</param>
            <param name="name">The name of the step</param>
            <returns>The first found step, otherwise null.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramExtensions.Save(Sick.Stream.Processing.IStepProgram,System.String)">
            <summary>
            Saves the step program to an xml-file.
            </summary>
            <param name="stepProgram">The step program</param>
            <param name="filePath">The file path</param>
        </member>
        <member name="T:Sick.Stream.Processing.ToolDescription">
            <summary>
            Contains all user relevant information of a Tool
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolDescription.GetToolDescription(Sick.Stream.Processing.IStep,System.Type)">
            <summary>
            Get the file content of the markdown file containing the description for the tool step.
            If no markdown file for the tool exist, the value of the description attribute is returned.
            If no tool is selected the content of Start.md file will be returned.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.ToolBox">
            <summary>
            Helper class that contain a list of all available tools.
            Used to load environment and populate AddStepVM without instantiating tool classes.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolBox.VerifyPluginReferenceVersions(System.Reflection.Assembly)">
            <summary>
            Verifies that all Sick.Stream packages in the plugin assembly matches the version of Stream Editor.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Environment.ToolExecutionContext">
            <summary>
            Context in which the tool is currently executing in. Allows internal tools to access the current runtime environment.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Environment.ToolExecutionContext.ProcessingEnvironment">
            <summary>
            The processing environment in which the tool is executing.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Environment.ToolExecutionContext.Program">
            <summary>
            The program which is executing the tool.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.ToolParameterProvider">
            <summary>
            Class used by ToolBase to generate the two parameter lists Arguments and Results
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolParameterProvider.CreateArgumentList">
            <summary>
            Generates the argument list for the for the tool based on the tool properties and their attributes.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolParameterProvider.CreateResultList">
            <summary>
            Generate the result list for the tool.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolParameterProvider.CreateEmptyArgument(System.Reflection.PropertyInfo)">
            <summary>
            Function for creating an Argument from a tool property.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.ToolParameterProvider.CreateStringArgument(System.Reflection.PropertyInfo)">
            <summary>
            Creates different types of StringArgument from the property.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.ToolResources">
            <summary>
            Class for accessing the resource strings in a tool plugin assembly.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.ActionArgument">
            <summary>
            Argument class containing an Action.
            An argument of this class will appear as a button in the GUI.
            </summary>
        </member>
        <member name="F:Sick.Stream.Processing.Parameters.ActionArgument.Action">
            <summary>
            Action that will be triggered when clicking the argument button.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.ActionArgument.Content">
            <summary>
            Used the set the content of the button.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.ArgumentBase`1">
            <summary>
            Argument class that takes a variable name as input,
            or a string that can be parsed to a variable.
            </summary>
            <typeparam name="T">The type of variable the parameter is associated with.</typeparam>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.ArgumentBase`1.TryParse(System.String)">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.ArgumentBase`1.IsEmpty">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.ArgumentBase`1.Type">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.ArgumentBase`1.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.BooleanArgument">
            <summary>
            Argument class for handling booleans.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.BooleanArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.BooleanArgument.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.CameraSelectorArgument">
            <summary>
            Argument type used by the GrabFromCamera tool for displaying
            discovered cameras together with their name and status.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.CameraSelectorArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.CameraSelectorArgument.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IEnumArgument">
            <summary>
            Interface for <see cref="T:Sick.Stream.Processing.Parameters.EnumArgument`1"/>
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.EnumArgument`1">
            <summary>
            Argument class for handling enum.
            </summary>
            <typeparam name="T">The enum type.</typeparam>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.EnumArgument`1.Values">
            <summary>
            List of all the enum values for the enum type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.EnumArgument`1.EnumValue">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.EnumArgument`1.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.EnumArgument`1.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IArgument">
            <summary>
            Interface for arguments to be used in a <see cref="T:Sick.Stream.Processing.StepBase"/>,
            which inherits from <see cref="T:Sick.Stream.Processing.Parameters.IParameter"/>. Adding a property with a class implementing
            this interface to a <see cref="T:Sick.Stream.Processing.StepBase"/> automatically makes it an argument.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IArgument.OptionalDescription">
            <summary>
            Describes the default behavior of an optional argument, that is, what should
            happen when this field is left empty. Is null if the argument is mandatory.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IArgument.Category">
            <summary>
            The argument category.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.InvalidParameterException">
            <summary>
            Exception for when an invalid argument has been entered.
            Is used instead of <see cref="T:System.ArgumentException"/> since it causes problems during startup.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.InvalidParameterException.#ctor(System.String)">
            <summary>
            Constructs an <see cref="T:Sick.Stream.Processing.Parameters.InvalidParameterException"/> with the given message.
            </summary>
            <param name="msg">The error message, which will be displayed to the user.</param>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IParameter">
            <summary>
            Interface for parameters, which encompasses arguments and results.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.Description">
            <summary>
            The description of the parameter. This is shown in the parameter tooltip, and in the tool description.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.IsOptional">
            <summary>
            Whether a value for this parameter is optional.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.Name">
            <summary>
            The name of the parameter.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.DisplayName">
            <summary>
            The display name of the parameter.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.IsEmpty">
            <summary>
            True if the parameter is empty.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.IsEnabled">
            <summary>
            True if the argument input is enabled in the GUI.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.Unit">
            <summary>
            The parameter unit.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IParameter.IsVisible">
            <summary>
            True if the argument is shown in the GUI.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.IParameter.ToString">
            <summary>
            Get the argument value as a string.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.IParameter.Deserialize(System.String)">
            <summary>
            Sets the argument value from the input string.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.IParameter.DeserializeWithValidation(System.String)">
            <summary>
            As Deserialize but throw exception on validation errors.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IResult">
            <summary>
            Interface for results to be used in a <see cref="T:Sick.Stream.Processing.StepBase"/>,
            which inherits from <see cref="T:Sick.Stream.Processing.Parameters.IParameter"/>. Adding a property with a class implementing
            this interface to a <see cref="T:Sick.Stream.Processing.StepBase"/> automatically makes it a result.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IResult.Value">
            <summary>
            The input value. Could be a name of a variable
            or a string that can be parsed to have variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IResult.Type">
            <summary>
            The variable type of the result.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IVariableArgument">
            <summary>
            Interface for <see cref="T:Sick.Stream.Processing.Parameters.ArgumentBase`1"/>
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IVariableArgument.Value">
            <summary>
            The input value. Could be a name of a variable
            or a string that can be parsed to have variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IVariableArgument.Type">
            <summary>
            The type of the variable.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.IVariableArgument.TryParse(System.String)">
            <summary>
            Parses a string to an <see cref="T:Sick.Stream.Algorithms.IVariable"/>.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.MultiTypeArgument">
            <summary>
            Attribute that allow for multiple types.
            The <see cref="T:Sick.Stream.Processing.VariableTypesAttribute"/> decides which types are allowed.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.MultiTypeArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.MultiTypeArgument.ToString">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.MultiTypeArgument.Type">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.MultiTypeArgument.TryParse(System.String)">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.NumberArgument">
            <summary>
            Argument type used for double and int properties in tools.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.Min">
            <summary>
            The minimum allowed value. Can be null to not have a minimum.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.Max">
            <summary>
            The maximum allowed value. Can be null to not have a maximum.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.Increment">
            <summary>
            The increments allowed for the value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.ExcludeMin">
            <summary>
            If true the input value must be strictly higher than the Min value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.ExcludeMax">
            <summary>
            If true the input value must be strictly lower than the Max value.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.NumberType">
            <summary>
            The number type.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.NumberArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.Parse(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.ToString">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.SetValue(System.Nullable{System.Double})">
            <summary>
            Sets the value string from numeric value.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.TryGetNumber">
            <summary>
            Will return a number if the argument has a numeric value and else null.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.TryParse(System.String)">
            <summary>
             Returns a number if the argument has a numeric value and else null.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.Validate(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.NumberArgument.FormatInput(System.String)">
            <summary>
            If input decimal separator is a comma it is replaced with a dot.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.FloatArgument">
            <summary>
            Argument for handling double argument values.
            Remark: This class is currently only used in Stream Setup.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.FloatArgument.Min">
            <summary>
            The minimum allowed value. Can be null to not have a minimum.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.FloatArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.FloatArgument.Max">
            <summary>
            The maximum allowed value. Can be null to not have a maximum.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.FloatArgument.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.IntegerArgument">
            <summary>
            Argument for handling integer argument values.
            Remark: This class is currently only used in Stream Setup.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IntegerArgument.Min">
            <summary>
            The minimum allowed value. Can be null to not have a minimum.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IntegerArgument.Max">
            <summary>
            The maximum allowed value. Can be null to not have a minimum.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IntegerArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.IntegerArgument.Increment">
            <summary>
            The increments allowed for the value.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.IntegerArgument.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.ParameterBase`1">
            <summary>
            Base class for all Argument and Result classes.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.ParameterUtils.EnableOnEnumValue(Sick.Stream.Processing.Parameters.IParameter,Sick.Stream.Processing.Parameters.IEnumArgument,System.Boolean,System.Object[])">
            <summary>
            Enable or show the parameter only if the input enum parameter has the specific enum value(s).
            </summary>
            <param name="parameter">The input EnumArgument.</param>
            <param name="enumArgument"></param>
            <param name="hide">If true, the parameter will be hidden, instead of just disabled.</param>
            <param name="values">The enum value(s) that will enable or show the parameter.</param>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.ParameterUtils.EnableOnCheckboxValue(Sick.Stream.Processing.Parameters.IParameter,Sick.Stream.Processing.Parameters.BooleanArgument,System.Boolean,System.Boolean)">
            <summary>
            Enable or show the parameter only if the input bool parameter is true or false.
            </summary>
            <param name="parameter">The input BooleanArgument.</param>
            <param name="booleanArgument"></param>
            <param name="value">True or false, depending on when the parameter should be enabled.</param>
            <param name="hide">If true, the parameter will be hidden, instead of just disabled.</param>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.ParameterUtils.GetVariableType(System.Type)">
            <summary>
            Get the VariableType enum associated with input variable type.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.RegionArgument">
            <summary>
            Argument class for <see cref="T:Sick.Stream.Algorithms.IPixelRegionList"/>. Can also take in
            either a <see cref="T:Sick.Stream.Algorithms.IRectangleList"/> or a <see cref="T:Sick.Stream.Algorithms.IEllipseList"/>
            and convert them to pixel regions.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.ResultBase`1">
            <summary>
            Abstract base class for the result types.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.StringArgument">
            <summary>
            Argument for handling string argument values.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.StringArgument.IsEmpty">
            <inheritdoc />
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.StringArgument.ToString">
            <inheritdoc />
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.FileSelectorArgument">
            <summary>
            Argument for handling file argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.FileSelectorArgument.GetAbsolutePath">
            <summary>
            If Value is a relative path, return the full path
            otherwise return Value
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.FolderSelectorArgument">
            <summary>
            Argument for handling folder argument.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.Parameters.FolderSelectorArgument.GetAbsolutePath">
            <summary>
            If Value is a relative path, return the full path
            otherwise return Value
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.MathExpressionArgument">
            <summary>
            Argument for handling math expression inputs. Comes with an button for opening a calculator.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.MatrixArgument">
            <summary>
            Argument for matrices. Comes with an button for opening a matrix editor.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.StringRefreshArgument">
            <summary>
            Argument for handling string argument values. Comes with an button for refreshing.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.CommaSeparatedStringArgument">
            <summary>
            Attribute for handling a comma separated list of strings />
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.CommaSeparatedStringArgument.Types">
            <summary>
            List of all the variable type that are allowed.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Parameters.CommaSeparatedStringArgument.ShowVariableList">
            <summary>
            True if the variables from the variable list should also be shown.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Parameters.StepProgramSelector">
            <summary>
            Argument class for selecting a step program.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ArgumentBase_InvalidArgumentMessage">
            <summary>
              Looks up a localized string similar to &quot;{0}&quot; is not a valid argument name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.CalibrationModel_DisplayValue">
            <summary>
              Looks up a localized string similar to Calibration error: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.CalibrationModel_ToolTipAlignmentError">
            <summary>
              Looks up a localized string similar to Alignment error:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.CalibrationModel_ToolTipAlignmentMaxError">
            <summary>
              Looks up a localized string similar to Alignment max error:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.CalibrationModel_ToolTipCalibrationError">
            <summary>
              Looks up a localized string similar to Calibration error:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.CalibrationModel_ToolTipCalibrationMaxError">
            <summary>
              Looks up a localized string similar to Calibration max error:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Common_ArgumentDoesNotSupportListMessage">
            <summary>
              Looks up a localized string similar to The input argument &apos;{0}&apos; does not support variables with multiple values..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Else_MissingPreviousIfStepException">
            <summary>
              Looks up a localized string similar to Else step is missing a previous if step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Else_UnclosedElseStepException">
            <summary>
              Looks up a localized string similar to Else step is missing a closing end step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.End_ExcessiveClosingEndException">
            <summary>
              Looks up a localized string similar to An extra end step not used have been encountered..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.EnumArgument_InvalidEnumException">
            <summary>
              Looks up a localized string similar to Invalid enum argument value: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.EnvironmentLoadResult_StepLoadError">
            <summary>
              Looks up a localized string similar to The following steps could not be loaded:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.EnvironmentLoadResult_VariableLoadError">
            <summary>
              Looks up a localized string similar to The following variables could not be loaded:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_Index_Description">
            <summary>
              Looks up a localized string similar to The loop index for the current iteration..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_Index_DisplayName">
            <summary>
              Looks up a localized string similar to Index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_InvalidIterationsException">
            <summary>
              Looks up a localized string similar to For step is missing closing end step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_Iterations_Description">
            <summary>
              Looks up a localized string similar to The number of times the for-loop will be executed..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_Iterations_DisplayName">
            <summary>
              Looks up a localized string similar to Iterations.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_StartIndex_Description">
            <summary>
              Looks up a localized string similar to The start index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_StartIndex_DisplayName">
            <summary>
              Looks up a localized string similar to Start index.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.For_UnclosedIfStepException">
            <summary>
              Looks up a localized string similar to For step is missing closing end step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.If_Expression_Description">
            <summary>
              Looks up a localized string similar to The expression that will be evaluated..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.If_Expression_DisplayName">
            <summary>
              Looks up a localized string similar to Expression.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.If_InvalidExpression1">
            <summary>
              Looks up a localized string similar to The expression needs to evaluate to a single value..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.If_InvalidExpression2">
            <summary>
              Looks up a localized string similar to Invalid math expression..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.If_UnclosedIfStepException">
            <summary>
              Looks up a localized string similar to If step is missing closing end step..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_DisplayValue_Unit">
            <summary>
              Looks up a localized string similar to Unit:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipDetectionThreshold">
            <summary>
              Looks up a localized string similar to Detection threshold:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipExposure">
            <summary>
              Looks up a localized string similar to Exposure:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipMultiSlopeMode">
            <summary>
              Looks up a localized string similar to Multi slope mode:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipOffset">
            <summary>
              Looks up a localized string similar to Offset:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipScale">
            <summary>
              Looks up a localized string similar to Scale:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipSearchDirection">
            <summary>
              Looks up a localized string similar to Search direction:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipSize">
            <summary>
              Looks up a localized string similar to Size:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipUnit">
            <summary>
              Looks up a localized string similar to Unit:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipWamSize">
            <summary>
              Looks up a localized string similar to Wam size:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipZmax">
            <summary>
              Looks up a localized string similar to Z max:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Image_ToolTipZmin">
            <summary>
              Looks up a localized string similar to Z min:.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ImageArgument_ImageDoesNotContainAllowedTypeMessage">
            <summary>
              Looks up a localized string similar to Input image {0} does not contain any of the following allowed image types: &quot;{1}&quot;..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.LicenseLevelInfo__Deprecated">
            <summary>
              Looks up a localized string similar to Licensed (Deprecated).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.LicenseLevelInfo_Experimental">
            <summary>
              Looks up a localized string similar to Licensed (Experimental).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.LicenseLevelInfo_Internal">
            <summary>
              Looks up a localized string similar to Licensed (Internal).
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.LicenseLevelInfo_Licensed">
            <summary>
              Looks up a localized string similar to Licensed.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.LicenseLevelInfo_Open">
            <summary>
              Looks up a localized string similar to Open.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_CouldNotFindStringException">
            <summary>
              Looks up a localized string similar to Could not find a string variable with the name: {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_InvalidExpressionException">
            <summary>
              Looks up a localized string similar to Failed to evaluate expression..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_InvalidExpressionInput">
            <summary>
              Looks up a localized string similar to Invalid expression input.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_InvalidStringExpression">
            <summary>
              Looks up a localized string similar to A String compare expression must be on the form: StringVariable == &quot;string value&quot;.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_InvalidStringLengthException">
            <summary>
              Looks up a localized string similar to The String variable must have length 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MathExpression_InvalidVariableTypeException">
            <summary>
              Looks up a localized string similar to Only variables of the &apos;{0}&apos; type can be used. Got variable of type &apos;{1}&apos;  .
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.MatrixArgument_ValidateEnteredMatrixSizeIncorrect">
            <summary>
              Looks up a localized string similar to Entered matrix size is incorrect.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_EmptyValueException">
            <summary>
              Looks up a localized string similar to Value cannot be empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_EqualToMaxException">
            <summary>
              Looks up a localized string similar to Invalid value, the value must be lower than {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_EqualToMinException">
            <summary>
              Looks up a localized string similar to Invalid value, the value must be larger than {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_HigherThanMaxException">
            <summary>
              Looks up a localized string similar to Invalid value, the maximum allowed value is {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_IncrementValueException">
            <summary>
              Looks up a localized string similar to Invalid value, the value should be an integer with increment {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_IntegerArgumentInputException">
            <summary>
              Looks up a localized string similar to The argument &apos;{0}&apos; only support integers numbers, the value is &apos;{1}&apos;..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_LowerThanMinException">
            <summary>
              Looks up a localized string similar to Invalid value, the minimum allowed value is {0}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_NumberNotAnIntegerMessage">
            <summary>
              Looks up a localized string similar to Number must be an integer..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_NumberNotEvenMessage">
            <summary>
              Looks up a localized string similar to Number must be even..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_NumberNotOddMessage">
            <summary>
              Looks up a localized string similar to Number must be odd..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_NumberTooLargeMessage">
            <summary>
              Looks up a localized string similar to Number cannot be larger than {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_NumberTooSmallMessage">
            <summary>
              Looks up a localized string similar to Number cannot be smaller than {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.NumberArgument_ParseFailedMessage">
            <summary>
              Looks up a localized string similar to Could not parse input value, a number argument needs to be on the form: &apos;2,3,4...&apos;.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ParameterUtils_ParseDoubleString">
            <summary>
              Looks up a localized string similar to Could not parse {0} to number..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ParameterUtils_ParseErrorContainsInvalidCharacters">
            <summary>
              Looks up a localized string similar to The input contains invalid characters, the input needs to be on the form {0}, ....
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ParameterUtils_ParseErrorMissingParantheses">
            <summary>
              Looks up a localized string similar to Missing parentheses.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.PointArgumentBase_ParseErrorInvalidIntputForm">
            <summary>
              Looks up a localized string similar to Could not parse input value. The input needs to be on the form {0}, ....
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.PointArgumentBase_ParseErrorListNotAllowed">
            <summary>
              Looks up a localized string similar to Only one point can be entered for {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.PointArgumentBase_SinglePointErrorMessage">
            <summary>
              Looks up a localized string similar to Only one point can be entered for {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_CouldNotCreateVariable">
            <summary>
              Looks up a localized string similar to Could not create variable.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_CouldNotFindFileException">
            <summary>
              Looks up a localized string similar to Could not find file.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_CouldNotLoadEnvironmentFileException">
            <summary>
              Looks up a localized string similar to Could not load environment file..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_InvalidVariableFileFormat">
            <summary>
              Looks up a localized string similar to is an invalid variable file format..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_InvalidVariableName">
            <summary>
              Looks up a localized string similar to is an invalid variable name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_UnsupportedImageFormatException">
            <summary>
              Looks up a localized string similar to Image could not be loaded. Unsupported image format..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ProcessingEnvironment_UnsupportedVariableFormatException">
            <summary>
              Looks up a localized string similar to Variable could not be loaded. Unsupported variable format..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.RunStepProgram_InvalidProgramException">
            <summary>
              Looks up a localized string similar to Invalid program. Run step program cannot run the same program it is part of..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.RunStepProgram_NoProgramFoundException">
            <summary>
              Looks up a localized string similar to There is no program with the given program name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.RunStepProgram_Program_Description">
            <summary>
              Looks up a localized string similar to The step program to run..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.RunStepProgram_Program_DisplayName">
            <summary>
              Looks up a localized string similar to Program.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_ArgumentErrorException">
            <summary>
              Looks up a localized string similar to {0}: Invalid input for {1}: &quot;{2}&quot; could not be interpreted as a variable name or parsed as a literal..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_InitializeAllResults_SameNameException">
            <summary>
              Looks up a localized string similar to {0}: All resulting variables for a step need to have unique names, the name &apos;{1}&apos; is used for multiple results..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_InvalidTypeException">
            <summary>
              Looks up a localized string similar to {0}: Invalid variable type: The variable &apos;{1}&apos; of type {2} is used as input to the argument &apos;{3}&apos;..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_RequiredResultException">
            <summary>
              Looks up a localized string similar to {0}: No input value for required result {1}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_StepArgumentIsEmptyException">
            <summary>
              Looks up a localized string similar to {0}: No input value for required argument {1}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.Step_StepInaccessibleText">
            <summary>
              Looks up a localized string similar to License required.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepBase_InvalidVariableLengthException">
            <summary>
              Looks up a localized string similar to The variable &apos;{0}&apos; contains more than 1 element. The argument &apos;{1}&apos; only accepts a variable of length 1..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepParameterBase_InvalidParameterName">
            <summary>
              Looks up a localized string similar to {0} is not a valid variable name..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepProgram_FinishStepProgramMessage">
            <summary>
              Looks up a localized string similar to Step program finished..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepProgram_ProgramNameEmptyException">
            <summary>
              Looks up a localized string similar to Step program name cannot be empty..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepProgram_RunStepMessage">
            <summary>
              Looks up a localized string similar to Running step: {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StepProgram_RunStepProgramMessage">
            <summary>
              Looks up a localized string similar to Run step program: {0}..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.StringArgument_FileDoesNotExist">
            <summary>
              Looks up a localized string similar to File does not exist..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.ToolBase_NumberArgumentException">
            <summary>
              Looks up a localized string similar to Argument &apos;{0}&apos; is invalid: {1}.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.VariableListBase_DisplayValueEmpty">
            <summary>
              Looks up a localized string similar to Empty.
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.VariableListBase_ToolTipEmpty">
            <summary>
              Looks up a localized string similar to The variable does not contain any elements..
            </summary>
        </member>
        <member name="P:Sick.Stream.Processing.Properties.Resources.VariableListBase_ToolTipIndex">
            <summary>
              Looks up a localized string similar to Index {0}: .
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.EnvironmentSerializer">
             <summary>
             Handles serialization and deserialization of the process environment.
             The serializing environment is stored as an envi-file. It contains the following:
             * A file containing the version number.
             * A file containing the step program, structured in an xml.
             * A file for each variable, serialized as strings.
            
             When serializing an individual variable it is stored as a vari-file.
             It contains the following:
             * A file containing the version number.
             * A file containing the serialized string of the variable.
             </summary>
        </member>
        <member name="M:Sick.Stream.Processing.EnvironmentSerializer.SerializeEnvironment(System.String,Sick.Stream.Processing.ProcessingEnvironment,System.Boolean)">
            <summary>
            Serialize the input environment to the specified file path.
            </summary>
            <param name="filePath">The path to the new envi-file.</param>
            <param name="env">The environment to be serialized.</param>
            <param name="excludeLargeVariableTypes">If true, Image and PointCloud variables will not be serialized.</param>
        </member>
        <member name="M:Sick.Stream.Processing.EnvironmentSerializer.DeserializeEnvironment(System.String,Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Deserialize an envi-file stored on disk, to a process environment.
            </summary>
            <param name="filePath">Path to the envi-file.</param>
            <param name="env">The process environment.</param>
        </member>
        <member name="T:Sick.Stream.Processing.VariableLoadResult">
            <summary>
            Stores the name and type of a variable.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.EnvironmentLoadResult">
            <summary>
            Contains the result from the deserialization of the environment.
            It contains all steps and variables that were incorrectly deserialized.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.EnvironmentLoadResult.GetLoadErrorMessage">
            <summary>
            Returns a string listing all the errors.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.StepProgramSerializer">
            <summary>
            Class for handling serialization and deserialization of step programs.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.SerializeStepProgram(System.IO.Compression.ZipArchive,Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Serializes the step programs to xml and writes it to a zip archive.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.DeserializeStepProgram(System.IO.Compression.ZipArchive,Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Reads an step program xml from an zip archive and deserialize it
            to the process environment.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.WriteEnvironmentXml(Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Serialize all step programs in the environment to an xml file.
            </summary>
            <param name="env">Input process environment.</param>
            <returns>xml string containing the step program structure.</returns>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.ReadEnvironmentFromXml(Sick.Stream.Processing.StepProgramSerializer.ProgramRootXml,Sick.Stream.Processing.ProcessingEnvironment)">
            <summary>
            Deserialize an EnvironmentXml and
            creates and adds the step program to the input environment.
            </summary>
            <param name="envXml">The EnvironmentXml to be deserialized.</param>
            <param name="env">The process environment.</param>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.WriteArgumentXml``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{Sick.Stream.Processing.StepProgramSerializer.ArgumentXml})">
            <summary>
            Writes a list of arguments to a list of xml arguments.
            </summary>
        </member>
        <member name="M:Sick.Stream.Processing.StepProgramSerializer.ReadStepParameterXml``1(System.Collections.Generic.List{``0},System.Collections.Generic.List{Sick.Stream.Processing.StepProgramSerializer.ArgumentXml})">
            <summary>
            Reads a list of xml arguments to a list of arguments.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.Serialization.UnknownTool">
            <summary>
            Dummy step added to the step program by the deserialization
            if the file contains steps that can't be loaded.
            </summary>
        </member>
        <member name="T:Sick.Stream.Processing.VariableLoader">
            <summary>
            Static class for handling loading of variables from file.
            </summary>
        </member>
    </members>
</doc>
