﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>Sick.Stream.Processing</id>
    <version>********</version>
    <authors>SICK</authors>
    <description>Stream Editor processing environment</description>
    <copyright>Copyright © SICK AG 2025</copyright>
    <dependencies>
      <group targetFramework=".NETStandard2.0">
        <dependency id="Sick.GenIStreamDotNet" version="1.0.0" exclude="Build,Analyzers" />
        <dependency id="Sick.Stream.Algorithms.DotNet" version="********" exclude="Build,Analyzers" />
        <dependency id="Sick.Stream.Common" version="********" exclude="Build,Analyzers" />
        <dependency id="DynamicData" version="9.2.1" exclude="Build,Analyzers" />
        <dependency id="NLog" version="5.4.0" exclude="Build,Analyzers" />
        <dependency id="Splat" version="15.3.1" exclude="Build,Analyzers" />
        <dependency id="System.Reactive" version="6.0.1" exclude="Build,Analyzers" />
        <dependency id="System.Runtime.CompilerServices.Unsafe" version="6.1.1" exclude="Build,Analyzers" />
        <dependency id="System.Threading.Tasks.Extensions" version="4.6.2" exclude="Build,Analyzers" />
        <dependency id="org.matheval" version="1.0.0.3" exclude="Build,Analyzers" />
      </group>
    </dependencies>
  </metadata>
</package>