<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Splat</name>
    </assembly>
    <members>
        <member name="T:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingManager">
            <summary>
            Default implementation of the Feature Usage Tracking Manager.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingManager.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingManager"/> class.
            </summary>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession">
            <summary>
            Default Feature Usage Tracking Session. Used for output when a dev chooses not to override.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession"/> class.
            </summary>
            <param name="featureName">The name of the feature.</param>
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.ParentReference">
            <inheritdoc />
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.FeatureReference">
            <inheritdoc />
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.FeatureName">
            <inheritdoc />
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.SubFeature(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.OnException(System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.DefaultFeatureUsageTrackingSession.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.EnableFeatureUsageTrackingExtensions">
            <summary>
            Extensions for the IEnableFeatureUsageTracking interface. This is a similar design to IEnableLogger, to allow
            easy use and extension of classes such as ViewModels.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.EnableFeatureUsageTrackingExtensions.FeatureUsageTrackingSession(Splat.ApplicationPerformanceMonitoring.IEnableFeatureUsageTracking,System.String)">
            <summary>
            Gets a Feature Usage Tracking Sessions.
            </summary>
            <param name="instance">instance of class that uses IEnableFeatureUsageTracking.</param>
            <param name="featureName">Name of the feature.</param>
            <returns>Feature Usage Tracking Session.</returns>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.EnableFeatureUsageTrackingExtensions.WithFeatureUsageTrackingSession(Splat.ApplicationPerformanceMonitoring.IEnableFeatureUsageTracking,System.String,System.Action{Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession})">
            <summary>
            Helper for wrapping an action with a Feature Usage Tracking Session.
            </summary>
            <param name="instance">instance of class that uses IEnableFeatureUsageTracking.</param>
            <param name="featureName">Name of the feature.</param>
            <param name="action">Action to carry out.</param>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.EnableFeatureUsageTrackingExtensions.WithSubFeatureUsageTrackingSession(Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession,System.String,System.Action{Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession})">
            <summary>
            Helper for wrapping an action with a SubFeature Usage Tracking Session.
            </summary>
            <param name="instance">instance of class that uses IEnableFeatureUsageTracking.</param>
            <param name="featureName">Name of the feature.</param>
            <param name="action">Action to carry out.</param>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.FuncFeatureUsageTrackingManager">
            <summary>
            Func based Feature Usage Tracking Manager.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.ApplicationPerformanceMonitoring.FuncFeatureUsageTrackingManager"/> class.
            </remarks>
            <param name="featureUsageTrackingSessionFunc">
            Factory function for a Feature Usage Tracking Session.
            </param>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.FuncFeatureUsageTrackingManager.#ctor(System.Func{System.String,Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession})">
            <summary>
            Func based Feature Usage Tracking Manager.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.ApplicationPerformanceMonitoring.FuncFeatureUsageTrackingManager"/> class.
            </remarks>
            <param name="featureUsageTrackingSessionFunc">
            Factory function for a Feature Usage Tracking Session.
            </param>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.FuncFeatureUsageTrackingManager.GetFeatureUsageTrackingSession(System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.IEnableFeatureUsageTracking">
            <summary>
            Interface for exposing Feature Usage Tracking as an extension to a class.
            </summary>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingManager">
            <summary>
            A manager which will generate a <see cref="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession"/> for the specified type.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingManager.GetFeatureUsageTrackingSession(System.String)">
            <summary>
            Generate a <see cref="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession"/> for the specified type.
            </summary>
            <param name="featureName">The name of the feature.</param>
            <returns>The <see cref="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession"/>.</returns>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession">
            <summary>
            Base interface for a feature usage tracking session.
            </summary>
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession.FeatureName">
            <summary>
            Gets the name of the Feature being tracked.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession.SubFeature(System.String)">
            <summary>
            Starts a sub-feature usage tracking session on the current session.
            </summary>
            <param name="description">Description of the sub-feature.</param>
            <returns>The sub-feature usage tracking session.</returns>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession.OnException(System.Exception)">
            <summary>
            Notify the APM toolset an exception has occured in the current tracking session.
            </summary>
            <param name="exception">The exception that occurred.</param>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession`1">
            <summary>
            Generic interface for a Feature Usage Tracking Session.
            </summary>
            <typeparam name="TReferenceType">The Type for the Unique Tracking References.</typeparam>
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession`1.FeatureReference">
            <summary>
            Gets the current Feature Usage Unique Reference.
            </summary>
        </member>
        <member name="P:Splat.ApplicationPerformanceMonitoring.IFeatureUsageTrackingSession`1.ParentReference">
            <summary>
            Gets the unique reference for the Parent Tracking Session, if any.
            </summary>
        </member>
        <member name="T:Splat.ApplicationPerformanceMonitoring.IViewTracking">
            <summary>
            Base interface for tracking view usage.
            </summary>
        </member>
        <member name="M:Splat.ApplicationPerformanceMonitoring.IViewTracking.OnViewNavigation(System.String)">
            <summary>
            Track a view navigation using just a name.
            </summary>
            <param name="name">Name of the view.</param>
        </member>
        <member name="M:Splat.AssemblyFinder.AttemptToLoadType``1(System.String)">
            <summary>
            Attempt to find the type based on the specified string.
            </summary>
            <typeparam name="T">The type to cast the value to if we find it.</typeparam>
            <param name="fullTypeName">The name of the full type.</param>
            <returns>The created object or the default value.</returns>
        </member>
        <member name="T:Splat.ActionDisposable">
            <summary>
            A disposable which will call the specified action.
            </summary>
        </member>
        <member name="M:Splat.ActionDisposable.#ctor(System.Action)">
            <summary>
            A disposable which will call the specified action.
            </summary>
        </member>
        <member name="P:Splat.ActionDisposable.Empty">
            <summary>
            Gets a action disposable which does nothing.
            </summary>
        </member>
        <member name="M:Splat.ActionDisposable.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Splat.BooleanDisposable">
            <summary>
            Represents a disposable resource that can be checked for disposal status.
            Based on the System.Reactive.Disposable class.
            </summary>
        </member>
        <member name="M:Splat.BooleanDisposable.#ctor">
            <summary>
            Represents a disposable resource that can be checked for disposal status.
            Based on the System.Reactive.Disposable class.
            </summary>
        </member>
        <member name="P:Splat.BooleanDisposable.IsDisposed">
            <summary>
            Gets a value indicating whether the object is disposed.
            </summary>
        </member>
        <member name="M:Splat.BooleanDisposable.Dispose">
            <summary>
            Sets the status to disposed, which can be observer through the <see cref="P:Splat.BooleanDisposable.IsDisposed"/> property.
            </summary>
        </member>
        <member name="T:Splat.CompositeDisposable">
            <summary>
            Represents a group of disposable resources that are disposed together.
            This is a simplified version based on System.Reactive's one.
            </summary>
        </member>
        <member name="M:Splat.CompositeDisposable.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.CompositeDisposable"/> class with no disposables contained by it initially.
            </summary>
        </member>
        <member name="M:Splat.CompositeDisposable.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.CompositeDisposable"/> class with the specified number of disposables.
            </summary>
            <param name="capacity">The number of disposables that the new CompositeDisposable can initially store.</param>
            <exception cref="T:System.ArgumentOutOfRangeException"><paramref name="capacity"/> is less than zero.</exception>
        </member>
        <member name="M:Splat.CompositeDisposable.#ctor(System.IDisposable[])">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.CompositeDisposable"/> class from a group of disposables.
            </summary>
            <param name="disposables">Disposables that will be disposed together.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="disposables"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException">Any of the disposables in the <paramref name="disposables"/> collection is <c>null</c>.</exception>
        </member>
        <member name="M:Splat.CompositeDisposable.#ctor(System.Collections.Generic.IEnumerable{System.IDisposable})">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.CompositeDisposable"/> class from a group of disposables.
            </summary>
            <param name="disposables">Disposables that will be disposed together.</param>
            <exception cref="T:System.ArgumentNullException"><paramref name="disposables"/> is <c>null</c>.</exception>
            <exception cref="T:System.ArgumentException">Any of the disposables in the <paramref name="disposables"/> collection is <c>null</c>.</exception>
        </member>
        <member name="M:Splat.CompositeDisposable.Dispose">
            <summary>
            Disposes all disposables in the group and removes them from the group.
            </summary>
        </member>
        <member name="M:Splat.CompositeDisposable.Init(System.Collections.Generic.IEnumerable{System.IDisposable})">
            <summary>
            Initialize the inner disposable list and count fields.
            </summary>
            <param name="disposables">The enumerable sequence of disposables.</param>
        </member>
        <member name="M:Splat.ExceptionMixins.ThrowArgumentNullExceptionIfNull``1(``0,System.String)">
            <summary>
            Throws the argument null exception if null.
            </summary>
            <typeparam name="T">Source Type.</typeparam>
            <param name="value">The value.</param>
            <param name="name">The name.</param>
        </member>
        <member name="M:Splat.ExceptionMixins.ThrowArgumentNullExceptionIfNull``1(``0,System.String,System.String)">
            <summary>
            Throws the argument null exception if null.
            </summary>
            <typeparam name="T">Source Type.</typeparam>
            <param name="value">The value.</param>
            <param name="name">The name.</param>
            <param name="message">The message.</param>
        </member>
        <member name="T:Splat.ActionLogger">
            <summary>
            A logger where you pass in Action delegates that will be invoked when the Write methods are invoked.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.ActionLogger"/> class.
            </remarks>
            <param name="writeNoType">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.String,Splat.LogLevel)"/> is called.</param>
            <param name="writeWithType">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.String,System.Type,Splat.LogLevel)"/> is called.</param>
            <param name="writeNoTypeWithException">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.Exception,System.String,Splat.LogLevel)"/> is called.</param>
            <param name="writeWithTypeAndException">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)"/> is called.</param>
        </member>
        <member name="M:Splat.ActionLogger.#ctor(System.Action{System.String,Splat.LogLevel},System.Action{System.String,System.Type,Splat.LogLevel},System.Action{System.Exception,System.String,Splat.LogLevel},System.Action{System.Exception,System.String,System.Type,Splat.LogLevel})">
            <summary>
            A logger where you pass in Action delegates that will be invoked when the Write methods are invoked.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.ActionLogger"/> class.
            </remarks>
            <param name="writeNoType">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.String,Splat.LogLevel)"/> is called.</param>
            <param name="writeWithType">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.String,System.Type,Splat.LogLevel)"/> is called.</param>
            <param name="writeNoTypeWithException">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.Exception,System.String,Splat.LogLevel)"/> is called.</param>
            <param name="writeWithTypeAndException">A action which is called when the <see cref="M:Splat.ActionLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)"/> is called.</param>
        </member>
        <member name="P:Splat.ActionLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.ActionLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ActionLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ActionLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ActionLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.AllocationFreeLoggerBase">
            <summary>
            Base class for a logger the provides allocation free logging.
            </summary>
            <seealso cref="T:Splat.IAllocationFreeLogger" />
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.AllocationFreeLoggerBase"/> class.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger" /> to wrap in this class.</param>
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.#ctor(Splat.ILogger)">
            <summary>
            Base class for a logger the provides allocation free logging.
            </summary>
            <seealso cref="T:Splat.IAllocationFreeLogger" />
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.AllocationFreeLoggerBase"/> class.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger" /> to wrap in this class.</param>
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.Level">
            <inheritdoc />
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.IsDebugEnabled">
            <inheritdoc />
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.IsInfoEnabled">
            <inheritdoc />
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.IsWarnEnabled">
            <inheritdoc />
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.IsErrorEnabled">
            <inheritdoc />
        </member>
        <member name="P:Splat.AllocationFreeLoggerBase.IsFatalEnabled">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``1(System.Exception,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``2(System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``2(System.Exception,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``3(System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``3(System.Exception,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``4(System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``4(System.Exception,System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``5(System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``6(System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Debug``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``1(System.Exception,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``2(System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``2(System.Exception,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``3(System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``3(System.Exception,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``4(System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``4(System.Exception,System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``5(System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``6(System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Info``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``1(System.Exception,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``2(System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``2(System.Exception,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``3(System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``3(System.Exception,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``4(System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``4(System.Exception,System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``5(System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``6(System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Warn``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``1(System.Exception,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``2(System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``2(System.Exception,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``3(System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``3(System.Exception,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``4(System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``4(System.Exception,System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``5(System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``6(System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Error``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``1(System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``1(System.Exception,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``2(System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``2(System.Exception,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``3(System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``3(System.Exception,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``4(System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``4(System.Exception,System.String,``0,``1,``2,``3)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``5(System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``6(System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Fatal``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.AllocationFreeLoggerBase.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.ConsoleLogger">
            <summary>
            A logger which will send messages to the console.
            </summary>
        </member>
        <member name="P:Splat.ConsoleLogger.ExceptionMessageFormat">
            <summary>
            Gets or sets the exception message format.
            First parameter will be the message, second will be the exception.
            </summary>
        </member>
        <member name="P:Splat.ConsoleLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.ConsoleLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ConsoleLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ConsoleLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ConsoleLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.DebugLogger">
            <summary>
            A logger which will send messages to the debug logger.
            </summary>
        </member>
        <member name="P:Splat.DebugLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.DebugLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.DebugLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.DebugLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.DebugLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.DefaultLogManager">
            <summary>
            The default log manager provided by splat.
            This log manager will cache the loggers for each type,
            This will use the default registered <see cref="T:Splat.ILogger"/> inside the <see cref="T:Splat.Locator"/>.
            </summary>
        </member>
        <member name="M:Splat.DefaultLogManager.#ctor(Splat.IReadonlyDependencyResolver)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.DefaultLogManager"/> class.
            </summary>
            <param name="dependencyResolver">A dependency resolver for testing purposes, will use the default Locator if null.</param>
        </member>
        <member name="M:Splat.DefaultLogManager.GetLogger(System.Type)">
            <inheritdoc />
        </member>
        <member name="T:Splat.FullLoggerExtensions">
            <summary>
            Provides extension methods to the <see cref="T:Splat.IFullLogger"/> interface.
            </summary>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Debug(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Debug is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Debug logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Debug``1(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Debug is enabled.
            </summary>
            <typeparam name="T">The type of object we are logging about.</typeparam>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Debug logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.DebugException(Splat.IFullLogger,System.Func{System.String},System.Exception)">
            <summary>
            Sends the value provided by the provided delegate, only if Debug is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Debug logging is enabled.</param>
            <param name="exception">A exception to log about.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Info(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Debug is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Debug logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Info``1(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Debug is enabled.
            </summary>
            <typeparam name="T">The type of object we are logging about.</typeparam>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Debug logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.InfoException(Splat.IFullLogger,System.Func{System.String},System.Exception)">
            <summary>
            Sends the value provided by the provided delegate, only if Info is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Info logging is enabled.</param>
            <param name="exception">A exception to log about.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Warn(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Warn is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Warn logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Warn``1(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Warn is enabled.
            </summary>
            <typeparam name="T">The type of object we are logging about.</typeparam>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Warn logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.WarnException(Splat.IFullLogger,System.Func{System.String},System.Exception)">
            <summary>
            Sends the value provided by the provided delegate, only if Warn is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Warn logging is enabled.</param>
            <param name="exception">A exception to log about.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Error(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Error is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Error logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Error``1(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Error is enabled.
            </summary>
            <typeparam name="T">The type of object we are logging about.</typeparam>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Error logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.ErrorException(Splat.IFullLogger,System.Func{System.String},System.Exception)">
            <summary>
            Sends the value provided by the provided delegate, only if Error is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Error logging is enabled.</param>
            <param name="exception">A exception to log about.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Fatal(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Fatal is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Fatal logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.Fatal``1(Splat.IFullLogger,System.Func{System.String})">
            <summary>
            Sends the value provided by the provided delegate, only if Fatal is enabled.
            </summary>
            <typeparam name="T">The type of object we are logging about.</typeparam>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Fatal logging is enabled.</param>
        </member>
        <member name="M:Splat.FullLoggerExtensions.FatalException(Splat.IFullLogger,System.Func{System.String},System.Exception)">
            <summary>
            Sends the value provided by the provided delegate, only if Fatal is enabled.
            </summary>
            <param name="logger">The logger to use.</param>
            <param name="function">The function to evaluate if Fatal logging is enabled.</param>
            <param name="exception">A exception to log about.</param>
        </member>
        <member name="T:Splat.FuncLogManager">
            <summary>
            A log manager which will generate the <see cref="T:Splat.IFullLogger"/> by using the specified Func.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.FuncLogManager"/> class.
            </remarks>
            <param name="getLoggerFunc">The function which will be used to generate the <see cref="T:Splat.IFullLogger"/>.</param>
        </member>
        <member name="M:Splat.FuncLogManager.#ctor(System.Func{System.Type,Splat.IFullLogger})">
            <summary>
            A log manager which will generate the <see cref="T:Splat.IFullLogger"/> by using the specified Func.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.FuncLogManager"/> class.
            </remarks>
            <param name="getLoggerFunc">The function which will be used to generate the <see cref="T:Splat.IFullLogger"/>.</param>
        </member>
        <member name="M:Splat.FuncLogManager.GetLogger(System.Type)">
            <inheritdoc />
        </member>
        <member name="T:Splat.IAllocationFreeErrorLogger">
            <summary>
            An allocation free exception logger which wraps all the possible logging methods available.
            Often not needed for your own loggers.
            A <see cref="T:Splat.WrappingFullLogger"/> will wrap simple loggers into a full logger.
            </summary>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``1(System.Exception,System.String,``0)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``2(System.Exception,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``4(System.Exception,System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Debug``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``1(System.Exception,System.String,``0)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``2(System.Exception,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``4(System.Exception,System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <summary>
            Logs a info message with the provided message format and values.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Logs a info message with the provided message format and values.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Info``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``1(System.Exception,System.String,``0)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``2(System.Exception,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``4(System.Exception,System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Warn``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``1(System.Exception,System.String,``0)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``2(System.Exception,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``4(System.Exception,System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Error``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``1(System.Exception,System.String,``0)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``2(System.Exception,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``3(System.Exception,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``4(System.Exception,System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``5(System.Exception,System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``6(System.Exception,System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``7(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``8(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``9(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeErrorLogger.Fatal``10(System.Exception,System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="exception">The exception.</param>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="T:Splat.IAllocationFreeLogger">
            <summary>
            An allocation free logger which wraps all the possible logging methods available.
            Often not needed for your own loggers.
            A <see cref="T:Splat.WrappingFullLogger"/> will wrap simple loggers into a full logger.
            </summary>
        </member>
        <member name="P:Splat.IAllocationFreeLogger.IsDebugEnabled">
            <summary>
            Gets a value indicating whether the logger currently emits debug logs.
            </summary>
        </member>
        <member name="P:Splat.IAllocationFreeLogger.IsInfoEnabled">
            <summary>
            Gets a value indicating whether the logger currently emits information logs.
            </summary>
        </member>
        <member name="P:Splat.IAllocationFreeLogger.IsWarnEnabled">
            <summary>
            Gets a value indicating whether the logger currently emits warning logs.
            </summary>
        </member>
        <member name="P:Splat.IAllocationFreeLogger.IsErrorEnabled">
            <summary>
            Gets a value indicating whether the logger currently emits error logs.
            </summary>
        </member>
        <member name="P:Splat.IAllocationFreeLogger.IsFatalEnabled">
            <summary>
            Gets a value indicating whether the logger currently emits fatal logs.
            </summary>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``1(System.String,``0)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``2(System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``3(System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``4(System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``5(System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``6(System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Debug``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``1(System.String,``0)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``2(System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``3(System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``4(System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``5(System.String,``0,``1,``2,``3,``4)">
            <summary>
            Logs a info message with the provided message format and values.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``6(System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Logs a info message with the provided message format and values.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Info``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``1(System.String,``0)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``2(System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``3(System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``4(System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``5(System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``6(System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Warn``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the warn log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``1(System.String,``0)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``2(System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``3(System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``4(System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``5(System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``6(System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Error``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``1(System.String,``0)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``2(System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``3(System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``4(System.String,``0,``1,``2,``3)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``5(System.String,``0,``1,``2,``3,``4)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``6(System.String,``0,``1,``2,``3,``4,``5)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``7(System.String,``0,``1,``2,``3,``4,``5,``6)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``8(System.String,``0,``1,``2,``3,``4,``5,``6,``7)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``9(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IAllocationFreeLogger.Fatal``10(System.String,``0,``1,``2,``3,``4,``5,``6,``7,``8,``9)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument4">The type of the fourth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument5">The type of the fifth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument6">The type of the sixth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument7">The type of the seventh argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument8">The type of the eighth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument9">The type of the ninth argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument10">The type of the tenth argument which is used in the formatting.</typeparam>
            <param name="messageFormat">The message format used to emit a message with the type arguments.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="argument4">The fourth argument for formatting purposes.</param>
            <param name="argument5">The fifth argument for formatting purposes.</param>
            <param name="argument6">The sixth argument for formatting purposes.</param>
            <param name="argument7">The seventh argument for formatting purposes.</param>
            <param name="argument8">The eighth argument for formatting purposes.</param>
            <param name="argument9">The ninth argument for formatting purposes.</param>
            <param name="argument10">The tenth argument for formatting purposes.</param>
        </member>
        <member name="T:Splat.IEnableLogger">
            <summary>
            "Implement" this interface in your class to get access to the Log()
            Mixin, which will give you a Logger that includes the class name in the
            log.
            </summary>
        </member>
        <member name="T:Splat.IFullLogger">
            <summary>
            A full logger which wraps all the possible logging methods available.
            Often not needed for your own loggers.
            A <see cref="T:Splat.WrappingFullLogger"/> will wrap simple loggers into a full logger.
            </summary>
        </member>
        <member name="M:Splat.IFullLogger.Debug``1(``0)">
            <summary>
            Emits a debug log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="value">The log to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``1(System.IFormatProvider,``0)">
            <summary>
            Emits a debug log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="value">The value to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.DebugException(System.String,System.Exception)">
            <summary>
            Emits a debug log message.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="message">A message to emit.</param>
            <param name="exception">The exception which to emit in the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug(System.Exception,System.String)">
            <summary>
            Emits a debug log message with an exception.
            </summary>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug(System.String)">
            <summary>
            Emits a message to the debug log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``1(System.String)">
            <summary>
            Emits a message to the debug log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``1(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Debug``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``1(``0)">
            <summary>
            Emits a info log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="value">The log to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``1(System.IFormatProvider,``0)">
            <summary>
            Emits a info log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="value">The value to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.InfoException(System.String,System.Exception)">
            <summary>
            Emits a info log message.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="message">A message to emit.</param>
            <param name="exception">The exception which to emit in the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info(System.Exception,System.String)">
            <summary>
            Emits a info log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info(System.String)">
            <summary>
            Emits a message to the info log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``1(System.String)">
            <summary>
            Emits a message to the info log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``1(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Info``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``1(``0)">
            <summary>
            Emits a warning log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="value">The log to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``1(System.IFormatProvider,``0)">
            <summary>
            Emits a warning log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="value">The value to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.WarnException(System.String,System.Exception)">
            <summary>
            Emits a warning log message.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="message">A message to emit.</param>
            <param name="exception">The exception which to emit in the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn(System.Exception,System.String)">
            <summary>
            Emits a warning log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn(System.String)">
            <summary>
            Emits a message to the warning log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``1(System.String)">
            <summary>
            Emits a message to the warning log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``1(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Warn``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``1(``0)">
            <summary>
            Emits a error log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="value">The log to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``1(System.IFormatProvider,``0)">
            <summary>
            Emits a error log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="value">The value to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.ErrorException(System.String,System.Exception)">
            <summary>
            Emits a error log message.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="message">A message to emit.</param>
            <param name="exception">The exception which to emit in the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error(System.Exception,System.String)">
            <summary>
            Emits a error log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error(System.String)">
            <summary>
            Emits a message to the error log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``1(System.String)">
            <summary>
            Emits a message to the error log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``1(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Error``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``1(``0)">
            <summary>
            Emits a fatal log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="value">The log to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``1(System.IFormatProvider,``0)">
            <summary>
            Emits a fatal log message.
            This will emit the public contents of the object provided to the log.
            </summary>
            <typeparam name="T">The type of object used as the message.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="value">The value to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.FatalException(System.String,System.Exception)">
            <summary>
            Emits a fatal log message.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="message">A message to emit.</param>
            <param name="exception">The exception which to emit in the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal(System.Exception,System.String)">
            <summary>
            Emits a fatal log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal(System.IFormatProvider,System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal(System.String)">
            <summary>
            Emits a message to the fatal log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``1(System.String)">
            <summary>
            Emits a message to the fatal log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``1(System.String,System.Object[])">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to emit to the log which includes the standard formatting tags.</param>
            <param name="args">The arguments for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``1(System.IFormatProvider,System.String,``0)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``2(System.IFormatProvider,System.String,``0,``1)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
        </member>
        <member name="M:Splat.IFullLogger.Fatal``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
        </member>
        <member name="T:Splat.ILogger">
            <summary>
            Represents a log target where messages can be written to.
            </summary>
        </member>
        <member name="P:Splat.ILogger.Level">
            <summary>
            Gets the level at which the target will emit messages.
            </summary>
        </member>
        <member name="M:Splat.ILogger.Write(System.String,Splat.LogLevel)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="message">The message to write.</param>
            <param name="logLevel">The severity level of the log message.</param>
        </member>
        <member name="M:Splat.ILogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="exception">The exception that occured.</param>
            <param name="message">The message to write.</param>
            <param name="logLevel">The severity level of the log message.</param>
        </member>
        <member name="M:Splat.ILogger.Write(System.String,System.Type,Splat.LogLevel)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="logLevel">The log level.</param>
        </member>
        <member name="M:Splat.ILogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="exception">The exception that occured.</param>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="logLevel">The log level.</param>
        </member>
        <member name="T:Splat.ILogManager">
            <summary>
            A manaager which will generate a <see cref="T:Splat.IFullLogger"/> for the specified type.
            </summary>
        </member>
        <member name="M:Splat.ILogManager.GetLogger(System.Type)">
            <summary>
            Generate a <see cref="T:Splat.IFullLogger"/> for the specified type.
            </summary>
            <param name="type">The type to generate the logger for.</param>
            <returns>The <see cref="T:Splat.IFullLogger"/> for the specified type.</returns>
        </member>
        <member name="T:Splat.IStaticFullLogger">
            <summary>
            Represents the logging interface for the Static Default Logger.
            </summary>
        </member>
        <member name="P:Splat.IStaticFullLogger.Level">
            <summary>
            Gets the level at which the target will emit messages.
            </summary>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug(System.Exception,System.String,System.String)">
            <summary>
            Emits a debug log message with an exception.
            </summary>
            <param name="exception">The exception.</param>
            <param name="message">The message.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug(System.String,System.String)">
            <summary>
            Emits a message to the debug log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug``1(System.String,System.String)">
            <summary>
            Emits a message to the debug log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug``1(System.IFormatProvider,System.String,``0,System.String)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Debug``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <summary>
            Emits a message using formatting to the debug log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info(System.Exception,System.String,System.String)">
            <summary>
            Emits a info log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info(System.String,System.String)">
            <summary>
            Emits a message to the info log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info``1(System.String,System.String)">
            <summary>
            Emits a message to the info log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info``1(System.IFormatProvider,System.String,``0,System.String)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Info``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <summary>
            Emits a message using formatting to the info log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn(System.Exception,System.String,System.String)">
            <summary>
            Emits a warning log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn(System.String,System.String)">
            <summary>
            Emits a message to the warning log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn``1(System.String,System.String)">
            <summary>
            Emits a message to the warning log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn``1(System.IFormatProvider,System.String,``0,System.String)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Warn``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <summary>
            Emits a message using formatting to the warning log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error(System.Exception,System.String,System.String)">
            <summary>
            Emits a error log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error(System.String,System.String)">
            <summary>
            Emits a message to the error log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error``1(System.String,System.String)">
            <summary>
            Emits a message to the error log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error``1(System.IFormatProvider,System.String,``0,System.String)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Error``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <summary>
            Emits a message using formatting to the error log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal(System.Exception,System.String,System.String)">
            <summary>
            Emits a fatal log message with exception.
            This will emit details about a exception.
            This type of logging is not able to be localized.
            </summary>
            <param name="exception">The exception which to emit in the log.</param>
            <param name="message">A message to emit.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal(System.String,System.String)">
            <summary>
            Emits a message to the fatal log.
            </summary>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal``1(System.String,System.String)">
            <summary>
            Emits a message to the fatal log.
            </summary>
            <typeparam name="T">The calling type.</typeparam>
            <param name="message">A non-localizable message to send to the log.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal``1(System.IFormatProvider,System.String,``0,System.String)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument">The type of the argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument">The argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Fatal``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <summary>
            Emits a message using formatting to the fatal log.
            </summary>
            <typeparam name="TArgument1">The type of the first argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument2">The type of the second argument which is used in the formatting.</typeparam>
            <typeparam name="TArgument3">The type of the third argument which is used in the formatting.</typeparam>
            <param name="formatProvider">The format provider to use.</param>
            <param name="message">A message to emit to the log which includes the standard formatting tags.</param>
            <param name="argument1">The first argument for formatting purposes.</param>
            <param name="argument2">The second argument for formatting purposes.</param>
            <param name="argument3">The third argument for formatting purposes.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Write(System.String,Splat.LogLevel,System.String)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="message">The message to write.</param>
            <param name="logLevel">The severity level of the log message.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Write(System.Exception,System.String,Splat.LogLevel,System.String)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="exception">The exception that occured.</param>
            <param name="message">The message to write.</param>
            <param name="logLevel">The severity level of the log message.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Write(System.String,System.Type,Splat.LogLevel,System.String)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="logLevel">The log level.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="M:Splat.IStaticFullLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel,System.String)">
            <summary>
            Writes a message to the target.
            </summary>
            <param name="exception">The exception that occured.</param>
            <param name="message">The message.</param>
            <param name="type">The type.</param>
            <param name="logLevel">The log level.</param>
            <param name="callerMemberName">Allows you to pass the method or property name of the caller to the method, used to allow the capture in the static logger of some additional context for support and debugging.</param>
        </member>
        <member name="T:Splat.LoggingException">
            <summary>
            A exception that occurs when there is a problem using the logging module.
            </summary>
        </member>
        <member name="M:Splat.LoggingException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.LoggingException"/> class.
            </summary>
        </member>
        <member name="M:Splat.LoggingException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.LoggingException"/> class.
            </summary>
            <param name="message">The message about the exception.</param>
        </member>
        <member name="M:Splat.LoggingException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.LoggingException"/> class.
            </summary>
            <param name="message">The message about the exception.</param>
            <param name="innerException">Any other internal exceptions we are mapping.</param>
        </member>
        <member name="T:Splat.LogHost">
            <summary>
            Contains helper methods to get access to the Default <see cref="T:Splat.IFullLogger"/>.
            </summary>
        </member>
        <member name="P:Splat.LogHost.Default">
            <summary>
            Gets the default <see cref="T:Splat.IFullLogger"/> registered within the <see cref="T:Splat.Locator"/>.
            </summary>
        </member>
        <member name="M:Splat.LogHost.Log``1(``0)">
            <summary>
            Call this method to write log entries on behalf of the current class.
            </summary>
            <typeparam name="T">The type to get the <see cref="T:Splat.IFullLogger"/> for.</typeparam>
            <param name="logClassInstance">The class we are getting the logger for.</param>
            <returns>The <see cref="T:Splat.IFullLogger"/> for the class type.</returns>
        </member>
        <member name="T:Splat.LogLevel">
            <summary>
            Represents the minimum log level a <see cref="T:Splat.ILogger"/> will start emitting from.
            </summary>
        </member>
        <member name="F:Splat.LogLevel.Debug">
            <summary>
            The log message is for debuging purposes.
            </summary>
        </member>
        <member name="F:Splat.LogLevel.Info">
            <summary>
            The log message is for information purposes.
            </summary>
        </member>
        <member name="F:Splat.LogLevel.Warn">
            <summary>
            The log message is for warning purposes.
            </summary>
        </member>
        <member name="F:Splat.LogLevel.Error">
            <summary>
            The log message is for error purposes.
            </summary>
        </member>
        <member name="F:Splat.LogLevel.Fatal">
            <summary>
            The log message is for fatal purposes.
            </summary>
        </member>
        <member name="T:Splat.LogManagerMixin">
            <summary>
            Extension methods associated with the logging module.
            </summary>
        </member>
        <member name="M:Splat.LogManagerMixin.GetLogger``1(Splat.ILogManager)">
            <summary>
            Gets a <see cref="T:Splat.IFullLogger"/> for the specified <see cref="T:Splat.ILogManager"/>.
            </summary>
            <typeparam name="T">The type of <see cref="T:Splat.ILogManager"/> to use.</typeparam>
            <param name="logManager">The log manager to get the logger from.</param>
            <returns>A logger for the specified type.</returns>
        </member>
        <member name="T:Splat.NullLogger">
            <summary>
            a logger which will never emit any value.
            </summary>
        </member>
        <member name="P:Splat.NullLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.NullLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.NullLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.NullLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.NullLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.StaticFullLogger">
            <summary>
            A full logger which used by the default static logger to allow capture of .NET framework caller data. Wraps a <see cref="T:Splat.IFullLogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.StaticFullLogger"/> class.
            </remarks>
            <param name="fullLogger">The <see cref="T:Splat.IFullLogger"/> to wrap in this class.</param>
        </member>
        <member name="M:Splat.StaticFullLogger.#ctor(Splat.IFullLogger)">
            <summary>
            A full logger which used by the default static logger to allow capture of .NET framework caller data. Wraps a <see cref="T:Splat.IFullLogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.StaticFullLogger"/> class.
            </remarks>
            <param name="fullLogger">The <see cref="T:Splat.IFullLogger"/> to wrap in this class.</param>
        </member>
        <member name="P:Splat.StaticFullLogger.Level">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug(System.Exception,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug``1(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug``1(System.IFormatProvider,System.String,``0,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Debug``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info(System.Exception,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info``1(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info``1(System.IFormatProvider,System.String,``0,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Info``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn(System.Exception,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn``1(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn``1(System.IFormatProvider,System.String,``0,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Warn``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error(System.Exception,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error``1(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error``1(System.IFormatProvider,System.String,``0,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Error``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal(System.Exception,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal``1(System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal``1(System.IFormatProvider,System.String,``0,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal``2(System.IFormatProvider,System.String,``0,``1,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Fatal``3(System.IFormatProvider,System.String,``0,``1,``2,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Write(System.String,Splat.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Write(System.Exception,System.String,Splat.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Write(System.String,System.Type,Splat.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="M:Splat.StaticFullLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel,System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Splat.WrappingFullLogger">
            <summary>
            A full logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingFullLogger"/> class.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
        </member>
        <member name="M:Splat.WrappingFullLogger.#ctor(Splat.ILogger)">
            <summary>
            A full logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingFullLogger"/> class.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``1(System.IFormatProvider,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.DebugException(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug(System.Exception,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug(System.IFormatProvider,System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``1(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``1(System.IFormatProvider,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``2(System.IFormatProvider,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Debug``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``1(System.IFormatProvider,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.InfoException(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info(System.Exception,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info(System.IFormatProvider,System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``1(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``1(System.IFormatProvider,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``2(System.IFormatProvider,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Info``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``1(System.IFormatProvider,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.WarnException(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn(System.Exception,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn(System.IFormatProvider,System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``1(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``1(System.IFormatProvider,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``2(System.IFormatProvider,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Warn``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``1(System.IFormatProvider,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.ErrorException(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error(System.Exception,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error(System.IFormatProvider,System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``1(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``1(System.IFormatProvider,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``2(System.IFormatProvider,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Error``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``1(``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``1(System.IFormatProvider,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.FatalException(System.String,System.Exception)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal(System.Exception,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal(System.IFormatProvider,System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``1(System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``1(System.String,System.Object[])">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``1(System.IFormatProvider,System.String,``0)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``2(System.IFormatProvider,System.String,``0,``1)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingFullLogger.Fatal``3(System.IFormatProvider,System.String,``0,``1,``2)">
            <inheritdoc />
        </member>
        <member name="T:Splat.WrappingLogLevelLogger">
            <summary>
            A prefix logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingLogLevelLogger"/> class.
            Placeholder.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
        </member>
        <member name="M:Splat.WrappingLogLevelLogger.#ctor(Splat.ILogger)">
            <summary>
            A prefix logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingLogLevelLogger"/> class.
            Placeholder.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
        </member>
        <member name="P:Splat.WrappingLogLevelLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingLogLevelLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingLogLevelLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingLogLevelLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingLogLevelLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.WrappingPrefixLogger">
            <summary>
            A prefix logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingPrefixLogger"/> class.
            Placeholder.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
            <param name="callingType">The type which will be calling this logger.</param>
        </member>
        <member name="M:Splat.WrappingPrefixLogger.#ctor(Splat.ILogger,System.Type)">
            <summary>
            A prefix logger which wraps a <see cref="T:Splat.ILogger"/>.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.WrappingPrefixLogger"/> class.
            Placeholder.
            </remarks>
            <param name="inner">The <see cref="T:Splat.ILogger"/> to wrap in this class.</param>
            <param name="callingType">The type which will be calling this logger.</param>
        </member>
        <member name="P:Splat.WrappingPrefixLogger.Level">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingPrefixLogger.Write(System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingPrefixLogger.Write(System.Exception,System.String,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingPrefixLogger.Write(System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="M:Splat.WrappingPrefixLogger.Write(System.Exception,System.String,System.Type,Splat.LogLevel)">
            <inheritdoc />
        </member>
        <member name="T:Splat.PointMathExtensions">
            <summary>
            Extension methods which extend the point mathematics.
            </summary>
        </member>
        <member name="M:Splat.PointMathExtensions.Floor(System.Drawing.Point)">
            <summary>
            Floor the specified point (i.e. round it to integer values).
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <returns>The point that has been floored.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.WithinEpsilonOf(System.Drawing.PointF,System.Drawing.PointF,System.Single)">
            <summary>
            Determines whether two points are within 'epsilon' of each other.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="other">The point to compare against.</param>
            <param name="epsilon">The tolerated epsilon value.</param>
            <returns>If the value is equal based on the epsilon.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.DotProduct(System.Drawing.PointF,System.Drawing.PointF)">
            <summary>
            Calculates the Dot product of two points.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="other">The point to perform the dot product against.</param>
            <returns>The calculated dot product.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.ScaledBy(System.Drawing.PointF,System.Single)">
            <summary>
            Scales a PointF by a scalar factor.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="factor">The amount to scale by.</param>
            <returns>The scaled point.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.Length(System.Drawing.PointF)">
            <summary>
            Calculates the magnitude of a point from (0,0).
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <returns>The length of the point.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.Normalize(System.Drawing.PointF)">
            <summary>
            Normalize the specified PointF (i.e. makes its magnitude = 1.0f).
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <returns>The normalized point.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.AngleInDegrees(System.Drawing.PointF)">
            <summary>
            Calculates the angle in degrees of a PointF.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <returns>The angle that has been generated.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.ProjectAlong(System.Drawing.PointF,System.Drawing.PointF)">
            <summary>
            Projects a PointF along a specified direction.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="direction">The point containing the direction.</param>
            <returns>The projected point.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.ProjectAlongAngle(System.Drawing.PointF,System.Single)">
            <summary>
            Projects a PointF along a specified angle.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="angleInDegrees">The angle in degrees to perform the projection against.</param>
            <returns>The point that has been projected.</returns>
        </member>
        <member name="M:Splat.PointMathExtensions.DistanceTo(System.Drawing.PointF,System.Drawing.PointF)">
            <summary>
            Calculates the distance between two points.
            </summary>
            <param name="value">The point value to use for the calculation.</param>
            <param name="other">The other point to generate for.</param>
            <returns>The distance to the other point.</returns>
        </member>
        <member name="T:Splat.RectangleMathExtensions">
            <summary>
            Extension methods to help with operations associated with the <see cref="T:System.Drawing.RectangleF"/> struct.
            </summary>
        </member>
        <member name="M:Splat.RectangleMathExtensions.Center(System.Drawing.RectangleF)">
            <summary>
            Determine the center of a Rectangle.
            </summary>
            <param name="value">The rectangle to perform the calculation against.</param>
            <returns>The point of the center of the rectangle.</returns>
        </member>
        <member name="M:Splat.RectangleMathExtensions.Divide(System.Drawing.RectangleF,System.Single,Splat.RectEdge)">
            <summary>
            Divide the specified Rectangle into two component rectangles.
            </summary>
            <param name="value">The rectangle to perform the calculation against.</param>
            <param name="amount">Amount to move away from the given edge.</param>
            <param name="fromEdge">The edge to create the slice from.</param>
            <returns>The set of rectnagles that are generated.</returns>
        </member>
        <member name="M:Splat.RectangleMathExtensions.DivideWithPadding(System.Drawing.RectangleF,System.Single,System.Single,Splat.RectEdge)">
            <summary>
            Divide the specified Rectangle into two component rectangles, adding
            a padding between them.
            </summary>
            <param name="value">The rectangle to perform the calculation against.</param>
            <param name="sliceAmount">Amount to move away from the given edge.</param>
            <param name="padding">The amount of padding that is in neither rectangle.</param>
            <param name="fromEdge">The edge to create the slice from.</param>
            <returns>The set of rectangles that are generated.</returns>
        </member>
        <member name="M:Splat.RectangleMathExtensions.InvertWithin(System.Drawing.RectangleF,System.Drawing.RectangleF)">
            <summary>
            <para>Vertically inverts the coordinates of the rectangle within containingRect.</para>
            <para>
            value can effectively be used to change the coordinate system of a rectangle.
            For example, if `rect` is defined for a coordinate system starting at the
            top-left, the result will be a rectangle relative to the bottom-left.
            </para>
            </summary>
            <param name="value">The rectangle to perform the calculation against.</param>
            <param name="containingRect">The containing rectangle.</param>
            <returns>The inverted rectangle.</returns>
        </member>
        <member name="M:Splat.RectangleMathExtensions.Copy(System.Drawing.RectangleF,System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Single},System.Nullable{System.Single})">
            <summary>
            <para>Creates a new RectangleF as a Copy of an existing one .</para>
            <para>
            value is useful when you have a rectangle that is almost what you
            want, but you just want to change a couple properties.
            </para>
            </summary>
            <param name="value">The rectangle to perform the calculation against.</param>
            <param name="x">Optional new x coordinate of the rectangle to use.</param>
            <param name="y">Optional new y coordinate of the rectangle to use.</param>
            <param name="width">Optional new width of the rectangle to use.</param>
            <param name="height">Optional new height of the rectangle to use.</param>
            <param name="top">Optional new top of the rectangle to use.</param>
            <param name="bottom">Optional new bottom of the rectangle to use.</param>
            <returns>The copied rectangle.</returns>
        </member>
        <member name="T:Splat.RectEdge">
            <summary>
            Specified which edge of a rectangle to use.
            </summary>
        </member>
        <member name="F:Splat.RectEdge.Left">
            <summary>
            The left of the rectangle.
            </summary>
        </member>
        <member name="F:Splat.RectEdge.Top">
            <summary>
            The top of the rectangle.
            </summary>
        </member>
        <member name="F:Splat.RectEdge.Right">
            <summary>
            The right of the rectangle.
            </summary>
        </member>
        <member name="F:Splat.RectEdge.Bottom">
            <summary>
            The bottom of the rectangle.
            </summary>
        </member>
        <member name="T:Splat.SizeMathExtensions">
            <summary>
            Extension methods to assist with the <see cref="T:System.Drawing.SizeF"/> struct.
            </summary>
        </member>
        <member name="M:Splat.SizeMathExtensions.WithinEpsilonOf(System.Drawing.SizeF,System.Drawing.SizeF,System.Single)">
            <summary>
            Determines whether two sizes are within epsilon of each other.
            </summary>
            <param name="value">The size we doing the operation against.</param>
            <param name="other">The size to compare if we are equal to.</param>
            <param name="epsilon">The tolerated epsilon value.</param>
            <returns>If the value is equal based on the epsilon.</returns>
        </member>
        <member name="M:Splat.SizeMathExtensions.ScaledBy(System.Drawing.SizeF,System.Single)">
            <summary>
            Scales a size by a scalar value.
            </summary>
            <param name="value">The size we doing the operation against.</param>
            <param name="factor">The amount to scale by.</param>
            <returns>The scaled size.</returns>
        </member>
        <member name="T:Splat.MemoizingMRUCache`2">
            <summary>
            <para>
            This data structure is a representation of a memoizing cache - i.e. a
            class that will evaluate a function, but keep a cache of recently
            evaluated parameters.
            </para>
            <para>
            Since this is a memoizing cache, it is important that this function be a
            "pure" function in the mathematical sense - that a key *always* maps to
            a corresponding return value.
            </para>
            </summary>
            <typeparam name="TParam">The type of the parameter to the calculation function.</typeparam>
            <typeparam name="TVal">The type of the value returned by the calculation
            function.</typeparam>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.#ctor(System.Func{`0,System.Object,`1},System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.MemoizingMRUCache`2"/> class.
            </summary>
            <param name="calculationFunc">The function whose results you want to cache,
            which is provided the key value, and an Tag object that is
            user-defined.</param>
            <param name="maxSize">The size of the cache to maintain, after which old
            items will start to be thrown out.</param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.#ctor(System.Func{`0,System.Object,`1},System.Int32,System.Action{`1})">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.MemoizingMRUCache`2"/> class.
            </summary>
            <param name="calculationFunc">The function whose results you want to cache,
            which is provided the key value, and an Tag object that is
            user-defined.</param>
            <param name="maxSize">The size of the cache to maintain, after which old
            items will start to be thrown out.</param>
            <param name="onRelease">A function to call when a result gets
            evicted from the cache (i.e. because Invalidate was called or the
            cache is full).</param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.#ctor(System.Func{`0,System.Object,`1},System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.MemoizingMRUCache`2"/> class.
            </summary>
            <param name="calculationFunc">The function whose results you want to cache,
            which is provided the key value, and an Tag object that is
            user-defined.</param>
            <param name="maxSize">The size of the cache to maintain, after which old
            items will start to be thrown out.</param>
            <param name="paramComparer">A comparer for the parameter.</param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.#ctor(System.Func{`0,System.Object,`1},System.Int32,System.Action{`1},System.Collections.Generic.IEqualityComparer{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.MemoizingMRUCache`2"/> class.
            </summary>
            <param name="calculationFunc">The function whose results you want to cache,
            which is provided the key value, and an Tag object that is
            user-defined.</param>
            <param name="maxSize">The size of the cache to maintain, after which old
            items will start to be thrown out.</param>
            <param name="onRelease">A function to call when a result gets
            evicted from the cache (i.e. because Invalidate was called or the
            cache is full).</param>
            <param name="paramComparer">A comparer for the parameter.</param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.Get(`0)">
            <summary>
            Gets the value from the specified key.
            </summary>
            <param name="key">The value to pass to the calculation function.</param>
            <returns>The value that we have got.</returns>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.Get(`0,System.Object)">
            <summary>
            Evaluates the function provided, returning the cached value if possible.
            </summary>
            <param name="key">The value to pass to the calculation function.</param>
            <param name="context">An additional optional user-specific parameter.</param>
            <returns>The value that we have got.</returns>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.TryGet(`0,`1@)">
            <summary>
            Tries to get the value if it's available.
            </summary>
            <param name="key">The input value of the key to use.</param>
            <param name="result">The result if available, otherwise it will be the default value.</param>
            <returns>If we were able to retrieve the value or not.</returns>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.Invalidate(`0)">
            <summary>
            Ensure that the next time this key is queried, the calculation
            function will be called.
            </summary>
            <param name="key">The key to invalidate the value for.</param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.InvalidateAll(System.Boolean)">
            <summary>
            Invalidate all the items in the cache.
            </summary>
            <param name="aggregateReleaseExceptions">
            Flag to indicate whether Exceptions during the resource Release call should not fail on the first item.
            But should try all items then throw an aggregate exception.
            </param>
        </member>
        <member name="M:Splat.MemoizingMRUCache`2.CachedValues">
            <summary>
            Returns all values currently in the cache.
            </summary>
            <returns>The values in the cache.</returns>
        </member>
        <member name="T:Splat.DefaultModeDetector">
            <summary>
            Contains the default mode detector to detect if we are currently in a unit test.
            </summary>
        </member>
        <member name="M:Splat.DefaultModeDetector.InUnitTestRunner">
            <inheritdoc />
        </member>
        <member name="T:Splat.IModeDetector">
            <summary>
            Detects if unit tests or design mode are currently running for the current application or library.
            </summary>
        </member>
        <member name="M:Splat.IModeDetector.InUnitTestRunner">
            <summary>
            Gets a value indicating whether the current library or application is running through a unit test.
            </summary>
            <returns>If we are currently running in a unit test.</returns>
        </member>
        <member name="T:Splat.ModeDetection.Mode">
            <summary>
            The default implementation of the <see cref="F:Splat.ModeDetection.Mode.Run"/> and <see cref="F:Splat.ModeDetection.Mode.Test"/> mode.
            </summary>
        </member>
        <member name="F:Splat.ModeDetection.Mode.Run">
            <summary>
            The default implementation of the run mode.
            </summary>
        </member>
        <member name="F:Splat.ModeDetection.Mode.Test">
            <summary>
            The default implementation of the test mode.
            </summary>
        </member>
        <member name="M:Splat.ModeDetection.Mode.InUnitTestRunner">
            <inheritdoc/>
        </member>
        <member name="T:Splat.ModeDetector">
            <summary>
            A helper class which detect if we are currently running via a unit test or design mode.
            </summary>
        </member>
        <member name="M:Splat.ModeDetector.#cctor">
            <summary>
            Initializes static members of the <see cref="T:Splat.ModeDetector"/> class.
            </summary>
        </member>
        <member name="P:Splat.ModeDetector.Current">
            <summary>
            Gets or sets the current mode detector set.
            </summary>
        </member>
        <member name="M:Splat.ModeDetector.OverrideModeDetector(Splat.IModeDetector)">
            <summary>
            Overrides the mode detector with one of your own provided ones.
            </summary>
            <param name="modeDetector">The mode detector to use.</param>
        </member>
        <member name="M:Splat.ModeDetector.InUnitTestRunner">
            <summary>
            Gets a value indicating whether we are currently running from a unit test.
            </summary>
            <returns>If we are currently running from a unit test.</returns>
        </member>
        <member name="T:Splat.DependencyResolverMixins">
            <summary>
            A set of extension methods that assist with the <see cref="T:Splat.IDependencyResolver"/> and <see cref="T:Splat.IMutableDependencyResolver"/> interfaces.
            </summary>
        </member>
        <member name="M:Splat.DependencyResolverMixins.GetService``1(Splat.IReadonlyDependencyResolver,System.String)">
            <summary>
            Gets an instance of the given <typeparamref name="T"/>. Must return <c>null</c>
            if the service is not available (must not throw).
            </summary>
            <typeparam name="T">The type for the object we want to retrieve.</typeparam>
            <param name="resolver">The resolver we are getting the service from.</param>
            <param name="contract">A optional value which will retrieve only a object registered with the same contract.</param>
            <returns>The requested object, if found; <c>null</c> otherwise.</returns>
        </member>
        <member name="M:Splat.DependencyResolverMixins.GetServices``1(Splat.IReadonlyDependencyResolver,System.String)">
            <summary>
            Gets all instances of the given <typeparamref name="T"/>. Must return an empty
            collection if the service is not available (must not return <c>null</c> or throw).
            </summary>
            <typeparam name="T">The type for the object we want to retrieve.</typeparam>
            <param name="resolver">The resolver we are getting the service from.</param>
            <param name="contract">A optional value which will retrieve only a object registered with the same contract.</param>
            <returns>A sequence of instances of the requested <typeparamref name="T"/>. The sequence
            should be empty (not <c>null</c>) if no objects of the given type are available.</returns>
        </member>
        <member name="M:Splat.DependencyResolverMixins.ServiceRegistrationCallback(Splat.IMutableDependencyResolver,System.Type,System.Action{System.IDisposable})">
            <summary>
            Registers a new callback that occurs when a new service with the specified type is registered.
            </summary>
            <param name="resolver">The resolver we want to register the callback with.</param>
            <param name="serviceType">The service type we are wanting to observe.</param>
            <param name="callback">The callback which should be called.</param>
            <returns>A disposable which will stop notifications to the callback.</returns>
        </member>
        <member name="M:Splat.DependencyResolverMixins.WithResolver(Splat.IDependencyResolver,System.Boolean)">
            <summary>
            Override the default Dependency Resolver until the object returned
            is disposed.
            </summary>
            <param name="resolver">The test resolver to use.</param>
            <param name="suppressResolverCallback">If we should suppress the resolver callback notify.</param>
            <returns>A disposable which will reset the resolver back to the original.</returns>
        </member>
        <member name="M:Splat.DependencyResolverMixins.Register``1(Splat.IMutableDependencyResolver,System.Func{``0},System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="factory">A factory method for generating a object of the specified type.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.Register``2(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T" />.
            </summary>
            <typeparam name="TAs">The type to register as.</typeparam>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.RegisterConstant(Splat.IMutableDependencyResolver,System.Object,System.Type,System.String)">
            <summary>
            Registers a constant value which will always return the specified object instance.
            </summary>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="value">The specified instance to always return.</param>
            <param name="serviceType">The type of service to register.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.RegisterConstant``1(Splat.IMutableDependencyResolver,``0,System.String)">
            <summary>
            Registers a constant value which will always return the specified object instance.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="value">The specified instance to always return.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.RegisterLazySingleton(Splat.IMutableDependencyResolver,System.Func{System.Object},System.Type,System.String)">
            <summary>
            Registers a lazy singleton value which will always return the specified object instance once created.
            The value is only generated once someone requests the service from the resolver.
            </summary>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="valueFactory">A factory method for generating a object of the specified type.</param>
            <param name="serviceType">The type of service to register.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.RegisterLazySingleton``1(Splat.IMutableDependencyResolver,System.Func{``0},System.String)">
            <summary>
            Registers a lazy singleton value which will always return the specified object instance once created.
            The value is only generated once someone requests the service from the resolver.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="valueFactory">A factory method for generating a object of the specified type.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.UnregisterCurrent``1(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Unregisters the current the value for the specified type and the optional contract.
            </summary>
            <typeparam name="T">The type of item to unregister.</typeparam>
            <param name="resolver">The resolver to unregister the service with.</param>
            <param name="contract">A optional contract which indicates to only removed the item registered with this contract.</param>
        </member>
        <member name="M:Splat.DependencyResolverMixins.UnregisterAll``1(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Unregisters the all the values for the specified type and the optional contract.
            </summary>
            <typeparam name="T">The type of items to unregister.</typeparam>
            <param name="resolver">The resolver to unregister the services with.</param>
            <param name="contract">A optional contract which indicates to only removed those items registered with this contract.</param>
        </member>
        <member name="T:Splat.FuncDependencyResolver">
            <summary>
            A simple dependency resolver which takes Funcs for all its actions.
            GetService is always implemented via GetServices().LastOrDefault().
            This container is not thread safe.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.FuncDependencyResolver"/> class.
            </remarks>
            <param name="getAllServices">A func which will return all the services contained for the specified service type and contract.</param>
            <param name="register">A func which will be called when a service type and contract are registered.</param>
            <param name="unregisterCurrent">A func which will unregister the current registered element for a service type and contract.</param>
            <param name="unregisterAll">A func which will unregister all the registered elements for a service type and contract.</param>
            <param name="toDispose">A optional disposable which is called when this resolver is disposed.</param>
        </member>
        <member name="M:Splat.FuncDependencyResolver.#ctor(System.Func{System.Type,System.String,System.Collections.Generic.IEnumerable{System.Object}},System.Action{System.Func{System.Object},System.Type,System.String},System.Action{System.Type,System.String},System.Action{System.Type,System.String},System.IDisposable)">
            <summary>
            A simple dependency resolver which takes Funcs for all its actions.
            GetService is always implemented via GetServices().LastOrDefault().
            This container is not thread safe.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.FuncDependencyResolver"/> class.
            </remarks>
            <param name="getAllServices">A func which will return all the services contained for the specified service type and contract.</param>
            <param name="register">A func which will be called when a service type and contract are registered.</param>
            <param name="unregisterCurrent">A func which will unregister the current registered element for a service type and contract.</param>
            <param name="unregisterAll">A func which will unregister all the registered elements for a service type and contract.</param>
            <param name="toDispose">A optional disposable which is called when this resolver is disposed.</param>
        </member>
        <member name="M:Splat.FuncDependencyResolver.GetService(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.GetServices(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.HasRegistration(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.Register(System.Func{System.Object},System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.UnregisterCurrent(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.UnregisterAll(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.ServiceRegistrationCallback(System.Type,System.String,System.Action{System.IDisposable})">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Splat.FuncDependencyResolver.Dispose(System.Boolean)">
            <summary>
            Disposes of all managed memory from this class.
            </summary>
            <param name="isDisposing">If we are currently disposing managed resources.</param>
        </member>
        <member name="T:Splat.IDependencyResolver">
            <summary>
            Represents a dependency resolver, a service to look up global class
            instances or types.
            </summary>
        </member>
        <member name="T:Splat.IMutableDependencyResolver">
            <summary>
            Represents a dependency resolver where types can be registered after setup.
            </summary>
        </member>
        <member name="M:Splat.IMutableDependencyResolver.HasRegistration(System.Type,System.String)">
            <summary>
            Check to see if a resolvers has a registration for a type.
            </summary>
            <param name="serviceType">The type to check for registration.</param>
            <returns>Whether there is a registration for the type.</returns>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.IMutableDependencyResolver.Register(System.Func{System.Object},System.Type,System.String)">
            <summary>
            Register a function with the resolver which will generate a object
            for the specified service type.
            Optionally a contract can be registered which will indicate
            that registration will only work with that contract.
            Most implementations will use a stack based approach to allow for multile items to be registered.
            </summary>
            <param name="factory">The factory function which generates our object.</param>
            <param name="serviceType">The type which is used for the registration.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
        </member>
        <member name="M:Splat.IMutableDependencyResolver.UnregisterCurrent(System.Type,System.String)">
            <summary>
            Unregisters the current item based on the specified type and contract.
            </summary>
            <param name="serviceType">The service type to unregister.</param>
            <param name="contract">The optional contract value, which will only remove the value associated with the contract.</param>
        </member>
        <member name="M:Splat.IMutableDependencyResolver.UnregisterAll(System.Type,System.String)">
            <summary>
            Unregisters all the values associated with the specified type and contract.
            </summary>
            <param name="serviceType">The service type to unregister.</param>
            <param name="contract">The optional contract value, which will only remove the value associated with the contract.</param>
        </member>
        <member name="M:Splat.IMutableDependencyResolver.ServiceRegistrationCallback(System.Type,System.String,System.Action{System.IDisposable})">
            <summary>
            <para>
            Register a callback to be called when a new service matching the type
            and contract is registered.
            </para>
            <para>
            When registered, the callback is also called for each currently matching
            service.
            </para>
            </summary>
            <returns>When disposed removes the callback.</returns>
            <param name="serviceType">The type which is used for the registration.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
            <param name="callback">The callback which will be called when the specified service type and contract are registered.</param>
        </member>
        <member name="P:Splat.InternalLocator.Current">
            <summary>
            Gets the read only dependency resolver. This class is used throughout
            libraries for many internal operations as well as for general use
            by applications. If this isn't assigned on startup, a default, highly
            capable implementation will be used, and it is advised for most people
            to simply use the default implementation.
            </summary>
            <value>The dependency resolver.</value>
        </member>
        <member name="P:Splat.InternalLocator.CurrentMutable">
            <summary>
            Gets the mutable dependency resolver.
            The default resolver is also a mutable resolver, so this will be non-null.
            Use this to register new types on startup if you are using the default resolver.
            </summary>
        </member>
        <member name="M:Splat.InternalLocator.Dispose">
            <summary>
            Releases unmanaged and - optionally - managed resources.
            </summary>
        </member>
        <member name="M:Splat.InternalLocator.SetLocator(Splat.IDependencyResolver)">
            <summary>
            Allows setting the dependency resolver.
            </summary>
            <param name="dependencyResolver">The dependency resolver to set.</param>
        </member>
        <member name="M:Splat.InternalLocator.RegisterResolverCallbackChanged(System.Action)">
            <summary>
            This method allows libraries to register themselves to be set up
            whenever the dependency resolver changes. Applications should avoid
            this method, it is usually used for libraries that depend on service
            location.
            </summary>
            <param name="callback">A callback that is invoked when the
            resolver is changed. This callback is also invoked immediately,
            to configure the current resolver.</param>
            <returns>When disposed, removes the callback. You probably can
            ignore this.</returns>
        </member>
        <member name="M:Splat.InternalLocator.SuppressResolverCallbackChangedNotifications">
            <summary>
            This method will prevent resolver changed notifications from happening until
            the returned <see cref="T:System.IDisposable"/> is disposed.
            </summary>
            <returns>A disposable which when disposed will indicate the change
            notification is no longer needed.</returns>
        </member>
        <member name="M:Splat.InternalLocator.AreResolverCallbackChangedNotificationsEnabled">
            <summary>
            Indicates if the we are notifying external classes of updates to the resolver being changed.
            </summary>
            <returns>A value indicating whether the notifications are happening.</returns>
        </member>
        <member name="T:Splat.IReadonlyDependencyResolver">
            <summary>
            An interface for interacting with a dependency resolver in a read-only fashion.
            </summary>
        </member>
        <member name="M:Splat.IReadonlyDependencyResolver.GetService(System.Type,System.String)">
            <summary>
            Gets an instance of the given <paramref name="serviceType"/>. Must return <c>null</c>
            if the service is not available (must not throw).
            </summary>
            <param name="serviceType">The object type.</param>
            <param name="contract">A optional value which will retrieve only a object registered with the same contract.</param>
            <returns>The requested object, if found; <c>null</c> otherwise.</returns>
        </member>
        <member name="M:Splat.IReadonlyDependencyResolver.GetServices(System.Type,System.String)">
            <summary>
            Gets all instances of the given <paramref name="serviceType"/>. Must return an empty
            collection if the service is not available (must not return <c>null</c> or throw).
            </summary>
            <param name="serviceType">The object type.</param>
            <param name="contract">A optional value which will retrieve only objects registered with the same contract.</param>
            <returns>A sequence of instances of the requested <paramref name="serviceType"/>. The sequence
            should be empty (not <c>null</c>) if no objects of the given type are available.</returns>
        </member>
        <member name="T:Splat.Locator">
            <summary>
            A Locator which will host the container for dependency injection based operations.
            </summary>
        </member>
        <member name="P:Splat.Locator.Current">
            <summary>
            Gets the read only dependency resolver. This class is used throughout
            libraries for many internal operations as well as for general use
            by applications. If this isn't assigned on startup, a default, highly
            capable implementation will be used, and it is advised for most people
            to simply use the default implementation.
            </summary>
            <value>The dependency resolver.</value>
        </member>
        <member name="P:Splat.Locator.CurrentMutable">
            <summary>
            Gets the mutable dependency resolver.
            The default resolver is also a mutable resolver, so this will be non-null.
            Use this to register new types on startup if you are using the default resolver.
            </summary>
        </member>
        <member name="P:Splat.Locator.InternalLocator">
            <summary>
            Gets or sets the current locator instance.
            Used mostly for testing purposes.
            </summary>
        </member>
        <member name="M:Splat.Locator.SetLocator(Splat.IDependencyResolver)">
            <summary>
            Allows setting the dependency resolver.
            </summary>
            <param name="dependencyResolver">The dependency resolver to set.</param>
        </member>
        <member name="M:Splat.Locator.GetLocator">
            <summary>
            Gets the full locator.
            Note you should use <see cref="P:Splat.Locator.Current"/> or <see cref="P:Splat.Locator.CurrentMutable"/> in most situations.
            </summary>
            <returns>The locator.</returns>
        </member>
        <member name="M:Splat.Locator.RegisterResolverCallbackChanged(System.Action)">
            <summary>
            This method allows libraries to register themselves to be set up
            whenever the dependency resolver changes. Applications should avoid
            this method, it is usually used for libraries that depend on service
            location.
            </summary>
            <param name="callback">A callback that is invoked when the
            resolver is changed. This callback is also invoked immediately,
            to configure the current resolver.</param>
            <returns>When disposed, removes the callback. You probably can
            ignore this.</returns>
        </member>
        <member name="M:Splat.Locator.SuppressResolverCallbackChangedNotifications">
            <summary>
            This method will prevent resolver changed notifications from happening until
            the returned <see cref="T:System.IDisposable"/> is disposed.
            </summary>
            <returns>A disposable which when disposed will indicate the change
            notification is no longer needed.</returns>
        </member>
        <member name="M:Splat.Locator.AreResolverCallbackChangedNotificationsEnabled">
            <summary>
            Indicates if the we are notifying external classes of updates to the resolver being changed.
            </summary>
            <returns>A value indicating whether the notifications are happening.</returns>
        </member>
        <member name="T:Splat.ModernDependencyResolver">
            <summary>
            <para>
            This class is a dependency resolver written for modern C# 5.0 times.
            It implements all registrations via a Factory method. With the power
            of Closures, you can actually implement most lifetime styles (i.e.
            construct per call, lazy construct, singleton) using this.
            </para>
            <para>
            Unless you have a very compelling reason not to, this is the only class
            you need in order to do dependency resolution, don't bother with using
            a full IoC container.
            </para>
            <para>This container is not thread safe.</para>
            </summary>
        </member>
        <member name="M:Splat.ModernDependencyResolver.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.ModernDependencyResolver"/> class.
            </summary>
        </member>
        <member name="M:Splat.ModernDependencyResolver.#ctor(System.Collections.Generic.Dictionary{System.ValueTuple{System.Type,System.String},System.Collections.Generic.List{System.Func{System.Object}}})">
            <summary>
            Initializes a new instance of the <see cref="T:Splat.ModernDependencyResolver"/> class.
            </summary>
            <param name="registry">A registry of services.</param>
        </member>
        <member name="M:Splat.ModernDependencyResolver.HasRegistration(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.Register(System.Func{System.Object},System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.GetService(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.GetServices(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.UnregisterCurrent(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.UnregisterAll(System.Type,System.String)">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.ServiceRegistrationCallback(System.Type,System.String,System.Action{System.IDisposable})">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.Duplicate">
            <summary>
            Generates a duplicate of the resolver with all the current registrations.
            Useful if you want to generate temporary resolver using the <see cref="M:Splat.DependencyResolverMixins.WithResolver(Splat.IDependencyResolver,System.Boolean)"/> method.
            </summary>
            <returns>The newly generated <see cref="T:Splat.ModernDependencyResolver"/> class with the current registrations.</returns>
        </member>
        <member name="M:Splat.ModernDependencyResolver.Dispose">
            <inheritdoc />
        </member>
        <member name="M:Splat.ModernDependencyResolver.Dispose(System.Boolean)">
            <summary>
            Disposes of all managed memory from this class.
            </summary>
            <param name="isDisposing">If we are currently disposing managed resources.</param>
        </member>
        <member name="T:Splat.NullServiceType">
            <summary>
            Null Service Type.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.NullServiceType"/> class.
            </remarks>
            <param name="factory">The value factory.</param>
        </member>
        <member name="M:Splat.NullServiceType.#ctor(System.Func{System.Object})">
            <summary>
            Null Service Type.
            </summary>
            <remarks>
            Initializes a new instance of the <see cref="T:Splat.NullServiceType"/> class.
            </remarks>
            <param name="factory">The value factory.</param>
        </member>
        <member name="P:Splat.NullServiceType.Factory">
            <summary>
            Gets the Factory.
            </summary>
        </member>
        <member name="T:Splat.ResolverMixins">
            <summary>
            Resolver Mixins.
            </summary>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterAnd``1(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T" />.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterAnd``1(Splat.IMutableDependencyResolver,System.Func{``0},System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T" />.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="factory">A factory method for generating a object of the specified type.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterAnd``2(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T" />.
            </summary>
            <typeparam name="TAs">The type to register as.</typeparam>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterAnd``2(Splat.IMutableDependencyResolver,System.Func{``1},System.String)">
            <summary>
            Registers a factory for the given <typeparamref name="T" />.
            </summary>
            <typeparam name="TAs">The type to register as.</typeparam>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="factory">A factory method for generating a object of the specified type.</param>
            <param name="contract">A optional contract value which will indicates to only generate the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterConstantAnd(Splat.IMutableDependencyResolver,System.Object,System.Type,System.String)">
            <summary>
            Registers a constant value which will always return the specified object instance.
            </summary>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="value">The specified instance to always return.</param>
            <param name="serviceType">The type of service to register.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterConstantAnd``1(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Registers a constant value which will always return the specified object instance.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterConstantAnd``1(Splat.IMutableDependencyResolver,``0,System.String)">
            <summary>
            Registers a constant value which will always return the specified object instance.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="value">The specified instance to always return.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterLazySingletonAnd(Splat.IMutableDependencyResolver,System.Func{System.Object},System.Type,System.String)">
            <summary>
            Registers a lazy singleton value which will always return the specified object instance once created.
            The value is only generated once someone requests the service from the resolver.
            </summary>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="valueFactory">A factory method for generating a object of the specified type.</param>
            <param name="serviceType">The type of service to register.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterLazySingletonAnd``1(Splat.IMutableDependencyResolver,System.String)">
            <summary>
            Registers a lazy singleton value which will always return the specified object instance once created.
            The value is only generated once someone requests the service from the resolver.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="M:Splat.ResolverMixins.RegisterLazySingletonAnd``1(Splat.IMutableDependencyResolver,System.Func{``0},System.String)">
            <summary>
            Registers a lazy singleton value which will always return the specified object instance once created.
            The value is only generated once someone requests the service from the resolver.
            </summary>
            <typeparam name="T">The service type to register for.</typeparam>
            <param name="resolver">The resolver to register the service type with.</param>
            <param name="valueFactory">A factory method for generating a object of the specified type.</param>
            <param name="contract">A optional contract value which will indicates to only return the value if this contract is specified.</param>
            <returns>The resolver.</returns>
        </member>
        <member name="T:Splat.ServiceLocationInitialization">
            <summary>
            Initialization logic for Splat interacting with Dependency Resolvers.
            </summary>
        </member>
        <member name="M:Splat.ServiceLocationInitialization.InitializeSplat(Splat.IMutableDependencyResolver)">
            <summary>
            Registers all the default registrations that are needed by the Splat module.
            </summary>
            <param name="resolver">The resolver to register the needed service types against.</param>
        </member>
        <member name="T:Splat.TargetFrameworkExtensions">
            <summary>
            Extension methods that help to get the target framework for a assembly.
            </summary>
        </member>
        <member name="M:Splat.TargetFrameworkExtensions.GetTargetFrameworkName(System.Reflection.Assembly)">
            <summary>
            Gets the target framework for an assembly.
            </summary>
            <param name="assembly">The assembly to get the target framework for.</param>
            <returns>The target framework or null if not known.</returns>
        </member>
    </members>
</doc>
