﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata>
    <id>SuitDotNet</id>
    <version>1.3.190</version>
    <authors>SuitDotNet</authors>
    <description>Package Description</description>
    <repository type="git" commit="594b92fae96196904b74b9d8dbcc11738122edf4" />
    <dependencies>
      <group targetFramework=".NETFramework4.7.2">
        <dependency id="IKVM.WINDWARD" version="*******" exclude="Build,Analyzers" />
        <dependency id="OpenTK" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="OpenTK.GLControl" version="3.1.0" exclude="Build,Analyzers" />
        <dependency id="System.Buffers" version="4.5.1" exclude="Build,Analyzers" />
      </group>
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="PresentationCore" targetFramework=".NETFramework4.7.2" />
      <frameworkAssembly assemblyName="PresentationFramework" targetFramework=".NETFramework4.7.2" />
      <frameworkAssembly assemblyName="System.Windows.Forms" targetFramework=".NETFramework4.7.2" />
      <frameworkAssembly assemblyName="System.Xaml" targetFramework=".NETFramework4.7.2" />
      <frameworkAssembly assemblyName="WindowsBase" targetFramework=".NETFramework4.7.2" />
      <frameworkAssembly assemblyName="WindowsFormsIntegration" targetFramework=".NETFramework4.7.2" />
    </frameworkAssemblies>
  </metadata>
</package>