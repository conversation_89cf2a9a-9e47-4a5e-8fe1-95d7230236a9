﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.AppContext</name>
  </assembly>
  <members>
    <member name="T:System.AppContext">
      <summary>Proporciona a miembros para establecer y recuperar datos acerca del contexto de una aplicación. </summary>
    </member>
    <member name="P:System.AppContext.BaseDirectory">
      <summary>Obtiene la ruta de acceso del directorio base que el interpretador de ensamblados utiliza para buscar ensamblados. </summary>
      <returns>la ruta de acceso del directorio base que el interpretador de ensamblados utiliza para buscar ensamblados. </returns>
    </member>
    <member name="M:System.AppContext.SetSwitch(System.String,System.Boolean)">
      <summary>Establece el valor de un modificador. </summary>
      <param name="switchName">Nombre del modificador. </param>
      <param name="isEnabled">Valor del modificador. </param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="switchName" /> es null. </exception>
      <exception cref="T:System.ArgumentException">El valor de <paramref name="switchName" /> es <see cref="F:System.String.Empty" />. </exception>
    </member>
    <member name="M:System.AppContext.TryGetSwitch(System.String,System.Boolean@)">
      <summary>True para obtener el valor de un modificador. </summary>
      <returns>true si se ha establecido <paramref name="switchName" /> y el argumento <paramref name="isEnabled" /> contiene el valor del modificador; de lo contrario, es false. </returns>
      <param name="switchName">Nombre del modificador. </param>
      <param name="isEnabled">Cuando este método vuelve, contiene el valor de <paramref name="switchName" /> si <paramref name="switchName" /> se ha encontrado, o false si <paramref name="switchName" /> no se encontró.Este parámetro se pasa sin inicializar.</param>
      <exception cref="T:System.ArgumentNullException">El valor de <paramref name="switchName" /> es null. </exception>
      <exception cref="T:System.ArgumentException">El valor de <paramref name="switchName" /> es <see cref="F:System.String.Empty" />. </exception>
    </member>
  </members>
</doc>