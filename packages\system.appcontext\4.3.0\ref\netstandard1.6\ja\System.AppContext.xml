﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.AppContext</name>
  </assembly>
  <members>
    <member name="T:System.AppContext">
      <summary>設定と、アプリケーションのコンテキストに関するデータを取得するメンバーを提供します。</summary>
    </member>
    <member name="P:System.AppContext.BaseDirectory">
      <summary>アセンブリの競合回避モジュールがアセンブリを探すために使用するベース ディレクトリのパス名を取得します。</summary>
      <returns>アセンブリの競合回避モジュールがアセンブリを探すために使用するベース ディレクトリのパス名を指定します。</returns>
    </member>
    <member name="M:System.AppContext.SetSwitch(System.String,System.Boolean)">
      <summary>スイッチの値を設定します。</summary>
      <param name="switchName">スイッチの名前。</param>
      <param name="isEnabled">スイッチの値。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="switchName" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="switchName" /> は <see cref="F:System.String.Empty" /> です。</exception>
    </member>
    <member name="M:System.AppContext.TryGetSwitch(System.String,System.Boolean@)">
      <summary>True、スイッチの値を取得します。</summary>
      <returns>true場合<paramref name="switchName" />が設定され、<paramref name="isEnabled" />引数には、スイッチの値が含まれていますそれ以外の場合、falseです。</returns>
      <param name="switchName">スイッチの名前。</param>
      <param name="isEnabled">このメソッドが戻るときの値が含まれています。<paramref name="switchName" />場合<paramref name="switchName" />が見つかり、またはfalse場合<paramref name="switchName" />が見つかりませんでした。このパラメーターは初期化せずに渡されます。</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="switchName" /> は null です。</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="switchName" /> は <see cref="F:System.String.Empty" /> です。</exception>
    </member>
  </members>
</doc>