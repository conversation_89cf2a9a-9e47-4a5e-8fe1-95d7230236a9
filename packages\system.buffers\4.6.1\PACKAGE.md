## About

Provides resource pooling of any type for performance-critical applications that allocate and deallocate objects frequently.

## Main Types

The main types provided by this library are:

- System.Buffers.ArrayPool<T>

## Additional Documentation

- API reference can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.buffers

## Related Packages

ArrayPool is shipped as part of the shared framework starting with .NET Core 3.1.

## License

System.Buffers is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
