﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2012/06/nuspec.xsd">
  <metadata>
    <id>System.Buffers</id>
    <version>4.6.1</version>
    <authors>Microsoft</authors>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <license type="expression">MIT</license>
    <licenseUrl>https://licenses.nuget.org/MIT</licenseUrl>
    <icon>Icon.png</icon>
    <readme>PACKAGE.md</readme>
    <projectUrl>https://github.com/dotnet/maintenance-packages</projectUrl>
    <description>System.Buffers</description>
    <copyright>© Microsoft Corporation. All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <repository type="git" url="https://github.com/dotnet/maintenance-packages" commit="6b84308c9ad012f53240d72c1d716d7e42546483" />
    <dependencies>
      <group targetFramework=".NETFramework4.6.2" />
      <group targetFramework=".NETCoreApp2.0" />
      <group targetFramework=".NETStandard2.0" />
      <group targetFramework=".NETStandard2.1" />
    </dependencies>
  </metadata>
</package>