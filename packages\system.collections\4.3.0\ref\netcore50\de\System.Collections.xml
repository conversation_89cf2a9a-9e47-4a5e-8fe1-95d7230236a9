﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>Verwaltet ein komprimiertes Array von Bitwerten, die als boolesche Werte dargestellt sind. true bezeichnet hierbei ein gesetztes Bit (1), und false bezeichnet ein nicht gesetztes Bit (0).</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-<PERSON>lasse mit Bitwerten, die aus dem angegebenen Array von booleschen Werten kopiert werden.</summary>
      <param name="values">Ein boolesches Array mit den zu kopierenden Werten. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-Klasse mit Bitwerten, die aus dem angegebenen Bytearray kopiert werden.</summary>
      <param name="bytes">Ein Bytearray, das die zu kopierenden Werte enthält, wobei jedes Byte acht aufeinander folgende Bits darstellt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-Klasse mit Bitwerten, die aus dem angegebenen <see cref="T:System.Collections.BitArray" /> kopiert werden.</summary>
      <param name="bits">Das <see cref="T:System.Collections.BitArray" />, das kopiert werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-Klasse, die die angegebene Anzahl von Bitwerten enthalten kann. Diese werden am Anfang auf false festgelegt.</summary>
      <param name="length">Die Anzahl der Bitwerte im neuen <see cref="T:System.Collections.BitArray" />. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-Klasse, die die angegebene Anzahl von Bitwerten enthalten kann. Diese werden am Anfang auf den angegebenen Wert festgelegt.</summary>
      <param name="length">Die Anzahl der Bitwerte im neuen <see cref="T:System.Collections.BitArray" />. </param>
      <param name="defaultValue">Der boolesche Wert, der jedem Bit zugeordnet werden soll. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.BitArray" />-Klasse mit Bitwerten, die aus dem angegebenen Array von 32-Bit-Ganzzahlen kopiert werden.</summary>
      <param name="values">Ein Array von Ganzzahlen, das die zu kopierenden Werte enthält, wobei jede Ganzzahl 32 aufeinander folgende Bits darstellt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>Führt eine bitweise AND-Operation mit den Elementen im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> aus.</summary>
      <returns>Die aktuelle Instanz, die das Ergebnis der bitweisen AND-Operation für die Elemente im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> enthält.</returns>
      <param name="value">Das <see cref="T:System.Collections.BitArray" />, mit dem die bitweise AND-Operation ausgeführt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>Ruft den Wert des Bits an einer bestimmten Position im <see cref="T:System.Collections.BitArray" /> ab.</summary>
      <returns>Der Wert des Bits an Position <paramref name="index" />.</returns>
      <param name="index">Der nullbasierte Index des abzurufenden Werts. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.BitArray" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" /> für das gesamte <see cref="T:System.Collections.BitArray" />.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>Ruft den Wert eines Bits an einer bestimmten Position im <see cref="T:System.Collections.BitArray" /> ab oder legt diesen fest.</summary>
      <returns>Der Wert des Bits an Position <paramref name="index" />.</returns>
      <param name="index">Der nullbasierte Index des Werts, der abgerufen oder festgelegt werden soll. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>Ruft die Anzahl von Elementen im <see cref="T:System.Collections.BitArray" /> ab oder legt diese fest.</summary>
      <returns>Die Anzahl der Elemente im <see cref="T:System.Collections.BitArray" />.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>Invertiert alle Bitwerte im aktuellen <see cref="T:System.Collections.BitArray" />, sodass auf true festgelegte Elemente auf false und auf false festgelegte Elemente auf true festgelegt werden.</summary>
      <returns>Die aktuelle Instanz mit invertierten Bitwerten.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>Führt eine bitweise OR-Operation mit den Elementen im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> aus.</summary>
      <returns>Die aktuelle Instanz, die das Ergebnis der bitweisen OR-Operation für die Elemente im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> enthält.</returns>
      <param name="value">Das <see cref="T:System.Collections.BitArray" />, mit dem die bitweise OR-Operation ausgeführt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>Legt das Bit an einer bestimmten Position im <see cref="T:System.Collections.BitArray" /> auf den angegebenen Wert fest.</summary>
      <param name="index">Der nullbasierte Index des festzulegenden Bits. </param>
      <param name="value">Der boolesche Wert, der dem Bit zugeordnet werden soll. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>Legt alle Bits im <see cref="T:System.Collections.BitArray" /> auf den angegebenen Wert fest.</summary>
      <param name="value">Der boolesche Wert, der allen Bits zugeordnet werden soll. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente des <see cref="T:System.Collections.BitArray" /> in ein <see cref="T:System.Array" />, beginnend beim angegebenen <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.BitArray" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>Ruft die Anzahl der Elemente im <see cref="T:System.Collections.BitArray" /> ab.</summary>
      <returns>Die Anzahl der Elemente im <see cref="T:System.Collections.BitArray" />.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf <see cref="T:System.Collections.BitArray" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.BitArray" /> synchronisiert (threadsicher) ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.BitArray" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.BitArray" /> synchronisiert werden kann.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>Führt eine bitweise exklusive OR-Operation mit den Elementen im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> aus.</summary>
      <returns>Die aktuelle Instanz, die das Ergebnis der bitweisen exklusiven OR-Operation für die Elemente im aktuellen <see cref="T:System.Collections.BitArray" /> in Bezug auf die entsprechenden Elemente im angegebenen <see cref="T:System.Collections.BitArray" /> enthält. </returns>
      <param name="value">Das <see cref="T:System.Collections.BitArray" />, mit dem die bitweise exklusive OR-Operation ausgeführt werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>Stellt Objekte zum Ausführen eines Strukturvergleichs von zwei Auflistungsobjekten bereit.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>Ruft ein vordefiniertes Objekt ab, das einen Strukturvergleich von zwei Objekten ausführt.</summary>
      <returns>Ein vordefiniertes Objekt, mit dem ein Strukturvergleich von zwei Auflistungsobjekten ausgeführt wird.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>Ruft ein vordefiniertes Objekt ab, das zwei Objekte auf Strukturgleichheit überprüft.</summary>
      <returns>Ein vordefiniertes Objekt, mit dem zwei Auflistungsobjekte auf Strukturgleichheit überprüft werden.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>Stellt eine Basisklasse für Implementierungen der generischen <see cref="T:System.Collections.Generic.IComparer`1" />-Schnittstelle bereit.</summary>
      <typeparam name="T">Der Typ der zu vergleichenden Objekte.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Comparer`1" />-Klasse.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>Vergleicht beim Überschreiben in einer abgeleiteten Klasse zwei Objekte gleichen Typs und gibt über den zurückgegebenen Wert an, ob das eine Objekt kleiner, größer oder gleich dem anderen Objekt ist.</summary>
      <returns>Eine ganze Zahl mit Vorzeichen, die die relativen Werte von <paramref name="x" /> und <paramref name="y" /> angibt, wie in der folgenden Tabelle veranschaulicht.Wert Bedeutung Kleiner als 0 <paramref name="x" /> ist kleiner als <paramref name="y" />.Zero <paramref name="x" /> ist gleich <paramref name="y" />.Größer als 0 (null) <paramref name="x" /> ist größer als <paramref name="y" />.</returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
      <exception cref="T:System.ArgumentException">Der Typ <paramref name="T" /> implementiert weder die generische <see cref="T:System.IComparable`1" />-Schnittstelle noch die <see cref="T:System.IComparable" />-Schnittstelle.</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>Erstellt mithilfe des angegebenen Vergleichs einen Vergleich.</summary>
      <returns>Der neue Comparer.</returns>
      <param name="comparison">Der Vergleich, der verwendet werden soll.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>Gibt für den vom generischen Argument angegebenen Typ einen Standardvergleich für die Sortierreihenfolgen zurück.</summary>
      <returns>Ein Objekt, das <see cref="T:System.Collections.Generic.Comparer`1" /> erbt und als Sortierreihenfolgenvergleich für den Typ <paramref name="T" /> fungiert.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>Vergleicht zwei Objekte und gibt einen Wert zurück, der angibt, ob ein Wert niedriger, gleich oder größer als der andere Wert ist.</summary>
      <returns>Eine ganze Zahl mit Vorzeichen, die die relativen Werte von <paramref name="x" /> und <paramref name="y" /> angibt, wie in der folgenden Tabelle veranschaulicht.Wert Bedeutung Kleiner als 0<paramref name="x" /> ist kleiner als <paramref name="y" />.Zero<paramref name="x" /> ist gleich <paramref name="y" />.Größer als 0 (null)<paramref name="x" /> ist größer als <paramref name="y" />.</returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> oder <paramref name="y" /> weist einen Typ auf, der nicht in den Typ <paramref name="T" /> umgewandelt werden kann.- oder -<paramref name="x" /> und <paramref name="y" /> implementieren weder die generische <see cref="T:System.IComparable`1" />-Schnittstelle noch die generische <see cref="T:System.IComparable" />-Schnittstelle.</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>Stellt eine Auflistung von Schlüsseln und Werten dar.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <typeparam name="TKey">Der Typ der Schlüssel im Wörterbuch.</typeparam>
      <typeparam name="TValue">Der Typ der Werte im Wörterbuch.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse mit der Standardanfangskapazität, wobei der Standardgleichheitsvergleich für den Schlüsseltyp verwendet wird.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopierte Elemente enthält und den Standardgleichheitsvergleich für den Schlüsseltyp verwendet.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.Dictionary`2" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopierte Elemente enthält und den angegebenen <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> verwendet.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.Dictionary`2" /> kopiert werden.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Implementierung, die zum Vergleichen von Schlüsseln verwendet werden soll, oder null, wenn der Standard-<see cref="T:System.Collections.Generic.EqualityComparer`1" /> für diesen Schlüsseltyp verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse mit der Standardanfangskapazität und dem angegebenen <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Implementierung, die zum Vergleichen von Schlüsseln verwendet werden soll, oder null, wenn der Standard-<see cref="T:System.Collections.Generic.EqualityComparer`1" /> für diesen Schlüsseltyp verwendet werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse mit der Standardanfangskapazität, wobei der Standardgleichheitsvergleich für den Schlüsseltyp verwendet wird.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.Dictionary`2" /> enthalten kann.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Dictionary`2" />-Klasse mit der angegebenen Anfangskapazität und dem angegebenen <see cref="T:System.Collections.Generic.IEqualityComparer`1" />.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.Dictionary`2" /> enthalten kann.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Implementierung, die zum Vergleichen von Schlüsseln verwendet werden soll, oder null, wenn der Standard-<see cref="T:System.Collections.Generic.EqualityComparer`1" /> für diesen Schlüsseltyp verwendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>Fügt dem Wörterbuch den angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Der Schlüssel des hinzuzufügenden Elements.</param>
      <param name="value">Der Wert des hinzuzufügenden Elements.Der Wert kann für Verweistypen null sein.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.Dictionary`2" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>Entfernt sämtliche Schlüssel und Werte aus dem <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>Ruft den <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> ab, mit dem die Gleichheit der Schlüssel für das Wörterbuch bestimmt wird. </summary>
      <returns>Die Implementierung der generischen <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Schnittstelle, mit der die Gleichheit der Schlüssel für das aktuelle <see cref="T:System.Collections.Generic.Dictionary`2" /> bestimmt und Hashwerte für die Schlüssel bereitgestellt werden.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>Bestimmt, ob das <see cref="T:System.Collections.Generic.Dictionary`2" /> den angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.Dictionary`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.Generic.Dictionary`2" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.Dictionary`2" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.Dictionary`2" /> ein Element mit dem angegebenen Wert enthält, andernfalls false.</returns>
      <param name="value">Der im <see cref="T:System.Collections.Generic.Dictionary`2" /> zu suchende Wert.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>Ruft die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.Dictionary`2" /> ab.</summary>
      <returns>Die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.Dictionary`2" /> durchläuft.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />-Struktur für das <see cref="T:System.Collections.Generic.Dictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>Ruft den Wert ab, der dem angegebenen Schlüssel zugeordnet ist, oder legt diesen fest.</summary>
      <returns>Der dem angegebenen Schlüssel zugeordnete Wert.Wenn der angegebene Schlüssel nicht gefunden wird, löst ein Get-Vorgang eine <see cref="T:System.Collections.Generic.KeyNotFoundException" /> aus, und durch einen Set-Vorgang wird ein neues Element mit dem angegebenen Schlüssel erstellt.</returns>
      <param name="key">Der Schlüssel des abzurufenden oder festzulegenden Werts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Die Eigenschaft wird abgerufen, und der <paramref name="key" /> ist nicht in der Auflistung vorhanden.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>Ruft eine Auflistung ab, die die Schlüssel im <see cref="T:System.Collections.Generic.Dictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />, die die Schlüssel im <see cref="T:System.Collections.Generic.Dictionary`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>Entfernt den Wert mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true, wenn das Element gefunden und entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="key" /> nicht im ursprünglichen <see cref="T:System.Collections.Generic.Dictionary`2" /> gefunden wurde.</returns>
      <param name="key">Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> den angegebenen Wert mit dem angegebenen Schlüssel hinzu.</summary>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die den Schlüssel und den Wert darstellt, die dem <see cref="T:System.Collections.Generic.Dictionary`2" /> hinzugefügt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">Der Schlüssel des <paramref name="keyValuePair" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.Dictionary`2" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> einen bestimmten Schlüssel und Wert enthält.</summary>
      <returns>true, wenn das <paramref name="keyValuePair" /> in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wird, andernfalls false.</returns>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die in die <see cref="T:System.Collections.Generic.ICollection`1" /> gesucht werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.Generic.ICollection`1" /> in ein Array vom Typ <see cref="T:System.Collections.Generic.KeyValuePair`2" />, beginnend am angegebenen Arrayindex.</summary>
      <param name="array">Ein eindimensionales Array vom Typ <see cref="T:System.Collections.Generic.KeyValuePair`2" />, in das die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Elemente aus der <see cref="T:System.Collections.Generic.ICollection`1" /> kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das Wörterbuch schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Dictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Entfernt einen Schlüssel und einen Wert aus dem Wörterbuch.</summary>
      <returns>true, wenn der durch das <paramref name="keyValuePair" /> dargestellte Schlüssel und Wert gefunden und entfernt wurden, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="keyValuePair" /> nicht im ursprünglichen <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die den Schlüssel und den Wert darstellt, die aus dem <see cref="T:System.Collections.Generic.Dictionary`2" /> entfernt werden sollen.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" /> vom Typ <paramref name="TKey" />, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" /> vom Typ <paramref name="TValue" />, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine Auflistung ab, die die Schlüssel des <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />-Objekts enthält.</summary>
      <returns>Eine Auflistung, die die Schlüssel des <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />-Objekts enthält.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ruft eine Auflistung ab, die die Werte des <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />-Objekts enthält.</summary>
      <returns>Eine Auflistung, die die Werte des <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />-Objekts enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.Generic.ICollection`1" /> in ein Array, wobei am angegebenen Arrayindex begonnen wird.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus der <see cref="T:System.Collections.Generic.ICollection`1" /> kopierten Elemente darstellt.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder - <paramref name="array" /> hat keine nullbasierte Indizierung.- oder - Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder - Der Typ der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Dictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Fügt dem Wörterbuch den angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Das Objekt, das als Schlüssel verwendet werden soll.</param>
      <param name="value">Das Objekt, das als Wert verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> weist einen Typ auf, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.Dictionary`2" /> nicht zugeordnet werden kann.- oder - <paramref name="value" /> weist einen Typ auf, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.Generic.Dictionary`2" /> nicht zugeordnet werden kann.- oder - Ein Wert mit dem gleichen Namen ist bereits im <see cref="T:System.Collections.Generic.Dictionary`2" /> vorhanden.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.IDictionary" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Gibt einen <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Dictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Dictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ruft den Wert mit dem angegebenen Schlüssel ab oder legt diesen fest.</summary>
      <returns>Der dem angegebenen Schlüssel zugeordnete Wert, oder null, wenn <paramref name="key" /> nicht im Wörterbuch enthalten ist oder <paramref name="key" /> einen Typ aufweist, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.Dictionary`2" /> nicht zugeordnet werden kann.</returns>
      <param name="key">Der Schlüssel des abzurufenden Werts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Es wird ein Wert zugewiesen, und <paramref name="key" /> ist ein Typ, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.Dictionary`2" /> nicht zugeordnet werden kann.- oder - Es wird ein Wert zugewiesen, und <paramref name="value" /> ist ein Typ, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.Generic.Dictionary`2" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>Ruft den dem angegebenen Schlüssel zugeordneten Wert ab.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.Dictionary`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der Schlüssel des abzurufenden Werts.</param>
      <param name="value">Enthält nach dem Beenden dieser Methode den Wert, der dem angegebenen Schlüssel zugeordnet ist, wenn der Schlüssel gefunden wurde, oder andernfalls den Standardwert für den Typ des <paramref name="value" />-Parameters.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>Ruft eine Auflistung ab, die die Werte im <see cref="T:System.Collections.Generic.Dictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />, die die Werte im <see cref="T:System.Collections.Generic.Dictionary`2" /> enthält.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>Listet die Elemente eines <see cref="T:System.Collections.Generic.Dictionary`2" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element im <see cref="T:System.Collections.Generic.Dictionary`2" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>Gibt sämtliche vom <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element des <see cref="T:System.Collections.Generic.Dictionary`2" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element im Wörterbuch an der aktuellen Position des Enumerators (als <see cref="T:System.Collections.DictionaryEntry" />).</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Ruft den Schlüssel des Elements an der aktuellen Position des Enumerators ab.</summary>
      <returns>Der Schlüssel des Elements im Wörterbuch an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Ruft den Wert des Elements an der aktuellen Position des Enumerators ab.</summary>
      <returns>Der Wert des Elements im Wörterbuch an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators (als <see cref="T:System.Object" />).</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>Stellt die Auflistung von Schlüsseln in einem <see cref="T:System.Collections.Generic.Dictionary`2" /> dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />-Klasse, die die Schlüssel im angegebenen <see cref="T:System.Collections.Generic.Dictionary`2" /> angibt.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.Dictionary`2" />, dessen Schlüssel in der neuen <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> angegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />-Elemente in ein vorhandenes eindimensionales <see cref="T:System.Array" />, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> ist größer als der verfügbare Platz von <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>Ruft die Anzahl der in der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> befindlichen Elemente ab.</summary>
      <returns>Die Anzahl der in der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> befindlichen Elemente.Das Abrufen des Wertes dieser Eigenschaft ist ein O(1)-Vorgang.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> für die <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="item">Das Objekt, das <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Entfernt alle Elemente aus <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn sich <paramref name="item" /> in <see cref="T:System.Collections.Generic.ICollection`1" /> befindet, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.ICollection`1" /> zu suchende Objekt.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> gibt diese Eigenschaft immer true zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn das <paramref name="item" /> nicht in der ursprünglichen <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="item">Das aus dem <see cref="T:System.Collections.Generic.ICollection`1" /> zu entfernende Objekt.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder -<paramref name="array" /> hat keine nullbasierte Indizierung.- oder -Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder -Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem eine Auflistung durchlaufen werden kann.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>Zählt die Elemente einer <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>Stellt die Auflistung von Werten in einem <see cref="T:System.Collections.Generic.Dictionary`2" /> dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />-Klasse, die die Werte im angegebenen <see cref="T:System.Collections.Generic.Dictionary`2" /> angibt.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.Dictionary`2" />, dessen Werte in der neuen <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> angegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />-Elemente in ein vorhandenes eindimensionales <see cref="T:System.Array" />, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> ist größer als der verfügbare Platz von <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> für die <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="item">Das Objekt, das <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Entfernt alle Elemente aus <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn sich <paramref name="item" /> in <see cref="T:System.Collections.Generic.ICollection`1" /> befindet, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.ICollection`1" /> zu suchende Objekt.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> gibt diese Eigenschaft immer true zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.ICollection`1" />.Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn das <paramref name="item" /> nicht in der ursprünglichen <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="item">Das aus dem <see cref="T:System.Collections.Generic.ICollection`1" /> zu entfernende Objekt.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder -<paramref name="array" /> hat keine nullbasierte Indizierung.- oder -Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder -Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem eine Auflistung durchlaufen werden kann.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Gibt sämtliche vom <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>Stellt eine Basisklasse für Implementierungen der generischen <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Schnittstelle bereit.</summary>
      <typeparam name="T">Der Typ der zu vergleichenden Objekte.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.EqualityComparer`1" />-Klasse.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>Gibt für den vom generischen Argument angegebenen Typ einen Standardgleichheitsvergleich zurück.</summary>
      <returns>Die Standardinstanz der <see cref="T:System.Collections.Generic.EqualityComparer`1" />-Klasse für den Typ <paramref name="T" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>Ermittelt beim Überschreiben in einer abgeleiteten Klasse, ob zwei Objekte vom Typ <paramref name="T" /> gleich sind.</summary>
      <returns>true, wenn die angegebenen Objekte gleich sind, andernfalls false.</returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>Dient beim Überschreiben in einer abgeleiteten Klasse für das angegebene Objekt als Hashfunktion für Hashalgorithmen und Datenstrukturen wie Hashtabellen.</summary>
      <returns>Ein Hashcode für das angegebene Objekt.</returns>
      <param name="obj">Das Objekt, für das ein Hashcode abgerufen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>Bestimmt, ob die angegebenen Objekte gleich sind.</summary>
      <returns>true, wenn die angegebenen Objekte gleich sind, andernfalls false.</returns>
      <param name="x">Das erste zu vergleichende Objekt.</param>
      <param name="y">Das zweite zu vergleichende Objekt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>Gibt einen Hashcode für das angegebene Objekt zurück.</summary>
      <returns>Ein Hashcode für das angegebene Objekt.</returns>
      <param name="obj">Das <see cref="T:System.Object" />, für das ein Hashcode zurückgegeben werden soll.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>Stellt eine Menge von Werten dar.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, finden Sie unter der Reference Source.</summary>
      <typeparam name="T">Der Typ der Elemente im Hashset.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.HashSet`1" />-Klasse, bei der für den Mengentyp der Standardgleichheitsvergleich verwendet wird.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.HashSet`1" />-Klasse, die den Standardgleichheitsvergleich des Mengentyps verwendet, aus der angegebenen Auflistung kopierte Elemente enthält und über ausreichend Kapazität für die Anzahl von kopierten Elementen verfügt.</summary>
      <param name="collection">Die Auflistung, deren Elemente in den neuen Satz kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.HashSet`1" />-Klasse, die den angegebenen Gleichheitsvergleich des Mengentyps verwendet, aus der angegebenen Auflistung kopierte Elemente enthält und über ausreichend Kapazität für die Anzahl der kopierten Elemente verfügt.</summary>
      <param name="collection">Die Auflistung, deren Elemente in den neuen Satz kopiert werden.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Implementierung, die zum Vergleichen von Werten in der Menge verwendet werden soll, oder null, wenn die <see cref="T:System.Collections.Generic.EqualityComparer`1" />-Standardimplementierung für diesen Mengentyp verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.HashSet`1" />-Klasse, bei der für den Mengentyp der angegebene Gleichheitsvergleich verwendet wird.</summary>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Implementierung, die zum Vergleichen von Werten in der Menge verwendet werden soll, oder null, wenn die <see cref="T:System.Collections.Generic.EqualityComparer`1" />-Standardimplementierung für diesen Mengentyp verwendet werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>Fügt das angegebene Element zu einer Menge hinzu.</summary>
      <returns>true, wenn das Element zum <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt hinzugefügt wird, false, wenn das Element bereits vorhanden ist.</returns>
      <param name="item">Der Element, das zur Menge hinzugefügt wird.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>Entfernt alle Elemente aus einem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>Ruft das <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Objekt ab, mit dem die Gleichheit der Werte in der Menge bestimmt wird.</summary>
      <returns>Das <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Objekt, mit dem die Gleichheit der Werte in der Menge bestimmt wird.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt das angegebene Element enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt das angegebene Element enthält, andernfalls false.</returns>
      <param name="item">Das Element, das im <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt gesucht werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>Kopiert die Elemente eines <see cref="T:System.Collections.Generic.HashSet`1" />-Objekts in ein Array.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus dem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die Elemente eines <see cref="T:System.Collections.Generic.HashSet`1" />-Objekts in ein Array, beginnend am angegebenen Index des Arrays.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus dem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> ist größer als die Länge des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Kopiert die angegebene Anzahl von Elementen eines <see cref="T:System.Collections.Generic.HashSet`1" />-Objekts in ein Array, beginnend am angegebenen Index des Arrays.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus dem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <param name="count">Die Anzahl von Elementen, die nach <paramref name="array" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" /> ist größer als die Länge des Ziel-<paramref name="array" />.- oder - <paramref name="count" /> ist größer als der verfügbare Platz zwischen dem <paramref name="index" /> und dem Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>Ruft die Anzahl der Elemente in einer Menge ab.</summary>
      <returns>Die Anzahl der Elemente in der Menge.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Entfernt alle Elemente in der angegebenen Auflistung aus dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt.</summary>
      <param name="other">Die Auflistung der Elemente, die aus dem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt entfernt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />-Objekt für das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt so, dass nur noch Elemente vorhanden sind, die in diesem Objekt und in der angegebenen Auflistung vorhanden sind.</summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine echte Teilmenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine echte Teilmenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine echte Obermenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine echte Obermenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine Teilmenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine Teilmenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine Obermenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt eine Obermenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt und eine angegebene Auflistung gemeinsame Elemente enthalten.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt und <paramref name="other" /> mindestens ein gemeinsames Element enthalten, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>Entfernt das angegebene Element aus einem <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt.</summary>
      <returns>true, wenn das Element gefunden und entfernt wurde, andernfalls false.Diese Methode gibt false zurück, wenn <paramref name="item" /> nicht im <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt gefunden wurde.</returns>
      <param name="item">Das zu entfernende Element.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Entfernt alle Elemente aus einer <see cref="T:System.Collections.Generic.HashSet`1" />-Auflistung, die die im angegebenen Prädikat definierten Bedingungen erfüllen.</summary>
      <returns>Die Anzahl von Elementen, die aus der <see cref="T:System.Collections.Generic.HashSet`1" />-Auflistung entfernt wurden.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für die Elemente definiert, die entfernt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ermittelt, ob ein <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt und die angegebene Auflistung dieselben Elemente enthalten.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt gleich <paramref name="other" /> ist, anderfalls „false“.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt so, dass nur noch Elemente vorhanden sind, die entweder in diesem Objekt oder aber in der angegebenen Auflistung, nicht jedoch in beiden vorhanden sind.</summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Fügt einem <see cref="T:System.Collections.Generic.ICollection`1" />-Objekt ein Element hinzu.</summary>
      <param name="item">Das Objekt, das dem <see cref="T:System.Collections.Generic.ICollection`1" />-Objekt hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> ist schreibgeschützt.</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob eine Auflistung schreibgeschützt ist.</summary>
      <returns>true, wenn die Auflistung schreibgeschützt ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />-Objekt, das zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />-Objekt, das zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>Legt die Kapazität eines <see cref="T:System.Collections.Generic.HashSet`1" />-Objekts auf die Anzahl der tatsächlich enthaltenen Elemente fest, aufgerundet auf einen nahe gelegenen implementierungsabhängigen Wert.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt so, dass alle Elemente vorhanden sind, die in diesem Objekt, in der angegebenen Auflistung oder beiden vorhanden sind.</summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.HashSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>Listet die Elemente eines <see cref="T:System.Collections.Generic.HashSet`1" />-Objekts auf.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.HashSet`1" />-Auflistung an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" />-Objekt verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.HashSet`1" />-Auflistung.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators (als <see cref="T:System.Object" />).</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>Stellt eine doppelt verknüpfte Liste dar.</summary>
      <typeparam name="T">Gibt den Elementtyp der verknüpften Liste an.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.LinkedList`1" />-Klasse.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.LinkedList`1" />-Klasse, die aus dem angegebenen <see cref="T:System.Collections.IEnumerable" /> kopierte Elemente enthält und eine ausreichende Kapazität für die Anzahl der kopierten Elemente aufweist. </summary>
      <param name="collection">Das <see cref="T:System.Collections.IEnumerable" />, dessen Elemente in die neue <see cref="T:System.Collections.Generic.LinkedList`1" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Fügt den angegebenen neuen Knoten nach dem angegebenen vorhandenen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzu.</summary>
      <param name="node">Der <see cref="T:System.Collections.Generic.LinkedListNode`1" />, nach dem der <paramref name="newNode" /> eingefügt werden soll.</param>
      <param name="newNode">Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.- oder -<paramref name="newNode" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> befindet sich nicht in der aktuellen <see cref="T:System.Collections.Generic.LinkedList`1" />.- oder -<paramref name="newNode" /> gehört zu einer anderen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Fügt nach dem angegebenen vorhandenen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> einen neuen Knoten mit dem angegebenen Wert hinzu.</summary>
      <returns>Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" />.</returns>
      <param name="node">Der <see cref="T:System.Collections.Generic.LinkedListNode`1" />, nach dem ein neuer <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" /> eingefügt werden soll.</param>
      <param name="value">Der der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzuzufügende Wert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> befindet sich nicht in der aktuellen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>Fügt den angegebenen neuen Knoten vor dem angegebenen vorhandenen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzu.</summary>
      <param name="node">Der <see cref="T:System.Collections.Generic.LinkedListNode`1" />, vor dem der <paramref name="newNode" /> eingefügt werden soll.</param>
      <param name="newNode">Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.- oder -<paramref name="newNode" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> befindet sich nicht in der aktuellen <see cref="T:System.Collections.Generic.LinkedList`1" />.- oder -<paramref name="newNode" /> gehört zu einer anderen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>Fügt vor dem angegebenen vorhandenen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> einen neuen Knoten mit dem angegebenen Wert hinzu.</summary>
      <returns>Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" />.</returns>
      <param name="node">Der <see cref="T:System.Collections.Generic.LinkedListNode`1" />, vor dem ein neuer <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" /> eingefügt werden soll.</param>
      <param name="value">Der der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzuzufügende Wert.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> befindet sich nicht in der aktuellen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Fügt am Anfang der <see cref="T:System.Collections.Generic.LinkedList`1" /> den angegebenen neuen Knoten hinzu.</summary>
      <param name="node">Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der am Anfang der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> gehört zu einer anderen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>Fügt am Anfang der <see cref="T:System.Collections.Generic.LinkedList`1" /> einen neuen Knoten mit dem angegebenen Wert hinzu.</summary>
      <returns>Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" />.</returns>
      <param name="value">Der Wert, der am Anfang der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Fügt am Ende der <see cref="T:System.Collections.Generic.LinkedList`1" /> den angegebenen neuen Knoten hinzu.</summary>
      <param name="node">Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der am Ende der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> gehört zu einer anderen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>Fügt am Ende der <see cref="T:System.Collections.Generic.LinkedList`1" /> einen neuen Knoten mit dem angegebenen Wert hinzu.</summary>
      <returns>Der neue <see cref="T:System.Collections.Generic.LinkedListNode`1" /> mit dem <paramref name="value" />.</returns>
      <param name="value">Der Wert, der am Ende der <see cref="T:System.Collections.Generic.LinkedList`1" /> hinzugefügt werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>Entfernt alle Knoten aus der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>Bestimmt, ob sich ein Wert in der <see cref="T:System.Collections.Generic.LinkedList`1" /> befindet.</summary>
      <returns>true, wenn sich <paramref name="value" /> in der <see cref="T:System.Collections.Generic.LinkedList`1" /> befindet, andernfalls false.</returns>
      <param name="value">Der in der <see cref="T:System.Collections.Generic.LinkedList`1" /> zu suchende Wert.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die gesamte <see cref="T:System.Collections.Generic.LinkedList`1" /> in ein kompatibles eindimensionales <see cref="T:System.Array" />, beginnend am angegebenen Index des Zielarrays.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.LinkedList`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.LinkedList`1" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>Ruft die Anzahl der Knoten ab, die tatsächlich in der <see cref="T:System.Collections.Generic.LinkedList`1" /> enthalten sind.</summary>
      <returns>Die Anzahl der Knoten, die tatsächlich in der <see cref="T:System.Collections.Generic.LinkedList`1" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>Sucht den ersten Knoten, der den angegebenen Wert enthält.</summary>
      <returns>Der erste <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der den angegebenen Wert enthält, sofern er gefunden wird, andernfalls null.</returns>
      <param name="value">Der in der <see cref="T:System.Collections.Generic.LinkedList`1" /> zu suchende Wert.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>Sucht den letzten Knoten, der den angegebenen Wert enthält.</summary>
      <returns>Der letzte <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der den angegebenen Wert enthält, sofern er gefunden wird, andernfalls null.</returns>
      <param name="value">Der in der <see cref="T:System.Collections.Generic.LinkedList`1" /> zu suchende Wert.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>Ruft den ersten Knoten der <see cref="T:System.Collections.Generic.LinkedList`1" /> ab.</summary>
      <returns>Der erste <see cref="T:System.Collections.Generic.LinkedListNode`1" /> der <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der <see cref="T:System.Collections.Generic.LinkedList`1" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> für die <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>Ruft den letzten Knoten der <see cref="T:System.Collections.Generic.LinkedList`1" /> ab.</summary>
      <returns>Der letzte <see cref="T:System.Collections.Generic.LinkedListNode`1" /> der <see cref="T:System.Collections.Generic.LinkedList`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>Entfernt den angegebenen Knoten aus der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <param name="node">Der <see cref="T:System.Collections.Generic.LinkedListNode`1" />, der aus der <see cref="T:System.Collections.Generic.LinkedList`1" /> entfernt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" /> befindet sich nicht in der aktuellen <see cref="T:System.Collections.Generic.LinkedList`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>Entfernt das erste Vorkommen des angegebenen Werts aus der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true, wenn das Element, das den <paramref name="value" /> enthält, erfolgreich entfernt wurde, andernfalls false.  Diese Methode gibt auch dann false zurück, wenn der <paramref name="value" /> nicht in der ursprünglichen <see cref="T:System.Collections.Generic.LinkedList`1" /> gefunden wurde.</returns>
      <param name="value">Die Wert, der aus der <see cref="T:System.Collections.Generic.LinkedList`1" /> entfernt werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>Entfernt den Knoten am Anfang der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="T:System.Collections.Generic.LinkedList`1" /> ist leer.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>Entfernt den Knoten am Ende der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <exception cref="T:System.InvalidOperationException">Die <see cref="T:System.Collections.Generic.LinkedList`1" /> ist leer.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Fügt am Ende der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.</summary>
      <param name="value">Der Wert, der am Ende der <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.LinkedList`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder -<paramref name="array" /> hat keine nullbasierte Indizierung.- oder -Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder -Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.LinkedList`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.LinkedList`1" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die verknüpfte Liste als Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der die verknüpfte Liste als Auflistung durchlaufen kann.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.LinkedList`1" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.LinkedList`1" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.LinkedList`1" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.Diese Klasse kann nicht vererbt werden.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>Stellt einen Knoten in einer <see cref="T:System.Collections.Generic.LinkedList`1" /> dar.Diese Klasse kann nicht vererbt werden.</summary>
      <typeparam name="T">Gibt den Elementtyp der verknüpften Liste an.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.LinkedListNode`1" />-Klasse, die den angegebenen Wert enthält.</summary>
      <param name="value">Der Wert, der im <see cref="T:System.Collections.Generic.LinkedListNode`1" /> enthalten sein soll.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>Ruft die <see cref="T:System.Collections.Generic.LinkedList`1" /> ab, der der <see cref="T:System.Collections.Generic.LinkedListNode`1" /> angehört.</summary>
      <returns>Ein Verweis auf die <see cref="T:System.Collections.Generic.LinkedList`1" />, der der <see cref="T:System.Collections.Generic.LinkedListNode`1" /> angehört, oder null, wenn der <see cref="T:System.Collections.Generic.LinkedListNode`1" /> nicht verknüpft ist.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>Ruft den nächsten Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> ab.</summary>
      <returns>Ein Verweis auf den nächsten Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> oder null, wenn der aktuelle Knoten das letzte Element (<see cref="P:System.Collections.Generic.LinkedList`1.Last" />) der <see cref="T:System.Collections.Generic.LinkedList`1" /> ist.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>Ruft den vorherigen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> ab.</summary>
      <returns>Ein Verweis auf den vorherigen Knoten in der <see cref="T:System.Collections.Generic.LinkedList`1" /> oder null, wenn der aktuelle Knoten das erste Element (<see cref="P:System.Collections.Generic.LinkedList`1.First" />) der <see cref="T:System.Collections.Generic.LinkedList`1" /> ist.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>Ruft den im Knoten enthaltenen Wert ab.</summary>
      <returns>Der im Knoten enthaltene Wert.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>Stellt eine stark typisierte Liste von Objekten dar, auf die über einen Index zugegriffen werden kann.Stellt Methoden zum Durchsuchen, Sortieren und Bearbeiten von Listen bereit.Um den .NET Framework-Quellcode für diesen Typ zu durchsuchen, rufen Sie die Verweisquelle auf.</summary>
      <typeparam name="T">Der Typ der Elemente in der Liste.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.List`1" />-Klasse, die die Standardanfangskapazität aufweist.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.List`1" />-Klasse, die aus der angegebenen Auflistung kopierte Elemente enthält und eine ausreichende Kapazität für die Anzahl der kopierten Elemente aufweist.</summary>
      <param name="collection">Die Auflistung, deren Elemente in die neue Liste kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.List`1" />-Klasse, die die angegebene Anfangskapazität aufweist.</summary>
      <param name="capacity">Die Anzahl von Elementen, die anfänglich in der neuen Liste gespeichert werden können.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist kleiner als 0. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>Fügt am Ende der <see cref="T:System.Collections.Generic.List`1" /> ein Objekt hinzu.</summary>
      <param name="item">Das Objekt, das am Ende der <see cref="T:System.Collections.Generic.List`1" /> hinzugefügt werden soll.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>Fügt die Elemente der angegebenen Auflistung am Ende von <see cref="T:System.Collections.Generic.List`1" /> hinzu.</summary>
      <param name="collection">Die Sammlung, deren Elemente am Ende der <see cref="T:System.Collections.Generic.List`1" /> hinzugefügt werden sollen.Die Auflistung an sich kann nicht null sein, sie kann jedoch Elemente enthalten, die null sind, wenn Typ <paramref name="T" /> einen Referenztyp darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>Gibt einen schreibgeschützten <see cref="T:System.Collections.Generic.IList`1" />-Wrapper für die aktuelle Auflistung zurück.</summary>
      <returns>Eine <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />, die als schreibgeschützter Wrapper um die aktuelle <see cref="T:System.Collections.Generic.List`1" /> fungiert.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>Durchsucht mithilfe des angegebenen Vergleichs einen Bereich von Elementen in der sortierten <see cref="T:System.Collections.Generic.List`1" /> nach einem Element und gibt den nullbasierten Index des Elements zurück.</summary>
      <returns>Der nullbasierte Index von <paramref name="item" /> in der sortierten <see cref="T:System.Collections.Generic.List`1" />, sofern <paramref name="item" /> gefunden wird, andernfalls eine negative Zahl, die das bitweise Komplement des Indexes des nächsten Elements darstellt, das größer als <paramref name="item" /> ist, oder, wenn kein größeres Element vorhanden ist, das bitweise Komplement von <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="index">Der nullbasierte Startindex des zu durchsuchenden Bereichs.</param>
      <param name="count">Die Länge des zu durchsuchenden Bereichs.</param>
      <param name="item">Das zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleichen von Elementen verwendet werden soll, oder null, wenn der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> verwendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Bereich in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> ist null, und der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für den Typ <paramref name="T" /> finden.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>Durchsucht mithilfe des Standardcomparers die gesamte sortierte <see cref="T:System.Collections.Generic.List`1" /> nach einem Element und gibt den nullbasierten Index des Elements zurück.</summary>
      <returns>Der nullbasierte Index von <paramref name="item" /> in der sortierten <see cref="T:System.Collections.Generic.List`1" />, sofern <paramref name="item" /> gefunden wird, andernfalls eine negative Zahl, die das bitweise Komplement des Indexes des nächsten Elements darstellt, das größer als <paramref name="item" /> ist, oder, wenn kein größeres Element vorhanden ist, das bitweise Komplement von <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Das zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <exception cref="T:System.InvalidOperationException">Der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für Typ <paramref name="T" /> finden.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>Durchsucht mithilfe des angegebenen Comparers die gesamte sortierte <see cref="T:System.Collections.Generic.List`1" /> nach einem Element und gibt den nullbasierten Index des Elements zurück.</summary>
      <returns>Der nullbasierte Index von <paramref name="item" /> in der sortierten <see cref="T:System.Collections.Generic.List`1" />, sofern <paramref name="item" /> gefunden wird, andernfalls eine negative Zahl, die das bitweise Komplement des Indexes des nächsten Elements darstellt, das größer als <paramref name="item" /> ist, oder, wenn kein größeres Element vorhanden ist, das bitweise Komplement von <see cref="P:System.Collections.Generic.List`1.Count" />.</returns>
      <param name="item">Das zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleich von Elementen verwendet werden soll.- oder - null zur Verwendung des Standardcomparers <see cref="P:System.Collections.Generic.Comparer`1.Default" />.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> ist null, und der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für den Typ <paramref name="T" /> finden.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>Ruft die Gesamtzahl der Elemente ab, die die interne Datenstruktur ohne Änderung der Größe aufnehmen kann, oder legt diese Anzahl fest.</summary>
      <returns>Die Anzahl der Elemente, die <see cref="T:System.Collections.Generic.List`1" /> enthalten kann, bevor eine Größenanpassung erforderlich ist.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" /> ist auf einen Wert festgelegt, der kleiner ist als <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Arbeitsspeicher im System verfügbar.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>Entfernt alle Elemente aus der <see cref="T:System.Collections.Generic.List`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>Bestimmt, ob sich ein Element in <see cref="T:System.Collections.Generic.List`1" /> befindet.</summary>
      <returns>true, wenn das <paramref name="item" /> in der <see cref="T:System.Collections.Generic.List`1" /> gefunden wird, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>Kopiert einen Bereich von Elementen aus der <see cref="T:System.Collections.Generic.List`1" /> in ein kompatibles eindimensionales Array, beginnend ab dem angegebenen Index im Zielarray.</summary>
      <param name="index">Der nullbasierte Index in der Quell-<see cref="T:System.Collections.Generic.List`1" />, ab dem mit dem Kopieren begonnen wird.</param>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.List`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <param name="count">Die Anzahl der zu kopierenden Elemente.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="arrayIndex" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> ist größer oder gleich dem <see cref="P:System.Collections.Generic.List`1.Count" /> der Quell-<see cref="T:System.Collections.Generic.List`1" />.- oder - Die Anzahl der Elemente ab <paramref name="index" /> bis zum Ende der Quell-<see cref="T:System.Collections.Generic.List`1" /> ist größer als der zwischen <paramref name="arrayIndex" /> und dem Ende des Ziel-<paramref name="array" /> verfügbare Raum. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>Kopiert die gesamte <see cref="T:System.Collections.Generic.List`1" /> in ein kompatibles eindimensionales Array, wobei am Anfang des Zielarrays begonnen wird.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.List`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.List`1" /> ist größer als die Anzahl der Elemente, die das Ziel-<paramref name="array" /> enthalten kann.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die gesamte <see cref="T:System.Collections.Generic.List`1" /> in ein kompatibles eindimensionales Array, beginnend am angegebenen Index des Zielarrays.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.List`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.List`1" /> überschreitet den verfügbaren Platz vom <paramref name="arrayIndex" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in <see cref="T:System.Collections.Generic.List`1" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in <see cref="T:System.Collections.Generic.List`1" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>Bestimmt, ob die <see cref="T:System.Collections.Generic.List`1" /> Elemente enthält, die mit den vom angegebenen Prädikat definierten Bedingungen übereinstimmen.</summary>
      <returns>true, wenn <see cref="T:System.Collections.Generic.List`1" /> ein oder mehr Elemente enthält, die die durch das angegebene Prädikat definierten Bedingungen erfüllen; andernfalls false.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für die Elemente definiert, nach denen gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt das erste Vorkommen im gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Das erste Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, sofern vorhanden, andernfalls der Standardwert für den Typ <paramref name="T" />.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>Ruft alle Elemente ab, die die vom angegebenen Prädikat definierten Bedingungen erfüllen.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.List`1" /> mit allen Elementen, die ggf. die durch das angegebene Prädikat definierten Bedingungen erfüllen, andernfalls eine leere <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für die Elemente definiert, nach denen gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des ersten Vorkommens innerhalb des Bereichs von Elementen im <see cref="T:System.Collections.Generic.List`1" /> zurück, der am angegebenen Index beginnt und die angegebene Anzahl von Elementen umfasst.</summary>
      <returns>Der nullbasierte Index des ersten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="startIndex">Der nullbasierte Startindex für die Suche.</param>
      <param name="count">Die Anzahl der Elemente im zu durchsuchenden Abschnitt.</param>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.- oder - <paramref name="count" /> ist kleiner als 0.- oder - <paramref name="startIndex" /> und <paramref name="count" /> geben keinen gültigen Abschnitt in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des ersten Vorkommens innerhalb des Bereichs von Elementen im <see cref="T:System.Collections.Generic.List`1" /> zurück, der vom angegebenen Index bis zum letzten Element reicht.</summary>
      <returns>Der nullbasierte Index des ersten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="startIndex">Der nullbasierte Startindex für die Suche.</param>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des ersten Vorkommens im gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Der nullbasierte Index des ersten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt das letzte Vorkommen im gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Das letzte Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, sofern vorhanden, andernfalls der Standardwert für den Typ <paramref name="T" />.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des ersten Vorkommens innerhalb des Bereichs von Elementen im <see cref="T:System.Collections.Generic.List`1" /> zurück, der die angegebene Anzahl von Elementen umfasst und am angegebenen Index endet.</summary>
      <returns>Der nullbasierte Index des letzten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="startIndex">Der nullbasierte Startindex für die Rückwärtssuche.</param>
      <param name="count">Die Anzahl der Elemente im zu durchsuchenden Abschnitt.</param>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.- oder - <paramref name="count" /> ist kleiner als 0.- oder - <paramref name="startIndex" /> und <paramref name="count" /> geben keinen gültigen Abschnitt in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des letzten Vorkommens innerhalb des Bereichs von Elementen im <see cref="T:System.Collections.Generic.List`1" /> zurück, der vom ersten Element bis zum angegeben Index reicht.</summary>
      <returns>Der nullbasierte Index des letzten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="startIndex">Der nullbasierte Startindex für die Rückwärtssuche.</param>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>Sucht nach einem Element, das die durch das angegebene Prädikat definierten Bedingungen erfüllt, und gibt den nullbasierten Index des letzten Vorkommens im gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Der nullbasierte Index des letzten Vorkommnisses eines Elements, das mit den durch <paramref name="match" /> definierten Bedingungen übereinstimmt, sofern gefunden, andernfalls –1.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für das Element definiert, nach dem gesucht werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>Führt die angegebene Aktion für jedes Element der <see cref="T:System.Collections.Generic.List`1" /> aus.</summary>
      <param name="action">Der <see cref="T:System.Action`1" />-Delegat, der für jedes Element von <see cref="T:System.Collections.Generic.List`1" /> ausgeführt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.List`1" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.List`1.Enumerator" /> für die <see cref="T:System.Collections.Generic.List`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>Erstellt eine flache Kopie eines Bereichs von Elementen in der Quell-<see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>Eine flache Kopie eines Bereichs von Elementen in der Quell-<see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="index">Der nullbasierte <see cref="T:System.Collections.Generic.List`1" />-Index, an dem der Bereich beginnt.</param>
      <param name="count">Die Anzahl der Elemente im Bereich.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Bereich von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des ersten Vorkommens innerhalb der gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Der nullbasierte Index des ggf. ersten Vorkommens von <paramref name="item" /> in der gesamten <see cref="T:System.Collections.Generic.List`1" />, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des ersten Vorkommens innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> zurück, der sich vom angegebenen Index bis zum letzten Element erstreckt.</summary>
      <returns>Der nullbasierte Index des ersten Vorkommens von <paramref name="item" /> innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" />, der sich von <paramref name="index" /> bis zum letzten Element erstreckt, sofern gefunden, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="index">Der nullbasierte Startindex für die Suche.0 (null) ist in einer leeren Liste gültig.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des ersten Vorkommens innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> zurück, der am angegebenen Index beginnt und die angegebene Anzahl von Elementen enthält.</summary>
      <returns>Der nullbasierte Index des ersten Vorkommens von <paramref name="item" /> innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" />, der am <paramref name="index" /> beginnt und <paramref name="count" /> Anzahl von Elementen enthält, sofern gefunden, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="index">Der nullbasierte Startindex für die Suche.0 (null) ist in einer leeren Liste gültig.</param>
      <param name="count">Die Anzahl der Elemente im zu durchsuchenden Abschnitt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.- oder - <paramref name="count" /> ist kleiner als 0.- oder - <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Abschnitt in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>Fügt am angegebenen Index ein Element in die <see cref="T:System.Collections.Generic.List`1" /> ein.</summary>
      <param name="index">Der nullbasierte Index, an dem <paramref name="item" /> eingefügt werden soll.</param>
      <param name="item">Das einzufügende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist größer als <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>Fügt die Elemente einer Auflistung am angegebenen Index in die <see cref="T:System.Collections.Generic.List`1" /> ein.</summary>
      <param name="index">Der nullbasierte Index, an dem neue Elemente eingefügt werden sollen.</param>
      <param name="collection">Die Sammlung, deren Elemente in die <see cref="T:System.Collections.Generic.List`1" /> eingefügt werden sollen.Die Auflistung an sich kann nicht null sein, sie kann jedoch Elemente enthalten, die null sind, wenn Typ <paramref name="T" /> einen Referenztyp darstellt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist größer als <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>Ruft das Element am angegebenen Index ab oder legt dieses fest.</summary>
      <returns>Das Element am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des Elements, das abgerufen oder festgelegt werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist größer oder gleich <see cref="P:System.Collections.Generic.List`1.Count" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des letzten Vorkommens innerhalb der gesamten <see cref="T:System.Collections.Generic.List`1" /> zurück.</summary>
      <returns>Der nullbasierte Index des ggf. letzten Vorkommens von <paramref name="item" /> in der gesamten <see cref="T:System.Collections.Generic.List`1" />, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des letzten Vorkommens innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> zurück, der sich vom angegebenen Index bis zum letzten Element erstreckt.</summary>
      <returns>Der nullbasierte Index des letzten Vorkommens von <paramref name="item" /> innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" />, der sich vom ersten Element bis <paramref name="index" /> erstreckt, sofern gefunden, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="index">Der nullbasierte Startindex für die Rückwärtssuche.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>Sucht nach dem angegebenen Objekt und gibt den nullbasierten Index des letzten Vorkommens innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> zurück, der die angegebene Anzahl von Elementen enthält und am angegebenen Index endet.</summary>
      <returns>Der nullbasierte Index des letzten Vorkommens von <paramref name="item" /> innerhalb des Bereichs von Elementen in der <see cref="T:System.Collections.Generic.List`1" />, der <paramref name="count" /> Anzahl von Elementen enthält und am <paramref name="index" /> endet, sofern gefunden, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.List`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
      <param name="index">Der nullbasierte Startindex für die Rückwärtssuche.</param>
      <param name="count">Die Anzahl der Elemente im zu durchsuchenden Abschnitt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> liegt außerhalb des Bereichs der gültigen Indizes für <see cref="T:System.Collections.Generic.List`1" />.- oder - <paramref name="count" /> ist kleiner als 0.- oder - <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Abschnitt in der <see cref="T:System.Collections.Generic.List`1" /> an. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich entfernt wurde; andernfalls false.Diese Methode gibt auch dann false zurück, wenn das <paramref name="item" /> nicht in der <see cref="T:System.Collections.Generic.List`1" /> gefunden wurde.</returns>
      <param name="item">Das aus der <see cref="T:System.Collections.Generic.List`1" /> zu entfernende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>Entfernt alle Elemente, die die vom angegebenen Prädikat definierten Bedingungen erfüllen.</summary>
      <returns>Die Anzahl der aus der <see cref="T:System.Collections.Generic.List`1" /> entfernten Elemente.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen für die Elemente definiert, die entfernt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>Entfernt das Element am angegebenen Index aus der <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Der nullbasierte Index des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="index" /> ist größer oder gleich <see cref="P:System.Collections.Generic.List`1.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>Entfernt einen Bereich von Elementen aus der <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <param name="index">Der nullbasierte Startindex des zu entfernenden Bereichs von Elementen.</param>
      <param name="count">Die Anzahl der zu entfernenden Elemente.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Bereich von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> an.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>Kehrt die Reihenfolge der Elemente in der gesamten <see cref="T:System.Collections.Generic.List`1" /> um.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>Kehrt die Reihenfolge der Elemente im angegebenen Bereich um.</summary>
      <param name="index">Der nullbasierte Startindex des Bereichs, in dem die Reihenfolge umgekehrt werden soll.</param>
      <param name="count">Die Anzahl der Elemente im Bereich, in dem die Reihenfolge umgekehrt werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Bereich von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> an. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>Sortiert die Elemente in der gesamten <see cref="T:System.Collections.Generic.List`1" /> mithilfe des Standardcomparers.</summary>
      <exception cref="T:System.InvalidOperationException">Der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für Typ <paramref name="T" /> finden.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>Sortiert die Elemente in der gesamten <see cref="T:System.Collections.Generic.List`1" /> mithilfe des angegebenen Comparers.</summary>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleichen von Elementen verwendet werden soll, oder null, wenn der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> verwendet werden soll.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> ist null, und der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für den Typ <paramref name="T" /> finden.</exception>
      <exception cref="T:System.ArgumentException">Die Implementierung von <paramref name="comparer" /> hat während der Sortierung einen Fehler verursacht.<paramref name="comparer" /> kann z. B. möglicherweise nicht 0 zurückgeben, wenn ein Element mit sich selbst verglichen wird.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>Sortiert die Elemente in der gesamten <see cref="T:System.Collections.Generic.List`1" /> mithilfe des angegebenen <see cref="T:System.Comparison`1" />.</summary>
      <param name="comparison">Die <see cref="T:System.Comparison`1" />, die beim Vergleich von Elementen verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Die Implementierung von <paramref name="comparison" /> hat während der Sortierung einen Fehler verursacht.<paramref name="comparison" /> kann z. B. möglicherweise nicht 0 zurückgeben, wenn ein Element mit sich selbst verglichen wird.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Sortiert die Elemente in einem Bereich von Elementen in der <see cref="T:System.Collections.Generic.List`1" /> mithilfe des angegebenen Vergleichs.</summary>
      <param name="index">Der nullbasierte Startindex des zu sortierenden Bereichs.</param>
      <param name="count">Die Länge des zu sortierenden Bereichs.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleichen von Elementen verwendet werden soll, oder null, wenn der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> verwendet werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> und <paramref name="count" /> geben keinen gültigen Bereich in der <see cref="T:System.Collections.Generic.List`1" /> an.- oder - Die Implementierung von <paramref name="comparer" /> hat während der Sortierung einen Fehler verursacht.<paramref name="comparer" /> kann z. B. möglicherweise nicht 0 zurückgeben, wenn ein Element mit sich selbst verglichen wird.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" /> ist null, und der Standardvergleich <see cref="P:System.Collections.Generic.Comparer`1.Default" /> kann keine Implementierung der generischen <see cref="T:System.IComparable`1" />-Schnittstelle oder der <see cref="T:System.IComparable" />-Schnittstelle für den Typ <paramref name="T" /> finden.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.List`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder - <paramref name="array" /> hat keine nullbasierte Indizierung.- oder - Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="arrayIndex" /> bis zum Ende des Ziel-<paramref name="array" />.- oder - Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.List`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.In der Standardimplementierung der <see cref="T:System.Collections.Generic.List`1" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>Fügt der <see cref="T:System.Collections.IList" /> ein Element hinzu.</summary>
      <returns>Die Position, an der das neue Element eingefügt wurde.</returns>
      <param name="item">Das <see cref="T:System.Object" />, das in <see cref="T:System.Collections.IList" /> eingefügt werden soll.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="item" /> weist einen Typ auf, der der <see cref="T:System.Collections.IList" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.IList" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn das <paramref name="item" /> in der <see cref="T:System.Collections.IList" /> gefunden wird, andernfalls false.</returns>
      <param name="item">Das <see cref="T:System.Object" />, das in der <see cref="T:System.Collections.IList" /> gesucht werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>Bestimmt den Index eines bestimmten Elements in der <see cref="T:System.Collections.IList" />.</summary>
      <returns>Der Index von <paramref name="item" />, wenn das Element in der Liste gefunden wird, andernfalls -1.</returns>
      <param name="item">Das im <see cref="T:System.Collections.IList" /> zu suchende Objekt.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="item" /> weist einen Typ auf, der der <see cref="T:System.Collections.IList" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>Fügt am angegebenen Index ein Element in die <see cref="T:System.Collections.IList" /> ein.</summary>
      <param name="index">Der nullbasierte Index, an dem <paramref name="item" /> eingefügt werden soll.</param>
      <param name="item">Das in die <see cref="T:System.Collections.IList" /> einzufügende Objekt.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kein gültiger Index in der <see cref="T:System.Collections.IList" />. </exception>
      <exception cref="T:System.ArgumentException">Der <paramref name="item" /> weist einen Typ auf, der der <see cref="T:System.Collections.IList" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IList" /> eine feste Größe aufweist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IList" /> eine feste Größe aufweist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.List`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IList" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IList" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.List`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>Ruft das Element am angegebenen Index ab oder legt dieses fest.</summary>
      <returns>Das Element am angegebenen Index.</returns>
      <param name="index">Der nullbasierte Index des Elements, das abgerufen oder festgelegt werden soll.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kein gültiger Index in der <see cref="T:System.Collections.IList" />.</exception>
      <exception cref="T:System.ArgumentException">Die Eigenschaft wird festgelegt, und der <paramref name="value" /> weist einen Typ auf, der der <see cref="T:System.Collections.IList" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.IList" />.</summary>
      <param name="item">Das aus der <see cref="T:System.Collections.IList" /> zu entfernende Objekt.</param>
      <exception cref="T:System.ArgumentException">Der <paramref name="item" /> weist einen Typ auf, der der <see cref="T:System.Collections.IList" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.Generic.List`1" /> in ein neues Array.</summary>
      <returns>Ein Array, das Kopien der Elemente aus <see cref="T:System.Collections.Generic.List`1" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>Legt die Kapazität auf die Anzahl der tatsächlich in der <see cref="T:System.Collections.Generic.List`1" /> befindlichen Elemente fest, sofern diese Anzahl unter dem Schwellenwert liegt.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>Bestimmt, ob jedes Element in der <see cref="T:System.Collections.Generic.List`1" /> die vom angegebenen Prädikat definierten Bedingungen erfüllt.</summary>
      <returns>true, wenn jedes Element in der <see cref="T:System.Collections.Generic.List`1" /> die vom angegebenen Prädikat definierten Bedingungen erfüllt; andernfalls false.Wenn die Liste über keine Elemente verfügt, ist der Rückgabewert true.</returns>
      <param name="match">Der <see cref="T:System.Predicate`1" />-Delegat, der die Bedingungen definiert, auf die die Elemente geprüft werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.List`1" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.List`1" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>Gibt sämtliche vom <see cref="T:System.Collections.Generic.List`1.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.List`1" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.List`1" /> an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>Stellt eine FIFO-Auflistung (First-In-First-Out) von Objekten dar.</summary>
      <typeparam name="T">Gibt den Typ der Elemente in der Warteschlange an.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Queue`1" />-Klasse, die die Standardanfangskapazität aufweist.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Queue`1" />-Klasse, die aus der angegebenen Auflistung kopierte Elemente enthält und eine ausreichende Kapazität für die Anzahl der kopierten Elemente aufweist.</summary>
      <param name="collection">Die Auflistung, deren Elemente in die neue <see cref="T:System.Collections.Generic.Queue`1" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Queue`1" />-Klasse, die die angegebene Anfangskapazität aufweist.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.Queue`1" /> enthalten kann.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>Entfernt alle Objekte aus dem <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>Bestimmt, ob sich ein Element in <see cref="T:System.Collections.Generic.Queue`1" /> befindet.</summary>
      <returns>true, wenn das <paramref name="item" /> in der <see cref="T:System.Collections.Generic.Queue`1" /> gefunden wird, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.Queue`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.Queue`1" />-Elemente in ein vorhandenes eindimensionales <see cref="T:System.Array" />, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.Queue`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in <see cref="T:System.Collections.Generic.Queue`1" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in <see cref="T:System.Collections.Generic.Queue`1" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>Entfernt das Objekt am Anfang von <see cref="T:System.Collections.Generic.Queue`1" /> und gibt es zurück.</summary>
      <returns>Das Objekt, das vom Anfang der <see cref="T:System.Collections.Generic.Queue`1" /> entfernt wurde.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>Fügt am Ende der <see cref="T:System.Collections.Generic.Queue`1" /> ein Objekt hinzu.</summary>
      <param name="item">Das Objekt, das <see cref="T:System.Collections.Generic.Queue`1" /> hinzugefügt werden soll.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.Queue`1" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> für das <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>Gibt das Objekt am Anfang von <see cref="T:System.Collections.Generic.Queue`1" /> zurück, ohne es zu entfernen.</summary>
      <returns>Das Objekt am Anfang der <see cref="T:System.Collections.Generic.Queue`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Queue`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.In der Standardimplementierung der <see cref="T:System.Collections.Generic.Queue`1" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.Queue`1" />-Elemente in ein neues Array.</summary>
      <returns>Ein neues Array mit Elementen, die aus <see cref="T:System.Collections.Generic.Queue`1" /> kopiert werden.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>Legt die Kapazität auf die Anzahl der tatsächlich im <see cref="T:System.Collections.Generic.Queue`1" /> befindlichen Elemente fest, sofern diese Anzahl unter 90 Prozent der aktuellen Kapazität liegt.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.Queue`1" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.Queue`1" /> an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>Gibt sämtliche vom <see cref="T:System.Collections.Generic.Queue`1.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.Queue`1" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>Stellt eine Auflistung von Schlüssel-Wert-Paaren dar, deren Reihenfolge anhand des Schlüssels bestimmt wird. </summary>
      <typeparam name="TKey">Der Typ der Schlüssel im Wörterbuch.</typeparam>
      <typeparam name="TValue">Der Typ der Werte im Wörterbuch.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2" />-Klasse, bei der die <see cref="T:System.Collections.Generic.IComparer`1" />-Standardimplementierung für den Typ des Schlüssels verwendet wird.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2" />-Klasse, bei der die angegebene <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung für den Vergleich von Schlüsseln verwendet wird.</summary>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die zum Vergleichen von Schlüsseln verwendet werden soll, oder null, wenn der Standard-<see cref="T:System.Collections.Generic.Comparer`1" /> für diesen Schlüsseltyp verwendet werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2" />-Klasse, die Elemente enthält, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopiert wurden, und bei der die <see cref="T:System.Collections.Generic.IComparer`1" />-Standardimplementierung für den Typ des Schlüssels verwendet wird.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.SortedDictionary`2" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2" />-Klasse, die Elemente enthält, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopiert wurden, und bei der die angegebene <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung für den Vergleich von Schlüsseln verwendet wird.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.SortedDictionary`2" /> kopiert werden.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die zum Vergleichen von Schlüsseln verwendet werden soll, oder null, wenn der Standard-<see cref="T:System.Collections.Generic.Comparer`1" /> für diesen Schlüsseltyp verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>Fügt dem <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Der Schlüssel des hinzuzufügenden Elements.</param>
      <param name="value">Der Wert des hinzuzufügenden Elements.Der Wert kann für Verweistypen null sein.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>Entfernt alle Elemente aus der <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>Ruft den <see cref="T:System.Collections.Generic.IComparer`1" /> ab, der zum Sortieren der Elemente des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> verwendet wird.</summary>
      <returns>Der <see cref="T:System.Collections.Generic.IComparer`1" />, der zum Sortieren der Elemente des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> verwendet wird</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Wert enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Wert enthält, andernfalls false.</returns>
      <param name="value">Der im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> zu suchende Wert.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Kopiert die Elemente des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> in das angegebene Array von <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Strukturen, wobei am angegebenen Index begonnen wird.</summary>
      <param name="array">Das eindimensionale Array von <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Strukturen, in das die Elemente aus der aktuellen <see cref="T:System.Collections.Generic.SortedDictionary`2" /> kopiert werden. Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.SortedDictionary`2" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>Ruft die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ab.</summary>
      <returns>Die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.SortedDictionary`2" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> für die <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>Ruft den Wert ab, der dem angegebenen Schlüssel zugeordnet ist, oder legt diesen fest.</summary>
      <returns>Der dem angegebenen Schlüssel zugeordnete Wert.Wenn der angegebene Schlüssel nicht gefunden wird, löst ein Get-Vorgang eine <see cref="T:System.Collections.Generic.KeyNotFoundException" /> aus, und durch einen Set-Vorgang wird ein neues Element mit dem angegebenen Schlüssel erstellt.</returns>
      <param name="key">Der Schlüssel des abzurufenden oder festzulegenden Werts.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Die Eigenschaft wird abgerufen, und der <paramref name="key" /> ist nicht in der Auflistung vorhanden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>Ruft eine Auflistung ab, die die Schlüssel im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />, die die Schlüssel im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true, wenn das Element erfolgreich entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="key" /> nicht in der <see cref="T:System.Collections.Generic.SortedDictionary`2" /> gefunden wurde.</returns>
      <param name="key">	Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.</summary>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die der <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> einen bestimmten Schlüssel und Wert enthält.</summary>
      <returns>true, wenn das <paramref name="keyValuePair" /> in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wird, andernfalls false.</returns>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die in die <see cref="T:System.Collections.Generic.ICollection`1" /> gesucht werden soll.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedDictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Entfernt das erste Vorkommen des angegebenen Elements aus der <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true, wenn <paramref name="keyValuePair" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn das <paramref name="keyValuePair" /> nicht in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="keyValuePair">Die <see cref="T:System.Collections.Generic.KeyValuePair`2" />-Struktur, die aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt werden soll.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" />, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" />, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine Auflistung mit den Schlüsseln in der<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Eine Auflistung mit den Schlüsseln in der<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ruft eine Auflistung mit den Werten in der<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>Eine Auflistung mit den Werten in der<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.Generic.ICollection`1" /> in ein Array, wobei am angegebenen Arrayindex begonnen wird.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus <see cref="T:System.Collections.Generic.ICollection`1" /> kopierten Elemente ist.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder - <paramref name="array" /> hat keine nullbasierte Indizierung.- oder - Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder - Der Typ der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedDictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Fügt der <see cref="T:System.Collections.IDictionary" />-Schnittstelle ein Element mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Das Objekt, das als Schlüssel des hinzuzufügenden Elements verwendet werden soll.</param>
      <param name="value">Das Objekt, das als Wert des hinzuzufügenden Elements verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> weist einen Typ auf, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.IDictionary" /> nicht zugeordnet werden kann.- oder - <paramref name="value" /> weist einen Typ auf, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.IDictionary" /> nicht zugeordnet werden kann.- oder - In <see cref="T:System.Collections.IDictionary" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.IDictionary" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Gibt einen <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedDictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedDictionary`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ruft das Element mit dem angegebenen Schlüssel ab oder legt dieses fest.</summary>
      <returns>Das Element mit dem angegebenen Schlüssel oder null, wenn <paramref name="key" /> nicht im Wörterbuch enthalten ist oder <paramref name="key" /> einen Typ aufweist, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> nicht zugeordnet werden kann.</returns>
      <param name="key">Der Schlüssel des abzurufenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Es wird ein Wert zugewiesen, und <paramref name="key" /> ist ein Typ, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> nicht zugeordnet werden kann.- oder - Es wird ein Wert zugewiesen, und <paramref name="value" /> ist ein Typ, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.Generic.SortedDictionary`2" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">	Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>Ruft den dem angegebenen Schlüssel zugeordneten Wert ab.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedDictionary`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der Schlüssel des abzurufenden Werts.</param>
      <param name="value">Wenn diese Methode zurückgegeben wird, enthält sie den dem angegebenen Schlüssel zugeordneten Wert, wenn der Schlüssel gefunden wird, andernfalls enthält sie den Standardwert für den Typ des <paramref name="value" />-Parameters. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>Ruft eine Auflistung ab, die die Werte im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />, die die Werte im <see cref="T:System.Collections.Generic.SortedDictionary`2" /> enthält.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.SortedDictionary`2" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.SortedDictionary`2" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>Ruft das Element an der aktuellen Position des Enumerators als <see cref="T:System.Collections.DictionaryEntry" />-Struktur ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Wörterbuches als <see cref="T:System.Collections.DictionaryEntry" />-Struktur.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>Ruft den Schlüssel des Elements an der aktuellen Position des Enumerators ab.</summary>
      <returns>Der Schlüssel des Elements in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>Ruft den Wert des Elements an der aktuellen Position des Enumerators ab.</summary>
      <returns>Der Wert des Elements in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>Stellt die Auflistung von Schlüsseln in einem <see cref="T:System.Collections.Generic.SortedDictionary`2" /> dar.Diese Klasse kann nicht vererbt werden.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />-Klasse, die die Schlüssel im angegebenen <see cref="T:System.Collections.Generic.SortedDictionary`2" /> angibt.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.SortedDictionary`2" />, dessen Schlüssel in der neuen <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> angegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />-Elemente in ein vorhandenes eindimensionales Array, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> kopierten Elemente ist.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> ist größer als der verfügbare Platz von <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> durchläuft.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />-Struktur für das <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="item">Das Objekt, das <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Entfernt alle Elemente aus <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>Bestimmt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> den angegebenen Wert enthält.</summary>
      <returns>true, wenn sich <paramref name="item" /> in <see cref="T:System.Collections.Generic.ICollection`1" /> befindet, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.ICollection`1" /> zu suchende Objekt.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wird, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="item" /> nicht in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="item">Das aus dem <see cref="T:System.Collections.Generic.ICollection`1" /> zu entfernende Objekt.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein Array, wobei an einem bestimmten Arrayindex begonnen wird.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder -<paramref name="array" /> hat keine nullbasierte Indizierung.- oder -Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder -Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem eine Auflistung durchlaufen werden kann.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>Stellt die Auflistung von Werten in einem <see cref="T:System.Collections.Generic.SortedDictionary`2" /> dar.Diese Klasse kann nicht geerbt werden.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />-Klasse, die die Werte im angegebenen <see cref="T:System.Collections.Generic.SortedDictionary`2" /> angibt.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.SortedDictionary`2" />, dessen Werte in der neuen <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> angegeben werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>Kopiert die <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />-Elemente in ein vorhandenes eindimensionales Array, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> kopierten Elemente ist.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> ist größer als der verfügbare Platz von <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> durchläuft.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />-Struktur für das <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Element hinzu.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="item">Das Objekt, das <see cref="T:System.Collections.Generic.ICollection`1" /> hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>Entfernt alle Elemente aus <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.ICollection`1" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn sich <paramref name="item" /> in <see cref="T:System.Collections.Generic.ICollection`1" /> befindet, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.ICollection`1" /> zu suchende Objekt.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wird, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="item" /> nicht in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="item">Das aus dem <see cref="T:System.Collections.Generic.ICollection`1" /> zu entfernende Objekt.</param>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Entfernt das erste Vorkommen eines angegebenen Objekts aus der <see cref="T:System.Collections.Generic.ICollection`1" />.  Diese Implementierung löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>true, wenn das <paramref name="item" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wird, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="item" /> nicht in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <exception cref="T:System.NotSupportedException">Wird immer ausgelöst, die Auflistung ist schreibgeschützt.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein Array, wobei an einem bestimmten Arrayindex begonnen wird.</summary>
      <param name="array">Das eindimensionale Array, das das Ziel der aus <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder -<paramref name="array" /> hat keine nullbasierte Indizierung.- oder -Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="index" /> bis zum Ende des Ziel-<paramref name="array" />.- oder -Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.  In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, mit dem eine Auflistung durchlaufen werden kann.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>Listet die Elemente einer <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>Stellt eine Auflistung von Schlüssel-Wert-Paaren dar, die auf Grundlage der zugeordneten <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung nach den Schlüsseln sortiert sind. </summary>
      <typeparam name="TKey">Der Typ der Schlüssel in der Auflistung.</typeparam>
      <typeparam name="TValue">Der Typ der Werte in der Auflistung.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse mit der Standardanfangskapazität und dem Standard-<see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse mit der Standardanfangskapazität und dem angegebenen <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleich von Schlüsseln verwendet wird.- oder - null, um den Standard-<see cref="T:System.Collections.Generic.Comparer`1" /> für den Schlüsseltyp zu verwenden.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopierte Elemente enthält, über eine der Anzahl der kopierten Elemente entsprechende Kapazität verfügt sowie den Standard-<see cref="T:System.Collections.Generic.IComparer`1" /> verwendet.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.SortedList`2" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse, die aus dem angegebenen <see cref="T:System.Collections.Generic.IDictionary`2" /> kopierte Elemente enthält, über eine der Anzahl der kopierten Elemente entsprechende Kapazität verfügt und den angegebenen <see cref="T:System.Collections.Generic.IComparer`1" /> verwendet.</summary>
      <param name="dictionary">Das <see cref="T:System.Collections.Generic.IDictionary`2" />, dessen Elemente in das neue <see cref="T:System.Collections.Generic.SortedList`2" /> kopiert werden.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleich von Schlüsseln verwendet wird.- oder - null, um den Standard-<see cref="T:System.Collections.Generic.Comparer`1" /> für den Schlüsseltyp zu verwenden.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" /> enthält mindestens einen doppelten Schlüssel.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse mit der angegebenen anfänglichen Kapazität und dem Standard-<see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.SortedList`2" /> enthalten kann.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist kleiner als Null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.SortedList`2" />-Klasse mit der angegebenen Anfangskapazität und dem angegebenen <see cref="T:System.Collections.Generic.IComparer`1" />.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.SortedList`2" /> enthalten kann.</param>
      <param name="comparer">Die <see cref="T:System.Collections.Generic.IComparer`1" />-Implementierung, die beim Vergleich von Schlüsseln verwendet wird.- oder - null, um den Standard-<see cref="T:System.Collections.Generic.Comparer`1" /> für den Schlüsseltyp zu verwenden.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> ist kleiner als Null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>Fügt dem <see cref="T:System.Collections.Generic.SortedList`2" /> ein Element mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Der Schlüssel des hinzuzufügenden Elements.</param>
      <param name="value">Der Wert des hinzuzufügenden Elements.Der Wert kann für Verweistypen null sein.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">In <see cref="T:System.Collections.Generic.SortedList`2" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>Ruft die Anzahl der Elemente ab, die die <see cref="T:System.Collections.Generic.SortedList`2" /> enthalten kann, oder legt diese fest.</summary>
      <returns>Die anfängliche Anzahl von Elementen, die die <see cref="T:System.Collections.Generic.SortedList`2" /> enthalten kann.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" /> ist auf einen Wert festgelegt, der kleiner ist als <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
      <exception cref="T:System.OutOfMemoryException">Es ist nicht genügend Arbeitsspeicher im System verfügbar.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>Entfernt alle Elemente aus der <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>Ruft den <see cref="T:System.Collections.Generic.IComparer`1" /> für die sortierte Liste ab. </summary>
      <returns>Der <see cref="T:System.IComparable`1" /> für die aktuelle <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>Stellt fest, ob der <see cref="T:System.Collections.Generic.SortedList`2" /> einen bestimmten Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedList`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.Generic.SortedList`2" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>Ermittelt, ob die <see cref="T:System.Collections.Generic.SortedList`2" /> einen bestimmten Wert enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedList`2" /> ein Element mit dem angegebenen Wert enthält, andernfalls false.</returns>
      <param name="value">Der im <see cref="T:System.Collections.Generic.SortedList`2" /> zu suchende Wert.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>Ruft die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.SortedList`2" /> ab.</summary>
      <returns>Die Anzahl der Schlüssel-Wert-Paare im <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.SortedList`2" /> durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" /> vom Typ <see cref="T:System.Collections.Generic.KeyValuePair`2" /> für die <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>Sucht den angegebenen Schlüssel und gibt den nullbasierten Index innerhalb der gesamten <see cref="T:System.Collections.Generic.SortedList`2" /> zurück.</summary>
      <returns>Der nullbasierte Index des <paramref name="key" /> innerhalb der gesamten <see cref="T:System.Collections.Generic.SortedList`2" />, sofern er gefunden wird, andernfalls -1.</returns>
      <param name="key">Der im <see cref="T:System.Collections.Generic.SortedList`2" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>Sucht den angegebenen Wert und gibt den nullbasierten Index des ersten Vorkommens innerhalb der gesamten <see cref="T:System.Collections.Generic.SortedList`2" /> zurück.</summary>
      <returns>Der nullbasierte Index des ggf. ersten Vorkommens von <paramref name="value" /> in der gesamten <see cref="T:System.Collections.Generic.SortedList`2" />, andernfalls -1.</returns>
      <param name="value">Der im <see cref="T:System.Collections.Generic.SortedList`2" /> zu suchende Wert.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>Ruft den Wert ab, der dem angegebenen Schlüssel zugeordnet ist, oder legt diesen fest.</summary>
      <returns>Der dem angegebenen Schlüssel zugeordnete Wert.Wenn der angegebene Schlüssel nicht gefunden wird, löst ein Get-Vorgang eine <see cref="T:System.Collections.Generic.KeyNotFoundException" /> aus, und durch einen Set-Vorgang wird ein neues Element mit dem angegebenen Schlüssel erstellt.</returns>
      <param name="key">Der Schlüssel, dessen Wert abgerufen oder festgelegt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">Die Eigenschaft wird abgerufen, und der <paramref name="key" /> ist nicht in der Auflistung vorhanden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>Ruft eine Auflistung mit den Schlüsseln in der <see cref="T:System.Collections.Generic.SortedList`2" /> in sortierter Reihenfolge auf.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.IList`1" />, die die Schlüssel im <see cref="T:System.Collections.Generic.SortedList`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <returns>true, wenn das Element erfolgreich entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="key" /> nicht im ursprünglichen <see cref="T:System.Collections.Generic.SortedList`2" /> gefunden wurde.</returns>
      <param name="key">	Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>Entfernt das Element am angegebenen Index aus der <see cref="T:System.Collections.Generic.SortedList`2" />.</summary>
      <param name="index">Der nullbasierte Index des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als Null.- oder - <paramref name="index" /> ist größer oder gleich <see cref="P:System.Collections.Generic.SortedList`2.Count" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Fügt der <see cref="T:System.Collections.Generic.ICollection`1" /> ein Schlüssel-Wert-Paar hinzu.</summary>
      <param name="keyValuePair">Das <see cref="T:System.Collections.Generic.KeyValuePair`2" />, das in <see cref="T:System.Collections.Generic.ICollection`1" /> eingefügt werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Ermittelt, ob <see cref="T:System.Collections.Generic.ICollection`1" /> ein bestimmtes Element enthält.</summary>
      <returns>true, wenn das <paramref name="keyValuePair" /> in der <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wird, andernfalls false.</returns>
      <param name="keyValuePair">Das <see cref="T:System.Collections.Generic.KeyValuePair`2" />, das in der <see cref="T:System.Collections.Generic.ICollection`1" /> gesucht werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.Generic.ICollection`1" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.ICollection`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als Null. </exception>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.Generic.ICollection`1" /> überschreitet den verfügbaren Platz vom <paramref name="arrayIndex" /> bis zum Ende des Ziel-<paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.ICollection`1" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedList`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>Entfernt das erste Vorkommen eines bestimmten Schlüssel-Wert-Paares aus der <see cref="T:System.Collections.Generic.ICollection`1" />.</summary>
      <returns>true, wenn <paramref name="keyValuePair" /> erfolgreich aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt wurde, andernfalls false.Diese Methode gibt auch dann false zurück, wenn <paramref name="keyValuePair" /> nicht im ursprünglichen <see cref="T:System.Collections.Generic.ICollection`1" /> gefunden wurde.</returns>
      <param name="keyValuePair">Der <see cref="T:System.Collections.Generic.KeyValuePair`2" />, der aus der <see cref="T:System.Collections.Generic.ICollection`1" /> entfernt werden soll.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" />, die die Schlüssel des <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>Ruft eine <see cref="T:System.Collections.Generic.ICollection`1" /> ab, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.ICollection`1" />, die die Werte im <see cref="T:System.Collections.Generic.IDictionary`2" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>Ruft eine aufzählbare Auflistung ab, die die Schlüssel im schreibgeschützten Wörterbuch enthält.</summary>
      <returns>Eine aufzählbare Sammlung, die die Schlüssel im schreibgeschützten Wörterbuch enthält.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>Ruft eine aufzählbare Auflistung ab, die die Werte im schreibgeschützten Wörterbuch enthält.</summary>
      <returns>Eine aufzählbare Sammlung, die die Werte im schreibgeschützten Wörterbuch enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> ist kleiner als Null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> ist mehrdimensional.- oder - <paramref name="array" /> hat keine nullbasierte Indizierung.- oder - Die Anzahl der Elemente in der Quell-<see cref="T:System.Collections.ICollection" /> überschreitet den verfügbaren Platz vom <paramref name="arrayIndex" /> bis zum Ende des Ziel-<paramref name="array" />.- oder - Der Typ der Quell-<see cref="T:System.Collections.ICollection" /> kann nicht automatisch in den Typ des Ziel-<paramref name="array" /> umgewandelt werden.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedList`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.In der Standardimplementierung der <see cref="T:System.Collections.Generic.SortedList`2" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>Fügt der <see cref="T:System.Collections.IDictionary" />-Schnittstelle ein Element mit dem angegebenen Schlüssel und Wert hinzu.</summary>
      <param name="key">Das <see cref="T:System.Object" />, das als Schlüssel für das hinzuzufügende Element verwendet werden soll.</param>
      <param name="value">Das <see cref="T:System.Object" />, das als Wert für das hinzuzufügende Element verwendet werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> weist einen Typ auf, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.IDictionary" /> nicht zugeordnet werden kann.- oder - <paramref name="value" /> weist einen Typ auf, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.IDictionary" /> nicht zugeordnet werden kann.- oder - In <see cref="T:System.Collections.IDictionary" /> ist bereits ein Element mit demselben Schlüssel enthalten.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>Ermittelt, ob das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem angegebenen Schlüssel enthält.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> ein Element mit dem Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der im <see cref="T:System.Collections.IDictionary" /> zu suchende Schlüssel.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>Gibt einen <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.IDictionaryEnumerator" /> für das <see cref="T:System.Collections.IDictionary" />.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> eine feste Größe aufweist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedList`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.IDictionary" /> schreibgeschützt ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.SortedList`2" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>Ruft das Element mit dem angegebenen Schlüssel ab oder legt dieses fest.</summary>
      <returns>Das Element mit dem angegebenen Schlüssel oder null, wenn <paramref name="key" /> nicht im Wörterbuch enthalten ist oder <paramref name="key" /> einen Typ aufweist, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.SortedList`2" /> nicht zugeordnet werden kann.</returns>
      <param name="key">Der Schlüssel des Elements, das abgerufen oder festgelegt werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Es wird ein Wert zugewiesen, und <paramref name="key" /> ist ein Typ, der dem Schlüsseltyp <paramref name="TKey" /> des <see cref="T:System.Collections.Generic.SortedList`2" /> nicht zugeordnet werden kann.- oder - Es wird ein Wert zugewiesen, und <paramref name="value" /> ist ein Typ, der dem Werttyp <paramref name="TValue" /> des <see cref="T:System.Collections.Generic.SortedList`2" /> nicht zugeordnet werden kann.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Schlüssel des <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>Entfernt das Element mit dem angegebenen Schlüssel aus dem <see cref="T:System.Collections.IDictionary" />.</summary>
      <param name="key">	Der Schlüssel des zu entfernenden Elements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>Ruft eine <see cref="T:System.Collections.ICollection" /> ab, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.ICollection" />, die die Werte im <see cref="T:System.Collections.IDictionary" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>Legt die Kapazität auf die Anzahl der tatsächlich im <see cref="T:System.Collections.Generic.SortedList`2" /> befindlichen Elemente fest, sofern diese Anzahl unter 90 Prozent der aktuellen Kapazität liegt.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>Ruft den dem angegebenen Schlüssel zugeordneten Wert ab.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedList`2" /> ein Element mit dem angegebenen Schlüssel enthält, andernfalls false.</returns>
      <param name="key">Der Schlüssel, dessen Wert abgerufen werden soll.</param>
      <param name="value">Wenn diese Methode zurückgegeben wird, enthält sie den dem angegebenen Schlüssel zugeordneten Wert, wenn der Schlüssel gefunden wird, andernfalls enthält sie den Standardwert für den Typ des <paramref name="value" />-Parameters.Dieser Parameter wird nicht initialisiert übergeben.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>Ruft eine Auflistung ab, die die Werte im <see cref="T:System.Collections.Generic.SortedList`2" /> enthält.</summary>
      <returns>Eine <see cref="T:System.Collections.Generic.IList`1" />, die die Werte im <see cref="T:System.Collections.Generic.SortedList`2" /> enthält.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>Stellt eine Auflistung von Objekten dar, deren Sortierreihenfolge beibehalten wird.</summary>
      <typeparam name="T">Der Typ der Elemente in der Gruppe.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>Initialisiert eine neue Instanz der<see cref="T:System.Collections.Generic.SortedSet`1" />-Klasse. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedSet`1" />-Klasse, die den angegebenen Vergleich verwendet.</summary>
      <param name="comparer">Der zum Vergleichen von Objekten zu verwendende Standardvergleich. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedSet`1" />-Klasse, die Elemente enthält, die aus einer angegebenen zählbaren Auflistung kopiert wurden.</summary>
      <param name="collection">Die zu kopierende zählbare Auflistung. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.SortedSet`1" />-Klasse, die aus einer angegebenen zählbaren Auflistung kopierte Elemente enthält und einen angegebenen Vergleich verwendet.</summary>
      <param name="collection">Die zu kopierende zählbare Auflistung. </param>
      <param name="comparer">Der zum Vergleichen von Objekten zu verwendende Standardvergleich. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>Fügt der Gruppe ein Element hinzu und gibt einen Wert zurück, der angibt, ob es erfolgreich hinzugefügt wurde.</summary>
      <returns>true, wenn der Gruppe <paramref name="item" /> hinzugefügt wurde, andernfalls false. </returns>
      <param name="item">Der Element, das zur Menge hinzugefügt wird.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>Entfernt alle Elemente aus dem Satz.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>Ruft das <see cref="T:System.Collections.Generic.IEqualityComparer`1" />-Objekt ab, mit dem die Gleichheit der Werte im <see cref="T:System.Collections.Generic.SortedSet`1" /> bestimmt wird.</summary>
      <returns>Der Vergleich, mit dem die Gleichheit der Werte im <see cref="T:System.Collections.Generic.SortedSet`1" /> bestimmt wird.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>Ermittelt, ob die Gruppe ein bestimmtes Element enthält.</summary>
      <returns>true, wenn die Gruppe <paramref name="item" /> enthält, andernfalls false.</returns>
      <param name="item">Das Element, das in der Gruppe gesucht werden soll.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>Kopiert das gesamte <see cref="T:System.Collections.Generic.SortedSet`1" /> in ein kompatibles eindimensionales Array, wobei am Anfang des Zielarrays begonnen wird.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus <see cref="T:System.Collections.Generic.SortedSet`1" /> kopiert werden.</param>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente im Quell-<see cref="T:System.Collections.Generic.SortedSet`1" /> ist größer als die Anzahl der Elemente, die das Zielarray enthalten kann. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert das vollständige <see cref="T:System.Collections.Generic.SortedSet`1" /> in ein kompatibles eindimensionales Array, beginnend am angegebenen Index des Arrays.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus <see cref="T:System.Collections.Generic.SortedSet`1" /> kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente im Quellarray ist größer als der verfügbare Platz vom <paramref name="index" /> bis zum Ende des Zielarrays.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>Kopiert eine angegebene Anzahl von Elementen aus <see cref="T:System.Collections.Generic.SortedSet`1" /> in ein kompatibles eindimensionales Array, beginnend ab dem angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus <see cref="T:System.Collections.Generic.SortedSet`1" /> kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <param name="count">Die Anzahl der zu kopierenden Elemente.</param>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente im Quellarray ist größer als der verfügbare Platz vom <paramref name="index" /> bis zum Ende des Zielarrays.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.- oder - <paramref name="count" /> ist kleiner als 0.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>Ruft die Anzahl der Elemente im <see cref="T:System.Collections.Generic.SortedSet`1" /> ab.</summary>
      <returns>Die Anzahl der Elemente im <see cref="T:System.Collections.Generic.SortedSet`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Entfernt alle Elemente in einer angegebenen Auflistung aus dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt.</summary>
      <param name="other">Die Auflistung der Elemente, die aus dem <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt entfernt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die <see cref="T:System.Collections.Generic.SortedSet`1" /> durchläuft.</summary>
      <returns>Ein Enumerator, der <see cref="T:System.Collections.Generic.SortedSet`1" /> in sortierter Reihenfolge durchläuft.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>Gibt eine Ansicht einer Teilmenge in einem <see cref="T:System.Collections.Generic.SortedSet`1" /> zurück.</summary>
      <returns>Eine Teilmengenansicht, die nur die Werte im angegebenen Bereich enthält.</returns>
      <param name="lowerValue">Der niedrigste gewünschte Wert in der Ansicht.</param>
      <param name="upperValue">Der höchste gewünschte Wert in der Ansicht. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="lowerValue" /> ist laut Vergleich größer als <paramref name="upperValue" />.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">Ein Vorgang, der ausgeführt werden sollte, befand sich außerhalb des von <paramref name="lowerValue" /> und <paramref name="upperValue" /> angegebenen Bereichs.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt, sodass es nur Elemente enthält, die in einer angegebenen Auflistung ebenfalls enthalten sind.</summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine echte Teilmenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine echte Teilmenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine echte Obermenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine echte Obermenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine Teilmenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine Teilmenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob ein <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine Obermenge der angegebenen Auflistung ist.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt eine Obermenge von <paramref name="other" /> ist, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>Ruft den durch den Vergleich definierten Höchstwert im <see cref="T:System.Collections.Generic.SortedSet`1" /> ab.</summary>
      <returns>Der Höchstwert in der Gruppe.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>Ruft den durch den Vergleich definierten Mindestwert im <see cref="T:System.Collections.Generic.SortedSet`1" /> ab.</summary>
      <returns>Der Mindestwert in der Gruppe.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt und eine angegebene Auflistung gemeinsame Elemente enthalten.</summary>
      <returns>true, wenn das <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt und <paramref name="other" /> mindestens ein gemeinsames Element enthalten, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>Entfernt ein angegebenes Element aus dem <see cref="T:System.Collections.Generic.SortedSet`1" />.</summary>
      <returns>true, wenn das Element gefunden und erfolgreich entfernt wurde, andernfalls false. </returns>
      <param name="item">Das zu entfernende Element.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>Entfernt alle Elemente aus einer <see cref="T:System.Collections.Generic.SortedSet`1" />-Auflistung, die die vom angegebenen Prädikat definierten Bedingungen erfüllen.</summary>
      <returns>Die Anzahl von Elementen, die aus der <see cref="T:System.Collections.Generic.SortedSet`1" />-Auflistung entfernt wurden. </returns>
      <param name="match">Der Delegat, der die Bedingungen für die Elemente definiert, die entfernt werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>Gibt ein <see cref="T:System.Collections.Generic.IEnumerable`1" /> zurück, das das <see cref="T:System.Collections.Generic.SortedSet`1" /> in umgekehrter Reihenfolge durchläuft.</summary>
      <returns>Ein Enumerator, der das <see cref="T:System.Collections.Generic.SortedSet`1" /> in umgekehrter Reihenfolge durchläuft.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>Bestimmt, ob das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt und die angegebene Auflistung dieselben Elemente enthalten.</summary>
      <returns>true, wenn das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt und <paramref name="other" /> gleich sind, andernfalls false.</returns>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt, sodass es nur Elemente enthält, die entweder im aktuellen Objekt oder in der angegebenen Auflistung, nicht jedoch in beiden vorhanden sind.</summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>Fügt einem <see cref="T:System.Collections.Generic.ICollection`1" />-Objekt ein Element hinzu.</summary>
      <param name="item">Das Objekt, das dem <see cref="T:System.Collections.Generic.ICollection`1" />-Objekt hinzugefügt werden soll.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" /> ist schreibgeschützt.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>Ruft einen Wert ab, der angibt, ob eine <see cref="T:System.Collections.ICollection" /> schreibgeschützt ist.</summary>
      <returns>true, wenn die Auflistung schreibgeschützt ist, andernfalls false.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein Enumerator, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert das vollständige <see cref="T:System.Collections.Generic.SortedSet`1" /> in ein kompatibles eindimensionales Array, beginnend am angegebenen Index des Arrays.</summary>
      <param name="array">Das eindimensionale Array, in das die Elemente aus <see cref="T:System.Collections.Generic.SortedSet`1" /> kopiert werden.Für das Array muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="index">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentException">Die Anzahl der Elemente im Quellarray ist größer als der verfügbare Platz vom <paramref name="index" /> bis zum Ende des Zielarrays. </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> ist kleiner als 0.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert ist, andernfalls false.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.In der Standardimplementierung der <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein Enumerator, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>Ändert das aktuelle <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt, sodass es alle Elemente enthält, die sowohl im aktuellen Objekt als auch in der angegebenen Auflistung vorhanden sind. </summary>
      <param name="other">Die Auflistung, die mit dem aktuellen <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekt verglichen werden soll.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" /> ist null.</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>Listet die Elemente eines <see cref="T:System.Collections.Generic.SortedSet`1" />-Objekts auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>Gibt alle vom <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" /> verwendeten Ressourcen frei. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.SortedSet`1" />-Auflistung.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>Stellt eine LIFO (Last-In-First-Out)-Auflistung variabler Größe von Instanzen desselben angegebenen Typs dar.</summary>
      <typeparam name="T">Gibt den Typ der Elemente im Stapel an.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Stack`1" />-Klasse, die die Standardanfangskapazität aufweist.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.Collections.Generic.Stack`1" />-Klasse, die aus der angegebenen Auflistung kopierte Elemente enthält und eine ausreichende Kapazität für die Anzahl der kopierten Elemente aufweist.</summary>
      <param name="collection">Die Auflistung, aus der Elemente kopiert werden sollen.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>Initialisiert eine neue, leere Instanz der <see cref="T:System.Collections.Generic.Stack`1" />-Klasse, die über die angegebene Anfangskapazität, mindestens aber über die anfängliche Standardkapazität verfügt.</summary>
      <param name="capacity">Die anfängliche Anzahl von Elementen, die das <see cref="T:System.Collections.Generic.Stack`1" /> enthalten kann.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>Entfernt alle Objekte aus dem <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>Bestimmt, ob sich ein Element in <see cref="T:System.Collections.Generic.Stack`1" /> befindet.</summary>
      <returns>true, wenn das <paramref name="item" /> in der <see cref="T:System.Collections.Generic.Stack`1" /> gefunden wird, andernfalls false.</returns>
      <param name="item">Das im <see cref="T:System.Collections.Generic.Stack`1" /> zu suchende Objekt.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>Kopiert den <see cref="T:System.Collections.Generic.Stack`1" /> in ein vorhandenes eindimensionales <see cref="T:System.Array" />, beginnend beim angegebenen Arrayindex.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.Generic.Stack`1" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>Ruft die Anzahl der Elemente ab, die in <see cref="T:System.Collections.Generic.Stack`1" /> enthalten sind.</summary>
      <returns>Die Anzahl der Elemente, die in <see cref="T:System.Collections.Generic.Stack`1" /> enthalten sind.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>Gibt einen Enumerator für den <see cref="T:System.Collections.Generic.Stack`1" /> zurück.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> für das <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>Gibt das Objekt oben im <see cref="T:System.Collections.Generic.Stack`1" /> zurück, ohne es zu entfernen.</summary>
      <returns>Das oberste Objekt im <see cref="T:System.Collections.Generic.Stack`1" />.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>Entfernt das Objekt oben im <see cref="T:System.Collections.Generic.Stack`1" /> und gibt es zurück.</summary>
      <returns>Das Objekt, das von der obersten Position des <see cref="T:System.Collections.Generic.Stack`1" /> entfernt wurde.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>Fügt ein Objekt am Anfang von <see cref="T:System.Collections.Generic.Stack`1" /> ein.</summary>
      <param name="item">Das Objekt, das auf dem <see cref="T:System.Collections.Generic.Stack`1" /> abgelegt werden soll.Der Wert kann für Verweistypen null sein.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der die Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.Generic.IEnumerator`1" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>Kopiert die Elemente der <see cref="T:System.Collections.ICollection" /> in ein <see cref="T:System.Array" />, beginnend bei einem bestimmten <see cref="T:System.Array" />-Index.</summary>
      <param name="array">Das eindimensionale <see cref="T:System.Array" />, das das Ziel der aus der <see cref="T:System.Collections.ICollection" /> kopierten Elemente ist.Für das <see cref="T:System.Array" /> muss eine nullbasierte Indizierung verwendet werden.</param>
      <param name="arrayIndex">Der nullbasierte Index im <paramref name="array" />, bei dem der Kopiervorgang beginnt.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>Ruft einen Wert ab, der angibt, ob der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist.</summary>
      <returns>true, wenn der Zugriff auf das <see cref="T:System.Collections.ICollection" /> synchronisiert (threadsicher) ist, andernfalls false.In der Standardimplementierung von <see cref="T:System.Collections.Generic.Stack`1" /> gibt diese Eigenschaft immer false zurück.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>Ruft ein Objekt ab, mit dem der Zugriff auf <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.</summary>
      <returns>Ein Objekt, mit dem der Zugriff auf die <see cref="T:System.Collections.ICollection" /> synchronisiert werden kann.In der Standardimplementierung der <see cref="T:System.Collections.Generic.Stack`1" /> gibt diese Eigenschaft immer die aktuelle Instanz zurück.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>Gibt einen Enumerator zurück, der eine Auflistung durchläuft.</summary>
      <returns>Ein <see cref="T:System.Collections.IEnumerator" />, der zum Durchlaufen der Auflistung verwendet werden kann.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>Kopiert den <see cref="T:System.Collections.Generic.Stack`1" /> in ein neues Array.</summary>
      <returns>Ein neues Array, das Kopien der Elemente von <see cref="T:System.Collections.Generic.Stack`1" /> enthält.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>Legt die Kapazität auf die Anzahl der tatsächlich im <see cref="T:System.Collections.Generic.Stack`1" /> befindlichen Elemente fest, sofern diese Anzahl unter 90 Prozent der aktuellen Kapazität liegt.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>Listet die Elemente eines <see cref="T:System.Collections.Generic.Stack`1" /> auf.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element im <see cref="T:System.Collections.Generic.Stack`1" /> an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>Gibt sämtliche vom <see cref="T:System.Collections.Generic.Stack`1.Enumerator" /> verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>Setzt den Enumerator auf das nächste Element der <see cref="T:System.Collections.Generic.Stack`1" />.</summary>
      <returns>true, wenn der Enumerator erfolgreich auf das nächste Element gesetzt wurde, false, wenn der Enumerator das Ende der Auflistung überschritten hat.</returns>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>Ruft das Element an der aktuellen Position des Enumerators ab.</summary>
      <returns>Das Element in der Auflistung an der aktuellen Position des Enumerators.</returns>
      <exception cref="T:System.InvalidOperationException">Der Enumerator ist vor dem ersten Element oder hinter dem letzten Element der Auflistung positioniert. </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>Setzt den Enumerator auf seine anfängliche Position vor dem ersten Element in der Auflistung.Diese Klasse kann nicht vererbt werden.</summary>
      <exception cref="T:System.InvalidOperationException">Die Auflistung wurde nach dem Erstellen des Enumerators geändert. </exception>
    </member>
  </members>
</doc>