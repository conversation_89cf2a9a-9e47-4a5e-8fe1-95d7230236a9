﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Collections</name>
  </assembly>
  <members>
    <member name="T:System.Collections.BitArray">
      <summary>부울로 나타나는 간단한 비트 값 배열을 관리합니다. 여기에서 true는 비트가 설정(1)되었고 false는 비트가 해제(0)되었음을 나타냅니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Boolean[])">
      <summary>지정한 부울 배열에서 복사된 비트 값을 포함하는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="values">복사할 부울 배열입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Byte[])">
      <summary>지정한 바이트 배열에서 복사된 비트 값을 포함하는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="bytes">복사할 값을 포함하는 바이트 배열로, 각 바이트는 8개의 연속 비트를 나타냅니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bytes" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="bytes" /> is greater than <see cref="F:System.Int32.MaxValue" />.</exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Collections.BitArray)">
      <summary>지정한 <see cref="T:System.Collections.BitArray" />에서 복사된 비트 값을 포함하는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="bits">복사할 <see cref="T:System.Collections.BitArray" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="bits" /> is null. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32)">
      <summary>지정한 비트 값을 보유할 수 있는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다. 이러한 값은 초기에 false로 설정됩니다.</summary>
      <param name="length">새 <see cref="T:System.Collections.BitArray" />에 있는 비트 값입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32,System.Boolean)">
      <summary>지정한 비트 값을 보유할 수 있는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다. 이러한 값은 초기에 지정된 값으로 설정됩니다.</summary>
      <param name="length">새 <see cref="T:System.Collections.BitArray" />에 있는 비트 값입니다. </param>
      <param name="defaultValue">각 비트에 할당될 부울 값입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="length" /> is less than zero. </exception>
    </member>
    <member name="M:System.Collections.BitArray.#ctor(System.Int32[])">
      <summary>지정한 32비트 정수 배열에서 복사된 비트 값을 포함하는 <see cref="T:System.Collections.BitArray" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="values">복사할 값을 포함하는 정수 배열로, 각 정수는 32개의 연속 비트를 나타냅니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="values" /> is null. </exception>
      <exception cref="T:System.ArgumentException">The length of <paramref name="values" /> is greater than <see cref="F:System.Int32.MaxValue" /></exception>
    </member>
    <member name="M:System.Collections.BitArray.And(System.Collections.BitArray)">
      <summary>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대해 비트 AND 연산을 수행합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대한 비트 AND 연산 결과가 포함된 현재 인스턴스입니다.</returns>
      <param name="value">비트 AND 연산이 수행될 <see cref="T:System.Collections.BitArray" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Get(System.Int32)">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 특정 위치에서 비트 값을 가져옵니다.</summary>
      <returns>
        <paramref name="index" /> 위치의 비트 값입니다.</returns>
      <param name="index">가져올 값의 0부터 시작하는 인덱스입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.BitArray" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>전체 <see cref="T:System.Collections.BitArray" />의 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Item(System.Int32)">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 특정 위치에서 비트 값을 가져오거나 설정합니다.</summary>
      <returns>
        <paramref name="index" /> 위치의 비트 값입니다.</returns>
      <param name="index">가져오거나 설정할 값의 0부터 시작하는 인덱스입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is equal to or greater than <see cref="P:System.Collections.BitArray.Count" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.BitArray.Length">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 요소 수를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" />의 요소 수입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">The property is set to a value that is less than zero. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Not">
      <summary>true로 설정된 요소는 false로 변경되고 false로 설정된 요소는 true로 변경되도록 현재 <see cref="T:System.Collections.BitArray" />에 있는 모든 비트 값을 반전합니다.</summary>
      <returns>반전된 비트 값을 가지는 현재 인스턴스입니다.</returns>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Or(System.Collections.BitArray)">
      <summary>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대한 비트 OR 연산을 수행합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대한 비트 OR 연산 결과가 포함된 현재 인스턴스입니다.</returns>
      <param name="value">비트 OR 연산이 수행될 <see cref="T:System.Collections.BitArray" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.Set(System.Int32,System.Boolean)">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 특정 위치에 있는 비트를 지정한 값으로 설정합니다.</summary>
      <param name="index">설정할 비트의 0부터 시작하는 인덱스입니다. </param>
      <param name="value">비트에 할당될 부울 값입니다. </param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.-or- <paramref name="index" /> is greater than or equal to the number of elements in the <see cref="T:System.Collections.BitArray" />. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.SetAll(System.Boolean)">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 모든 비트를 지정한 값으로 설정합니다.</summary>
      <param name="value">모든 비트에 할당할 부울 값입니다. </param>
      <filterpriority>2</filterpriority>
    </member>
    <member name="M:System.Collections.BitArray.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정된 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.BitArray" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.BitArray" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or- The number of elements in the source <see cref="T:System.Collections.BitArray" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.BitArray" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#Count">
      <summary>
        <see cref="T:System.Collections.BitArray" />의 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" />의 요소 수입니다.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.BitArray" />에 대한 액세스가 동기화되었는지 여부, 즉 스레드로부터 안전한지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Collections.BitArray.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.BitArray" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.BitArray" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.BitArray.Xor(System.Collections.BitArray)">
      <summary>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대한 배타적 비트 OR 연산을 수행합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.BitArray" />의 요소와 지정한 <see cref="T:System.Collections.BitArray" />의 해당 요소에 대한 배타적 비트 OR 연산 결과가 포함된 현재 인스턴스입니다. </returns>
      <param name="value">배타적 비트 OR 연산이 수행될 <see cref="T:System.Collections.BitArray" />입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="value" /> is null. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="value" /> and the current <see cref="T:System.Collections.BitArray" /> do not have the same number of elements. </exception>
      <filterpriority>2</filterpriority>
    </member>
    <member name="T:System.Collections.StructuralComparisons">
      <summary>두 컬렉션 개체의 구조를 비교하기 위한 개체를 제공합니다.</summary>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralComparer">
      <summary>두 개체의 구조를 비교하는 미리 정의된 개체를 가져옵니다.</summary>
      <returns>두 컬렉션 개체의 구조를 비교하는 데 사용되는 미리 정의된 개체입니다.</returns>
    </member>
    <member name="P:System.Collections.StructuralComparisons.StructuralEqualityComparer">
      <summary>두 개체의 구조가 같은지 비교하는 미리 정의된 개체를 가져옵니다.</summary>
      <returns>두 컬렉션 개체의 구조가 같은지 비교하는 데 사용되는 미리 정의된 개체입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.Comparer`1">
      <summary>
        <see cref="T:System.Collections.Generic.IComparer`1" /> 제네릭 인터페이스의 구현에 대한 기본 클래스를 제공합니다.</summary>
      <typeparam name="T">비교할 개체의 형식입니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.Comparer`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Compare(`0,`0)">
      <summary>파생된 클래스에서 재정의된 경우 같은 형식의 두 개체를 비교한 다음 한 개체가 다른 개체보다 작은지, 큰지 또는 두 개체가 같은지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>다음 표와 같이 <paramref name="x" /> 및 <paramref name="y" />의 상대 값을 나타내는 부호 있는 정수입니다.값 의미 0보다 작음 <paramref name="x" />가 <paramref name="y" />보다 작습니다.Zero <paramref name="x" />가 <paramref name="y" />와 같습니다.0보다 큼 <paramref name="x" />가 <paramref name="y" />보다 큰 경우</returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="T" /> 형식이 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스나 <see cref="T:System.IComparable" /> 인터페이스를 구현하지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.Create(System.Comparison{`0})">
      <summary>지정된 비교를 사용하여 비교자를 만듭니다.</summary>
      <returns>새 비교자입니다.</returns>
      <param name="comparison">사용할 비교입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.Comparer`1.Default">
      <summary>제네릭 인수에서 지정한 형식의 기본 정렬 순서 비교자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Comparer`1" />을 상속하고 <paramref name="T" /> 형식에 대한 정렬 순서 비교자의 역할을 하는 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Comparer`1.System#Collections#IComparer#Compare(System.Object,System.Object)">
      <summary>두 개체를 비교하여 한 개체가 다른 개체보다 작거나, 같거나 또는 크다는 것을 나타내는 값을 반환합니다.</summary>
      <returns>다음 표와 같이 <paramref name="x" /> 및 <paramref name="y" />의 상대 값을 나타내는 부호 있는 정수입니다.값 의미 0보다 작음<paramref name="x" />가 <paramref name="y" />보다 작습니다.Zero<paramref name="x" />가 <paramref name="y" />와 같습니다.0보다 큼<paramref name="x" />가 <paramref name="y" />보다 큰 경우</returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> 또는 <paramref name="y" />가 <paramref name="T" /> 형식으로 캐스팅할 수 없는 형식인 경우또는<paramref name="x" />와 <paramref name="y" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스나 <see cref="T:System.IComparable" /> 인터페이스를 구현하지 않는 경우</exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2">
      <summary>키와 값의 컬렉션을 나타냅니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <typeparam name="TKey">사전에 있는 키의 형식입니다.</typeparam>
      <typeparam name="TValue">사전에 있는 값의 형식입니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor">
      <summary>기본 초기 용량을 갖고 있고 키 형식에 대한 기본 같음 비교자를 사용하는 비어 있는 <see cref="T:System.Collections.Generic.Dictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소를 포함하고 키 형식에 대한 기본 같음 비교자를 사용하는 <see cref="T:System.Collections.Generic.Dictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.IDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>지정한 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소를 포함하고 지정한 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />을 사용하는 <see cref="T:System.Collections.Generic.Dictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.IDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</param>
      <param name="comparer">키를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현을 지정하거나, 해당 키 형식에 기본 null을 사용하려면 <see cref="T:System.Collections.Generic.EqualityComparer`1" />을 지정합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>기본 초기 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.Dictionary`2" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">키를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현을 지정하거나, 해당 키 형식에 기본 null을 사용하려면 <see cref="T:System.Collections.Generic.EqualityComparer`1" />을 지정합니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32)">
      <summary>지정된 초기 용량을 갖고 있고 키 형식에 대한 기본 같음 비교자를 사용하는 비어 있는 <see cref="T:System.Collections.Generic.Dictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.#ctor(System.Int32,System.Collections.Generic.IEqualityComparer{`0})">
      <summary>지정된 초기 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.Dictionary`2" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <param name="comparer">키를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현을 지정하거나, 해당 키 형식에 기본 null을 사용하려면 <see cref="T:System.Collections.Generic.EqualityComparer`1" />을 지정합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Add(`0,`1)">
      <summary>지정한 키와 값을 사전에 추가합니다.</summary>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="value">추가할 요소의 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Generic.Dictionary`2" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 모든 키와 값을 제거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Comparer">
      <summary>사전에 대한 키의 일치 여부를 확인하는 데 사용되는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" />을 가져옵니다. </summary>
      <returns>현재 <see cref="T:System.Collections.Generic.Dictionary`2" />에 대한 키의 일치 여부를 확인하고 키에 대한 해시 값을 제공하는 데 사용되는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 제네릭 인터페이스 구현입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsKey(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 지정한 키가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>true에 지정한 키가 있는 요소가 포함되어 있으면 <see cref="T:System.Collections.Generic.Dictionary`2" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 찾을 수 있는 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ContainsValue(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>true에 지정한 값이 있는 요소가 포함되어 있으면 <see cref="T:System.Collections.Generic.Dictionary`2" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 찾을 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 포함된 키/값 쌍의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 포함된 키/값 쌍의 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />를 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 대한 <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" /> 구조체입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Item(`0)">
      <summary>지정된 키에 연결된 값을 가져오거나 설정합니다.</summary>
      <returns>지정한 키와 연결된 값입니다.지정한 키가 없으면 가져오기 작업에서 <see cref="T:System.Collections.Generic.KeyNotFoundException" />을 throw하고 설정 작업에서 지정한 키가 있는 새 요소를 만듭니다.</returns>
      <param name="key">가져오거나 설정할 값의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">속성을 검색할 때 컬렉션에 <paramref name="key" />가 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 키를 포함하는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 지정한 키가 있는 값을 제거합니다.</summary>
      <returns>요소를 성공적으로 찾아서 제거한 경우 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="key" />가 <see cref="T:System.Collections.Generic.Dictionary`2" />에 없는 경우 false을 반환합니다.</returns>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>지정한 키가 있는 <see cref="T:System.Collections.Generic.ICollection`1" />에 지정한 값을 추가합니다.</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에 추가할 키와 값을 나타내는 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" />의 키가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Generic.Dictionary`2" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 키와 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>true가 <paramref name="keyValuePair" />에 있으면 <see cref="T:System.Collections.Generic.ICollection`1" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.KeyValuePair`2" />에서 찾을 <see cref="T:System.Collections.Generic.ICollection`1" /> 구조체입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소를  <see cref="T:System.Collections.Generic.KeyValuePair`2" />형식의 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 복사한 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 요소의 대상인 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 형식의 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>사전이 읽기 전용인지를 나타내는 값을 가져옵니다.</summary>
      <returns>true가 읽기 전용이면 <see cref="T:System.Collections.Generic.ICollection`1" />이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Dictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>사전에서 키와 값을 제거합니다.</summary>
      <returns>
        <paramref name="keyValuePair" />로 표시된 키와 값을 찾아서 제거한 경우 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="keyValuePair" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우 false을 반환합니다.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 제거할 키와 값을 나타내는 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />의 키를 포함하는 <see cref="T:System.Collections.Generic.IDictionary`2" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <paramref name="TKey" /> 형식의 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />의 값이 들어 있는 <see cref="T:System.Collections.Generic.IDictionary`2" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값을 포함하는 <paramref name="TValue" /> 형식의 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />의 키가 포함된 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />의 값을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IReadOnlyDictionary`2" />의 값을 포함하는 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.Generic.ICollection`1" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>true에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 <see cref="T:System.Collections.ICollection" />이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Dictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다. </returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>지정한 키와 값을 사전에 추가합니다.</summary>
      <param name="key">키로 사용할 개체입니다.</param>
      <param name="value">값으로 사용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" />의 형식을 <paramref name="TKey" />의 <see cref="T:System.Collections.Generic.Dictionary`2" /> 키 형식에 할당할 수 없는 경우또는<paramref name="value" />의 형식을 <paramref name="TValue" />의 값 형식인 <see cref="T:System.Collections.Generic.Dictionary`2" />에 할당할 수 없는 경우또는같은 키가 있는 값이 이미 <see cref="T:System.Collections.Generic.Dictionary`2" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>지정된 키를 갖는 요소가 <see cref="T:System.Collections.IDictionary" />에 들어 있는지 여부를 결정합니다.</summary>
      <returns>true에 지정한 키가 있는 요소가 포함되어 있으면 <see cref="T:System.Collections.IDictionary" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" />에서 찾을 수 있는 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionaryEnumerator" />에 대한 <see cref="T:System.Collections.IDictionary" />를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionaryEnumerator" />에 대한 <see cref="T:System.Collections.IDictionary" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true의 크기가 고정되어 있으면 <see cref="T:System.Collections.IDictionary" />이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Dictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true가 읽기 전용이면 <see cref="T:System.Collections.IDictionary" />이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Dictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>지정한 키가 있는 값을 가져오거나 설정합니다.</summary>
      <returns>지정한 키와 연결된 값입니다. <paramref name="key" />가 사전에 없거나 <paramref name="key" />의 형식을 <see cref="T:System.Collections.Generic.Dictionary`2" />의 키 형식 <paramref name="TKey" />에 할당할 수 없으면 null입니다.</returns>
      <param name="key">가져올 값의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">값이 할당되어 있고 <paramref name="key" />의 형식을 <paramref name="TKey" />의 <see cref="T:System.Collections.Generic.Dictionary`2" /> 키 형식에 할당할 수 없는 경우또는값이 할당되어 있고 <paramref name="value" />의 형식을 <paramref name="TValue" />의 <see cref="T:System.Collections.Generic.Dictionary`2" /> 값 형식에 할당할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.ICollection" />의 키를 포함하는 <see cref="T:System.Collections.IDictionary" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />의 키를 포함하는 <see cref="T:System.Collections.IDictionary" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에서 지정한 키를 가지는 요소를 제거합니다.</summary>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.ICollection" />의 값이 들어 있는 <see cref="T:System.Collections.IDictionary" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />의 값을 포함하는 <see cref="T:System.Collections.IDictionary" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.TryGetValue(`0,`1@)">
      <summary>지정한 키와 연결된 값을 가져옵니다.</summary>
      <returns>true에 지정한 키가 있는 요소가 포함되어 있으면 <see cref="T:System.Collections.Generic.Dictionary`2" />이고, 그렇지 않으면 false입니다.</returns>
      <param name="key">가져올 값의 키입니다.</param>
      <param name="value">이 메서드가 반환될 때 지정된 키가 있으면 해당 키와 연결된 값을 포함하고, 그렇지 않으면 <paramref name="value" /> 매개 변수의 형식의 기본값을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 값을 포함하는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.Dictionary`2" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>사전에서 열거자의 현재 위치에 있는 요소인 <see cref="T:System.Collections.DictionaryEntry" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>열거자의 현재 위치에 있는 요소의 키를 가져옵니다.</summary>
      <returns>사전에서 열거자의 현재 위치에 있는 요소의 키입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>열거자의 현재 위치에 있는 요소의 값을 가져옵니다.</summary>
      <returns>사전에서 열거자의 현재 위치에 있는 요소의 값입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소인 <see cref="T:System.Object" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 키 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.Dictionary`2" />의 키를 반영하는 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">해당 키가 새 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에 반영되는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에 포함된 요소 수입니다.이 속성의 값을 검색하는 것은 O(1) 연산입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에 대한 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 항목을 추가합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 항목을 모두 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 개체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 true를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.ICollection`1" />에서 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="item" />이 원래 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true이(가) 반환되고, 컬렉션의 끝을 지난 경우 false이(가) 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2" />의 값 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.#ctor(System.Collections.Generic.Dictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.Dictionary`2" />의 값을 반영하는 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">해당 값이 새 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에 반영되는 <see cref="T:System.Collections.Generic.Dictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에 대한 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 항목을 추가합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 항목을 모두 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 개체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 true를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.ICollection`1" />에서 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="item" />이 원래 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스에서 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.  <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.Dictionary`2.ValueCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Dictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.EqualityComparer`1">
      <summary>
        <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 제네릭 인터페이스의 구현에 대한 기본 클래스를 제공합니다.</summary>
      <typeparam name="T">비교할 개체의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.EqualityComparer`1.Default">
      <summary>제네릭 인수에서 지정한 형식의 기본 같음 비교자를 반환합니다.</summary>
      <returns>
        <paramref name="T" /> 형식에 대한 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 클래스의 기본 인스턴스입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.Equals(`0,`0)">
      <summary>파생 클래스에서 재정의되면 <paramref name="T" /> 형식의 두 개체가 같은지를 확인합니다.</summary>
      <returns>지정한 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.GetHashCode(`0)">
      <summary>파생 클래스에서 재정의되면 해시 테이블 같은 해시 알고리즘과 데이터 구조의 지정한 개체에 대한 해시 함수의 역할을 합니다.</summary>
      <returns>지정한 개체의 해시 코드입니다.</returns>
      <param name="obj">해시 코드를 가져오는 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#Equals(System.Object,System.Object)">
      <summary>지정한 개체가 같은지를 확인합니다.</summary>
      <returns>지정한 개체가 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="x">비교할 첫 번째 개체입니다.</param>
      <param name="y">비교할 두 번째 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="x" /> or <paramref name="y" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="M:System.Collections.Generic.EqualityComparer`1.System#Collections#IEqualityComparer#GetHashCode(System.Object)">
      <summary>지정한 개체의 해시 코드를 반환합니다.</summary>
      <returns>지정한 개체의 해시 코드입니다.</returns>
      <param name="obj">해시 코드가 반환될 <see cref="T:System.Object" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">The type of <paramref name="obj" /> is a reference type and <paramref name="obj" /> is null.-or-<paramref name="obj" /> is of a type that cannot be cast to type <paramref name="T" />.</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1">
      <summary>값 집합을 나타냅니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 Reference Source.</summary>
      <typeparam name="T">해시 집합에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor">
      <summary>비어 있으며 집합 형식에 대한 기본 같음 비교자를 사용하는 <see cref="T:System.Collections.Generic.HashSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>집합 형식에 대한 기본 같음 비교자를 사용하고 지정된 컬렉션에서 복사한 요소가 들어 있으며 복사된 요소 수를 수용하기에 용량이 충분한 <see cref="T:System.Collections.Generic.HashSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 집합에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IEqualityComparer{`0})">
      <summary>집합 형식에 대한 지정된 같음 비교자를 사용하고 지정된 컬렉션에서 복사한 요소가 들어 있으며 복사된 요소 수를 수용하기에 용량이 충분한 <see cref="T:System.Collections.Generic.HashSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 집합에 복사되는 컬렉션입니다.</param>
      <param name="comparer">집합의 값을 비교하려면 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현을 사용하고, 집합 형식에 대한 기본 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 구현을 사용하려면 null을(를) 지정합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.#ctor(System.Collections.Generic.IEqualityComparer{`0})">
      <summary>비어 있으며 집합 형식에 대한 지정된 같음 비교자를 사용하는 <see cref="T:System.Collections.Generic.HashSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">집합의 값을 비교하려면 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 구현을 사용하고, 집합 형식에 대한 기본 <see cref="T:System.Collections.Generic.EqualityComparer`1" /> 구현을 사용하려면 null을(를) 지정합니다.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Add(`0)">
      <summary>지정된 요소를 집합에 추가합니다.</summary>
      <returns>요소가 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에 추가되었으면 true이고, 요소가 이미 있으면 false입니다.</returns>
      <param name="item">집합에 추가할 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 요소를 모두 제거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Comparer">
      <summary>집합의 값이 같은지 확인하는 데 사용되는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 개체를 가져옵니다.</summary>
      <returns>집합의 값이 같은지 확인하는 데 사용되는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Contains(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에 지정된 요소가 포함되어 있는지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에 지정된 요소가 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 찾을 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[])">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32)">
      <summary>지정된 배열 인덱스에서 시작하여 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" />가 대상 <paramref name="array" />의 길이보다 큰 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>지정된 배열 인덱스에서 시작하여 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 지정된 수의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <param name="count">
        <paramref name="array" />에 복사할 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작은 경우또는<paramref name="count" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="arrayIndex" />가 대상 <paramref name="array" />의 길이보다 큰 경우또는<paramref name="count" />가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Count">
      <summary>집합에 포함된 요소 수를 가져옵니다.</summary>
      <returns>집합에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 지정된 컬렉션의 모든 요소를 제거합니다.</summary>
      <param name="other">
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 제거할 항목의 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 반복되는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에 대한 <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체를 수정하여 해당 개체와 지정된 컬렉션에 동시에 있는 요소만 포함합니다.</summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 지정된 컬렉션의 적합한 하위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 <paramref name="other" />의 적합한 하위 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 지정된 컬렉션의 적합한 상위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 <paramref name="other" />의 적합한 상위 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 지정된 컬렉션의 하위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 <paramref name="other" />의 하위 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 지정된 컬렉션의 상위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 <paramref name="other" />의 상위 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 지정된 컬렉션이 공통 요소를 공유하는지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 <paramref name="other" />이(가) 요소를 하나 이상 공유하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체에서 지정된 요소를 제거합니다.</summary>
      <returns>요소를 성공적으로 찾아서 제거한 경우 true이고, 그렇지 않으면 false입니다.<paramref name="item" /> 개체에 <see cref="T:System.Collections.Generic.HashSet`1" />이(가) 없으면 이 메서드는 false을(를) 반환합니다.</returns>
      <param name="item">제거할 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 컬렉션에서 지정된 조건자에 정의된 조건과 일치하는 요소를 모두 제거합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 컬렉션에서 제거된 요소의 수입니다.</returns>
      <param name="match">제거할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 지정된 컬렉션에 같은 요소가 포함되어 있는지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체가 <paramref name="other" />과(와) 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체를 수정하여 해당 개체와 지정된 컬렉션 중 하나에 있는 요소만 포함합니다.</summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 개체에 항목을 추가합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 개체에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>컬렉션이 읽기 전용인지를 나타내는 값을 가져옵니다.</summary>
      <returns>컬렉션이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" /> 개체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체의 용량을 실제로 포함된 요소 수로 설정하고 구현별로 다른 근방 값으로 반올림합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체를 수정하여 해당 개체와 지정된 컬렉션에 있는 모든 요소를 포함시킵니다.</summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.HashSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="T:System.Collections.Generic.HashSet`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 개체의 요소를 열거합니다.</summary>
      <filterpriority>2</filterpriority>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.HashSet`1" /> 컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.HashSet`1.Enumerator" /> 개체에 사용되는 리소스를 모두 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.HashSet`1" /> 컬렉션의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소인 <see cref="T:System.Object" />입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.HashSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1">
      <summary>이중 연결 목록을 나타냅니다.</summary>
      <typeparam name="T">링크된 목록의 요소 형식을 지정합니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor">
      <summary>비어 있는 <see cref="T:System.Collections.Generic.LinkedList`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정한 <see cref="T:System.Collections.IEnumerable" />에서 복사된 요소가 포함되어 있고 복사된 요소의 수를 수용할 수 있는 충분한 용량을 가지는 <see cref="T:System.Collections.Generic.LinkedList`1" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
      <param name="collection">요소가 새 <see cref="T:System.Collections.Generic.LinkedList`1" />에 복사되는 <see cref="T:System.Collections.IEnumerable" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 지정한 기존 노드 다음에 지정한 새 노드를 추가합니다.</summary>
      <param name="node">
        <paramref name="newNode" />를 삽입할 위치 앞에 있는 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <param name="newNode">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 추가할 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.또는<paramref name="newNode" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우또는<paramref name="newNode" />가 다른 <see cref="T:System.Collections.Generic.LinkedList`1" />에 속하는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddAfter(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 지정한 기존 노드 다음에 지정한 값이 포함된 새 노드를 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
      <param name="node">
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />을 삽입할 위치 앞에 있는 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 추가할 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 지정한 기존 노드 앞에 지정한 새 노드를 추가합니다.</summary>
      <param name="node">
        <paramref name="newNode" />를 삽입할 위치 뒤에 있는 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <param name="newNode">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 추가할 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.또는<paramref name="newNode" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우또는<paramref name="newNode" />가 다른 <see cref="T:System.Collections.Generic.LinkedList`1" />에 속하는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddBefore(System.Collections.Generic.LinkedListNode{`0},`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 지정한 기존 노드 앞에 지정한 값이 포함된 새 노드를 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
      <param name="node">
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />을 삽입할 위치 뒤에 있는 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 추가할 값입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 시작 위치에 지정한 새 노드를 추가합니다.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 시작 위치에 추가할 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 다른 <see cref="T:System.Collections.Generic.LinkedList`1" />에 속하는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddFirst(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 시작 위치에 지정한 값이 포함된 새 노드를 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 시작 위치에 추가할 값입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 끝에 지정한 새 노드를 추가합니다.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 끝에 추가할 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 다른 <see cref="T:System.Collections.Generic.LinkedList`1" />에 속하는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.AddLast(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 끝에 지정한 값이 포함된 새 노드를 추가합니다.</summary>
      <returns>
        <paramref name="value" />가 포함된 새 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 끝에 추가할 값입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 노드를 모두 제거합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Contains(`0)">
      <summary>값이 <see cref="T:System.Collections.Generic.LinkedList`1" />에 있는지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="value" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 찾을 수 있는 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.CopyTo(`0[],System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 <see cref="T:System.Collections.Generic.LinkedList`1" />을 호환되는 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.LinkedList`1" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 실제로 포함된 노드의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 실제로 포함된 노드의 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Find(`0)">
      <summary>지정한 값이 포함된 첫 번째 노드를 찾습니다.</summary>
      <returns>지정한 값이 있으면 해당 값이 포함된 첫 번째 <see cref="T:System.Collections.Generic.LinkedListNode`1" />이고, 그렇지 않으면 null입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 찾을 수 있는 값입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.FindLast(`0)">
      <summary>지정한 값이 포함된 마지막 노드를 찾습니다.</summary>
      <returns>지정한 값이 있으면 해당 값이 포함된 마지막 <see cref="T:System.Collections.Generic.LinkedListNode`1" />이고, 그렇지 않으면 null입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 찾을 수 있는 값입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.First">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 첫 번째 노드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 첫 번째 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에 대한 <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Last">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 마지막 노드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 마지막 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(System.Collections.Generic.LinkedListNode{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 지정된 노드를 제거합니다.</summary>
      <param name="node">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 제거할 <see cref="T:System.Collections.Generic.LinkedListNode`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="node" />가 null입니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="node" />가 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 맨 처음 발견되는 지정된 값을 제거합니다.</summary>
      <returns>
        <paramref name="value" />가 포함된 요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.  이 메서드는 <paramref name="value" />가 원래 <see cref="T:System.Collections.Generic.LinkedList`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 제거할 값입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveFirst">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 시작 위치에서 노드를 제거합니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" />이 비어 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.RemoveLast">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 끝에서 노드를 제거합니다.</summary>
      <exception cref="T:System.InvalidOperationException">
        <see cref="T:System.Collections.Generic.LinkedList`1" />이 비어 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />의 끝에 항목을 추가합니다.</summary>
      <param name="value">
        <see cref="T:System.Collections.Generic.ICollection`1" />의 끝에 추가할 값입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.LinkedList`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스에서 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.LinkedList`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.  <see cref="T:System.Collections.Generic.LinkedList`1" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>링크된 목록을 컬렉션으로 반복하는 열거자를 반환합니다.</summary>
      <returns>링크된 목록을 컬렉션으로 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.LinkedList`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.LinkedList`1" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.LinkedList`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.이 클래스는 상속될 수 없습니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.LinkedListNode`1">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 노드를 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
      <typeparam name="T">링크된 목록의 요소 형식을 지정합니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.LinkedListNode`1.#ctor(`0)">
      <summary>지정한 값을 포함하는 <see cref="T:System.Collections.Generic.LinkedListNode`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="value">
        <see cref="T:System.Collections.Generic.LinkedListNode`1" />에 포함할 값입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.List">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedListNode`1" />이 속하는 <see cref="T:System.Collections.Generic.LinkedList`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedListNode`1" />이 속하는 <see cref="T:System.Collections.Generic.LinkedList`1" />에 대한 참조이거나, <see cref="T:System.Collections.Generic.LinkedListNode`1" />이 링크되어 있지 않으면 null입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Next">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 다음 노드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 다음 노드에 대한 참조이거나, 현재 노드가 <see cref="T:System.Collections.Generic.LinkedList`1" />의 마지막 요소(<see cref="P:System.Collections.Generic.LinkedList`1.Last" />)이면 null입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Previous">
      <summary>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 이전 노드를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.LinkedList`1" />의 이전 노드에 대한 참조이거나, 현재 노드가 <see cref="T:System.Collections.Generic.LinkedList`1" />의 첫 번째 요소(<see cref="P:System.Collections.Generic.LinkedList`1.First" />)이면 null입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.LinkedListNode`1.Value">
      <summary>노드에 포함된 값을 가져옵니다.</summary>
      <returns>노드에 포함된 값입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.List`1">
      <summary>인덱스로 액세스할 수 있는 강력한 형식의 개체 목록을 나타냅니다.목록의 검색, 정렬 및 조작에 사용할 수 있는 메서드를 제공합니다.이 형식에 대 한.NET Framework 소스 코드를 찾아보려면 참조는 참조 원본.</summary>
      <typeparam name="T">목록에 있는 요소의 형식입니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor">
      <summary>비어 있는 상태에서 기본 초기 용량을 가지는 <see cref="T:System.Collections.Generic.List`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소를 포함하고 복사한 요소를 모두 수용할 수 있을 정도의 용량을 가진 <see cref="T:System.Collections.Generic.List`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">요소가 새 목록에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.#ctor(System.Int32)">
      <summary>비어 있는 상태에서 지정한 초기 용량을 가지는 <see cref="T:System.Collections.Generic.List`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">새 목록에 처음부터 저장할 수 있는 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작습니다. </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Add(`0)">
      <summary>개체를 <see cref="T:System.Collections.Generic.List`1" />의 끝 부분에 추가합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />의 끝에 추가할 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.AddRange(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션의 요소를 <see cref="T:System.Collections.Generic.List`1" />의 끝에 추가합니다.</summary>
      <param name="collection">요소가 <see cref="T:System.Collections.Generic.List`1" />의 끝에 추가되어야 하는 컬렉션입니다.컬렉션 자체가 null일 수는 없지만 <paramref name="T" /> 형식이 참조 형식인 경우 null인 요소를 포함할 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.AsReadOnly">
      <summary>현재 컬렉션에 대한 읽기 전용 <see cref="T:System.Collections.Generic.IList`1" /> 래퍼를 반환합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.Generic.List`1" />에 대한 읽기 전용 래퍼 역할을 하는 <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(System.Int32,System.Int32,`0,System.Collections.Generic.IComparer{`0})">
      <summary>지정된 비교자를 사용하여 정렬된 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 요소를 검색하고 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="item" />이 있으면 정렬된 <see cref="T:System.Collections.Generic.List`1" />에 있는 <paramref name="item" />의 인덱스(0부터 시작)이고, 그렇지 않으면 <paramref name="item" />보다 큰 다음 요소의 인덱스에 대한 비트 보수인 음수이거나 더 큰 요소가 없는 경우 <see cref="P:System.Collections.Generic.List`1.Count" />의 비트 보수입니다.</returns>
      <param name="index">검색할 범위의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">검색할 범위의 길이입니다.</param>
      <param name="item">찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="comparer">요소를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현이거나, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />를 사용하려면 null입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />의 올바른 범위를 나타내지 않는 경우</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" />가 null이고, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0)">
      <summary>기본 비교자를 사용하여 정렬된 전체 <see cref="T:System.Collections.Generic.List`1" />에서 요소를 검색하고 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="item" />이 있으면 정렬된 <see cref="T:System.Collections.Generic.List`1" />에 있는 <paramref name="item" />의 인덱스(0부터 시작)이고, 그렇지 않으면 <paramref name="item" />보다 큰 다음 요소의 인덱스에 대한 비트 보수인 음수이거나 더 큰 요소가 없는 경우 <see cref="P:System.Collections.Generic.List`1.Count" />의 비트 보수입니다.</returns>
      <param name="item">찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.InvalidOperationException">기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.BinarySearch(`0,System.Collections.Generic.IComparer{`0})">
      <summary>지정된 비교자를 사용하여 정렬된 전체 <see cref="T:System.Collections.Generic.List`1" />에서 요소를 검색하고 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="item" />이 있으면 정렬된 <see cref="T:System.Collections.Generic.List`1" />에 있는 <paramref name="item" />의 인덱스(0부터 시작)이고, 그렇지 않으면 <paramref name="item" />보다 큰 다음 요소의 인덱스에 대한 비트 보수인 음수이거나 더 큰 요소가 없는 경우 <see cref="P:System.Collections.Generic.List`1.Count" />의 비트 보수입니다.</returns>
      <param name="item">찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="comparer">요소를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현입니다.또는기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />를 사용하려면 null입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" />가 null이고, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Capacity">
      <summary>크기를 조정하지 않고 내부 데이터 구조가 보유할 수 있는 전체 요소 수를 가져오거나 설정합니다.</summary>
      <returns>크기를 조정하지 않고 <see cref="T:System.Collections.Generic.List`1" />에 포함될 수 있는 요소 수입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.List`1.Capacity" />가 <see cref="P:System.Collections.Generic.List`1.Count" />보다 작은 값으로 설정된 경우 </exception>
      <exception cref="T:System.OutOfMemoryException">시스템에 사용 가능한 메모리가 부족합니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />에서 요소를 모두 제거합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Contains(`0)">
      <summary>요소가 <see cref="T:System.Collections.Generic.List`1" />에 있는지 여부를 확인합니다.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.Generic.List`1" />; otherwise, false.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(System.Int32,`0[],System.Int32,System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 <see cref="T:System.Collections.Generic.List`1" />에 있는 일련의 요소를 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="index">소스 <see cref="T:System.Collections.Generic.List`1" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <param name="count">복사할 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="arrayIndex" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" />가 소스 <see cref="T:System.Collections.Generic.List`1" />의 <see cref="P:System.Collections.Generic.List`1.Count" />보다 크거나 같은 경우또는<paramref name="index" />부터 소스 <see cref="T:System.Collections.Generic.List`1" /> 끝까지의 요소 수가 <paramref name="arrayIndex" />부터 대상 <paramref name="array" /> 끝까지의 사용 가능한 공간보다 큰 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[])">
      <summary>대상 배열의 처음부터 시작하여 전체 <see cref="T:System.Collections.Generic.List`1" />을 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.List`1" />의 요소의 수가 대상 <paramref name="array" />가 포함할 수 있는 요소의 수보다 큰 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.CopyTo(`0[],System.Int32)">
      <summary>대상 배열의 지정된 인덱스에서 시작하여 전체 <see cref="T:System.Collections.Generic.List`1" />을 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.Generic.List`1" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.List`1" />의 요소 수가 <paramref name="arrayIndex" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Exists(System.Predicate{`0})">
      <summary>지정된 조건자에 정의된 조건과 일치하는 요소가 <see cref="T:System.Collections.Generic.List`1" />에 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>지정된 조건자에 정의된 조건과 일치하는 요소가 하나 이상 <see cref="T:System.Collections.Generic.List`1" />에 포함되어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Find(System.Predicate{`0})">
      <summary>지정된 조건자에 정의된 조건과 일치하는 요소를 검색하고 전체 <see cref="T:System.Collections.Generic.List`1" />에서 처음으로 검색한 요소를 반환합니다.</summary>
      <returns>지정한 조건자에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 첫 번째 요소이고, 그렇지 않으면 <paramref name="T" /> 형식의 기본값입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindAll(System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 모든 요소를 검색합니다.</summary>
      <returns>지정된 조건자에 정의된 조건과 일치하는 요소가 있으면 해당 요소를 모두 포함하는 <see cref="T:System.Collections.Generic.List`1" />이고, 그렇지 않으면 빈 <see cref="T:System.Collections.Generic.List`1" />입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 지정한 인덱스부터 시작하여 지정한 수의 요소를 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="startIndex">검색의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">검색할 섹션에 있는 요소 수입니다.</param>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우또는<paramref name="count" />가 0보다 작습니다.또는<paramref name="startIndex" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에서 올바른 섹션을 나타내지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Int32,System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 지정한 인덱스에서 마지막 요소로 확장하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="startIndex">검색의 0부터 시작하는 인덱스입니다.</param>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindIndex(System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 전체 <see cref="T:System.Collections.Generic.List`1" />에서 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 첫 번째 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLast(System.Predicate{`0})">
      <summary>지정된 조건자에 정의된 조건과 일치하는 요소를 검색하고 전체 <see cref="T:System.Collections.Generic.List`1" />에서 마지막으로 검색한 요소를 반환합니다.</summary>
      <returns>지정한 조건자에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 마지막 요소이고, 그렇지 않으면 <paramref name="T" /> 형식의 기본값입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Int32,System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 지정한 수의 요소가 들어 있고 지정한 인덱스에서 끝나는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="startIndex">역방향 검색의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">검색할 섹션에 있는 요소 수입니다.</param>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우또는<paramref name="count" />가 0보다 작습니다.또는<paramref name="startIndex" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에서 올바른 섹션을 나타내지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Int32,System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 첫 번째 요소에서 지정한 인덱스로 확장하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="startIndex">역방향 검색의 0부터 시작하는 인덱스입니다.</param>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="startIndex" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.FindLastIndex(System.Predicate{`0})">
      <summary>지정한 조건자에 정의된 조건과 일치하는 요소를 검색하여 전체 <see cref="T:System.Collections.Generic.List`1" />에서 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="match" />에 정의된 조건과 일치하는 요소가 있으면 일치하는 요소 중 마지막 요소의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="match">검색할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ForEach(System.Action{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 각 요소에 대해 지정된 작업을 수행합니다.</summary>
      <param name="action">
        <see cref="T:System.Collections.Generic.List`1" />의 각 요소에 대해 수행할 <see cref="T:System.Action`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="action" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />에 대한 <see cref="T:System.Collections.Generic.List`1.Enumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.GetRange(System.Int32,System.Int32)">
      <summary>소스 <see cref="T:System.Collections.Generic.List`1" />에 있는 일련의 요소에 대한 단순 복사본을 만듭니다.</summary>
      <returns>소스 <see cref="T:System.Collections.Generic.List`1" />에 있는 일련의 요소에 대한 단순 복사본입니다.</returns>
      <param name="index">범위가 시작되는 <see cref="T:System.Collections.Generic.List`1" /> 인덱스(0부터 시작)입니다.</param>
      <param name="count">범위의 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에 있는 요소의 올바른 범위를 나타내지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0)">
      <summary>지정된 개체를 검색하고, 전체 <see cref="T:System.Collections.Generic.List`1" />에서 처음으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="item" />이 있으면 전체 <see cref="T:System.Collections.Generic.List`1" />에서 맨 처음 발견되는 값의 0부터 시작하는 인덱스이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32)">
      <summary>지정된 개체를 검색하고, 지정된 인덱스부터 마지막 요소까지 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 처음으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="index" />부터 마지막 요소까지 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에 <paramref name="item" />이 있으면 처음으로 검색한 개체의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="index">검색의 0부터 시작하는 인덱스입니다.0은 빈 목록에서 유효합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.IndexOf(`0,System.Int32,System.Int32)">
      <summary>지정된 개체를 검색하고, 지정된 인덱스에서 시작하여 지정된 수의 요소를 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 처음으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="index" />에서 시작하여 <paramref name="count" />개의 요소를 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에 <paramref name="item" />이 있으면 처음으로 검색한 개체의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="index">검색의 0부터 시작하는 인덱스입니다.0은 빈 목록에서 유효합니다.</param>
      <param name="count">검색할 섹션에 있는 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우또는<paramref name="count" />가 0보다 작습니다.또는<paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에서 올바른 섹션을 나타내지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Insert(System.Int32,`0)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 지정된 인덱스에 요소를 삽입합니다.</summary>
      <param name="index">
        <paramref name="item" />를 삽입해야 하는 인덱스(0부터 시작)입니다.</param>
      <param name="item">삽입할 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="index" />가 <see cref="P:System.Collections.Generic.List`1.Count" />보다 큰 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 지정된 인덱스에 컬렉션의 요소를 삽입합니다.</summary>
      <param name="index">새 요소가 삽입되어야 하는 0부터 시작하는 인덱스입니다.</param>
      <param name="collection">요소가 <see cref="T:System.Collections.Generic.List`1" />에 삽입되어야 하는 컬렉션입니다.컬렉션 자체가 null일 수는 없지만 <paramref name="T" /> 형식이 참조 형식인 경우 null인 요소를 포함할 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="index" />가 <see cref="P:System.Collections.Generic.List`1.Count" />보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Item(System.Int32)">
      <summary>지정한 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정한 인덱스의 요소입니다.</returns>
      <param name="index">가져오거나 설정할 요소의 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="index" />가 <see cref="P:System.Collections.Generic.List`1.Count" />보다 크거나 같은 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0)">
      <summary>지정된 개체를 검색하고, 전체 <see cref="T:System.Collections.Generic.List`1" />에서 마지막으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>전체 <see cref="T:System.Collections.Generic.List`1" />에 <paramref name="item" />이 있으면 마지막으로 검색한 개체의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32)">
      <summary>지정된 개체를 검색하고, 첫 번째 요소부터 지정된 인덱스까지 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 마지막으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>첫 번째 요소부터 <paramref name="index" />까지 포함하는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에 <paramref name="item" />이 있으면 마지막으로 검색한 개체의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="index">역방향 검색의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.LastIndexOf(`0,System.Int32,System.Int32)">
      <summary>지정된 개체를 검색하며, 지정된 수의 요소를 포함하고 지정된 인덱스에서 끝나는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에서 마지막으로 검색한 개체의 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="count" />개의 요소를 포함하고 <paramref name="index" />에서 끝나는 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에 <paramref name="item" />이 있으면 마지막으로 검색한 개체의 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <param name="index">역방향 검색의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">검색할 섹션에 있는 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.Generic.List`1" />의 유효한 인덱스 범위 밖에 있는 경우또는<paramref name="count" />가 0보다 작습니다.또는<paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에서 올바른 섹션을 나타내지 않는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.</summary>
      <returns>
        <paramref name="item" />이 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.This method also returns false if <paramref name="item" /> was not found in the <see cref="T:System.Collections.Generic.List`1" />.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.List`1" />에서 제거할 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAll(System.Predicate{`0})">
      <summary>지정된 조건자에 정의된 조건과 일치하는 요소를 모두 제거합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />에서 제거한 요소의 수입니다.</returns>
      <param name="match">제거할 요소의 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 지정한 인덱스에서 요소를 제거합니다.</summary>
      <param name="index">제거할 요소의 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="index" />가 <see cref="P:System.Collections.Generic.List`1.Count" />보다 크거나 같은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.RemoveRange(System.Int32,System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />에서 요소의 범위를 제거합니다.</summary>
      <param name="index">제거할 요소의 범위에 대한 0부터 시작하는 인덱스입니다.</param>
      <param name="count">제거할 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에 있는 요소의 올바른 범위를 나타내지 않는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse">
      <summary>전체 <see cref="T:System.Collections.Generic.List`1" />에서 요소의 순서를 반대로 바꿉니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Reverse(System.Int32,System.Int32)">
      <summary>지정된 범위에서 요소의 순서를 반대로 바꿉니다.</summary>
      <param name="index">순서를 반대로 바꿀 범위의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">순서를 반대로 바꿀 범위의 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다. </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에 있는 요소의 올바른 범위를 나타내지 않는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort">
      <summary>기본 비교자를 사용하여 전체 <see cref="T:System.Collections.Generic.List`1" />의 요소를 정렬합니다.</summary>
      <exception cref="T:System.InvalidOperationException">기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Collections.Generic.IComparer{`0})">
      <summary>지정된 비교자를 사용하여 전체 <see cref="T:System.Collections.Generic.List`1" />에 있는 요소를 정렬합니다.</summary>
      <param name="comparer">요소를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현이거나, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />를 사용하려면 null입니다.</param>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" />가 null이고, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
      <exception cref="T:System.ArgumentException">정렬하는 동안<paramref name="comparer" /> 구현에서 오류가 발생한 경우예를 들어, <paramref name="comparer" />는 항목을 항목 자체와 비교할 때 0을 반환하지 않을 수 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Comparison{`0})">
      <summary>지정된 <see cref="T:System.Comparison`1" />을 사용하여 전체 <see cref="T:System.Collections.Generic.List`1" />의 요소를 정렬합니다.</summary>
      <param name="comparison">요소를 비교할 때 사용할 <see cref="T:System.Comparison`1" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparison" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">정렬하는 동안<paramref name="comparison" /> 구현에서 오류가 발생한 경우예를 들어, <paramref name="comparison" />는 항목을 항목 자체와 비교할 때 0을 반환하지 않을 수 있습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Sort(System.Int32,System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>지정된 비교자를 사용하여 <see cref="T:System.Collections.Generic.List`1" />의 요소 범위에 있는 요소를 정렬합니다.</summary>
      <param name="index">정렬할 범위의 0부터 시작하는 인덱스입니다.</param>
      <param name="count">정렬할 범위의 길이입니다.</param>
      <param name="comparer">요소를 비교할 때 사용할 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현이거나, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />를 사용하려면 null입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작습니다.또는<paramref name="count" />가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="index" /> 및 <paramref name="count" />가 <see cref="T:System.Collections.Generic.List`1" />에서 올바른 범위를 나타내지 않는 경우또는정렬하는 동안<paramref name="comparer" /> 구현에서 오류가 발생한 경우예를 들어, <paramref name="comparer" />는 항목을 항목 자체와 비교할 때 0을 반환하지 않을 수 있습니다.</exception>
      <exception cref="T:System.InvalidOperationException">
        <paramref name="comparer" />가 null이고, 기본 비교자 <see cref="P:System.Collections.Generic.Comparer`1.Default" />가 <see cref="T:System.IComparable`1" /> 제네릭 인터페이스의 구현이나 <paramref name="T" /> 형식의 <see cref="T:System.IComparable" /> 인터페이스를 찾지 못한 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" /> is read-only; otherwise, false.<see cref="T:System.Collections.Generic.List`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작습니다.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="arrayIndex" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.List`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.<see cref="T:System.Collections.Generic.List`1" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Add(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" />에 항목을 추가합니다.</summary>
      <returns>새 요소가 삽입된 위치입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" />에 추가할 <see cref="T:System.Object" />입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" />의 형식은 <see cref="T:System.Collections.IList" />에 할당할 수 없습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if <paramref name="item" /> is found in the <see cref="T:System.Collections.IList" />; otherwise, false.</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" />에서 찾을 <see cref="T:System.Object" />입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#IndexOf(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" />에서 특정 항목의 인덱스를 확인합니다.</summary>
      <returns>목록에 있으면 <paramref name="item" />의 인덱스이고, 그렇지 않으면 -1입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.IList" />에서 찾을 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" />의 형식은 <see cref="T:System.Collections.IList" />에 할당할 수 없습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Insert(System.Int32,System.Object)">
      <summary>항목을 <see cref="T:System.Collections.IList" />의 지정된 인덱스에 삽입합니다.</summary>
      <param name="index">
        <paramref name="item" />를 삽입해야 하는 인덱스(0부터 시작)입니다.</param>
      <param name="item">
        <see cref="T:System.Collections.IList" />에 삽입할 개체입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.IList" />의 유효한 인덱스가 아닌 경우 </exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" />의 형식은 <see cref="T:System.Collections.IList" />에 할당할 수 없습니다.</exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IList" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IList" />의 크기가 고정되어 있으면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.List`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IList" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IList" /> is read-only; otherwise, false.<see cref="T:System.Collections.Generic.List`1" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.List`1.System#Collections#IList#Item(System.Int32)">
      <summary>지정한 인덱스에 있는 요소를 가져오거나 설정합니다.</summary>
      <returns>지정한 인덱스의 요소입니다.</returns>
      <param name="index">가져오거나 설정할 요소의 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 <see cref="T:System.Collections.IList" />의 유효한 인덱스가 아닌 경우</exception>
      <exception cref="T:System.ArgumentException">속성이 설정되어 있고 <paramref name="value" />의 형식을 <see cref="T:System.Collections.IList" />에 할당할 수 없는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.System#Collections#IList#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IList" />에서 맨 처음 발견되는 특정 개체를 제거합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.IList" />에서 제거할 개체입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="item" />의 형식은 <see cref="T:System.Collections.IList" />에 할당할 수 없습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 요소를 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />의 요소 복사본을 포함하는 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />에 있는 요소의 실제 수가 임계값보다 작은 경우 용량을 요소 수로 설정합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.TrueForAll(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 모든 요소가 지정된 조건자에 정의된 조건과 일치하는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />의 모든 요소가 지정된 조건자에 정의된 조건과 일치하면 true이고, 그렇지 않으면 false입니다.목록에 요소가 없으면 반환 값은 true입니다.</returns>
      <param name="match">요소에 대해 확인할 조건을 정의하는 <see cref="T:System.Predicate`1" /> 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="T:System.Collections.Generic.List`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.List`1" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.List`1.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.List`1" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.List`1" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.List`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.Queue`1">
      <summary>개체의 선입선출(FIFO) 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">큐에 있는 요소의 형식을 지정합니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor">
      <summary>비어 있는 상태에서 기본 초기 용량을 가지는 <see cref="T:System.Collections.Generic.Queue`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소를 포함하고 복사한 요소를 모두 수용할 수 있을 정도의 용량을 가진 <see cref="T:System.Collections.Generic.Queue`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">해당 요소가 새 <see cref="T:System.Collections.Generic.Queue`1" />에 복사되는 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.#ctor(System.Int32)">
      <summary>비어 있는 상태에서 지정한 초기 용량을 가지는 <see cref="T:System.Collections.Generic.Queue`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Queue`1" />에 포함될 수 있는 초기 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />에서 개체를 모두 제거합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Contains(`0)">
      <summary>요소가 <see cref="T:System.Collections.Generic.Queue`1" />에 있는지를 확인합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.Queue`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.Queue`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Queue`1" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Queue`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />에 포함된 요소의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />에 포함된 요소의 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Dequeue">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />의 시작 부분에서 개체를 제거하고 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />의 시작에서 제거하는 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enqueue(`0)">
      <summary>개체를 <see cref="T:System.Collections.Generic.Queue`1" />의 끝 부분에 추가합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.Queue`1" />에 추가할 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />에 대한 <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Peek">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />의 시작 부분에서 개체를 제거하지 않고 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />의 처음에 있는 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Queue`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="index" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Queue`1" />의 기본 구현에서 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.<see cref="T:System.Collections.Generic.Queue`1" />의 기본 구현에서 이 속성은 항상 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" /> 요소를 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />에서 복사된 요소를 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />의 실제 요소 수가 현재 용량의 90% 미만인 경우 용량을 이 값으로 설정합니다.</summary>
    </member>
    <member name="T:System.Collections.Generic.Queue`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Queue`1" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Queue`1.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.Queue`1" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Queue`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2">
      <summary>키에 따라 정렬된 키/값 쌍의 컬렉션을 나타냅니다. </summary>
      <typeparam name="TKey">사전에 있는 키의 형식입니다.</typeparam>
      <typeparam name="TValue">사전에 있는 값의 형식입니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor">
      <summary>비어 있고 키 형식에 대해 기본적으로 구현된 <see cref="T:System.Collections.Generic.IComparer`1" />를 사용하는 빈 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>지정된 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현을 사용하여 키를 비교하는 빈 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소를 포함하고 키 형식에 대해 기본적으로 구현된 <see cref="T:System.Collections.Generic.IComparer`1" />를 사용하는 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.IDictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>지정한 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소를 포함하고 지정한 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현을 사용하여 키를 비교하는 <see cref="T:System.Collections.Generic.SortedDictionary`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 복사되는 <see cref="T:System.Collections.Generic.IDictionary`2" />입니다.</param>
      <param name="comparer">The <see cref="T:System.Collections.Generic.IComparer`1" /> implementation to use when comparing keys, or null to use the default <see cref="T:System.Collections.Generic.Comparer`1" /> for the type of the key.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Add(`0,`1)">
      <summary>지정한 키와 값을 가지는 요소를 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 추가합니다.</summary>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="value">추가할 요소의 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 모든 요소를 제거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Comparer">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 요소를 정렬하는 데 사용되는 <see cref="T:System.Collections.Generic.IComparer`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 요소를 정렬하는 데 사용되는 <see cref="T:System.Collections.Generic.IComparer`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsKey(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 지정된 키를 가진 요소가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ContainsValue(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 지정한 값이 있는 요소가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 찾을 수 있는 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>지정한 인덱스로부터 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 요소를 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체의 지정한 배열에 복사합니다.</summary>
      <param name="array">현재 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 복사된 요소의 대상인 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체의 1차원 배열입니다. 이 배열의 인덱스는 0부터 시작해야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 포함된 키/값 쌍의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 포함된 키/값 쌍의 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 대한 <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Item(`0)">
      <summary>지정된 키에 연결된 값을 가져오거나 설정합니다.</summary>
      <returns>지정한 키와 연결된 값입니다.지정한 키가 없으면 get 작업에서 <see cref="T:System.Collections.Generic.KeyNotFoundException" />을 throw하고 set 작업에서 지정한 키가 있는 새 요소를 만듭니다.</returns>
      <param name="key">가져오거나 설정할 값의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">속성을 검색할 때 컬렉션에 <paramref name="key" />가 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 키를 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <returns>요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="key" />가 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 항목을 추가합니다.</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="keyValuePair" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Generic.SortedDictionary`2" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 키와 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.SortedDictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 지정된 요소를 제거합니다.</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 구조체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>키를 포함 하는 컬렉션을 가져옵니다는<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>컬렉션에서 키를 포함 하는<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>값이 포함 된 컬렉션을 가져옵니다는<see cref="T:System.Collections.Generic.SortedDictionary`2" /></summary>
      <returns>컬렉션에서 값을 포함 하는<see cref="T:System.Collections.Generic.SortedDictionary`2" /></returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정한 배열 인덱스부터 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.Generic.ICollection`1" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.SortedDictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다. </returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>제공된 키와 값을 가진 요소를 <see cref="T:System.Collections.IDictionary" />에 추가합니다.</summary>
      <param name="key">추가할 요소의 키로 사용할 개체입니다.</param>
      <param name="value">추가할 요소의 값으로 사용할 개체입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.또는<paramref name="value" />의 형식을 <see cref="T:System.Collections.IDictionary" />의 값 형식 <paramref name="TValue" />에 할당할 수 없는 경우또는같은 키를 가진 요소가 이미 <see cref="T:System.Collections.IDictionary" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에 지정된 키를 가진 요소가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 <see cref="T:System.Collections.IDictionaryEnumerator" />를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />에 대한 <see cref="T:System.Collections.IDictionaryEnumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.<see cref="T:System.Collections.Generic.SortedDictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.<see cref="T:System.Collections.Generic.SortedDictionary`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>지정한 키를 가진 요소를 가져오거나 설정합니다.</summary>
      <returns>지정한 키를 갖는 요소입니다. <paramref name="key" />가 사전에 없거나 <paramref name="key" />의 형식을 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 <paramref name="TKey" /> 키 형식에 할당할 수 없으면 null입니다.</returns>
      <param name="key">가져올 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.또는A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedDictionary`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 키를 포함하는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />의 키를 포함하는 <see cref="T:System.Collections.ICollection" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 값을 포함하는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />의 값을 포함하는 <see cref="T:System.Collections.ICollection" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.TryGetValue(`0,`1@)">
      <summary>지정한 키와 연결된 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedDictionary`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">가져올 값의 키입니다.</param>
      <param name="value">이 메서드가 반환될 때 지정한 키가 있으면 해당 키와 연결된 값이고, 그렇지 않으면 <paramref name="value" /> 매개 변수 형식의 기본값을 포함합니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 값을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Entry">
      <summary>열거자의 현재 위치에 있는 요소를 <see cref="T:System.Collections.DictionaryEntry" /> 구조체로 가져옵니다.</summary>
      <returns>컬렉션에서 사전의 현재 위치에 있는 요소인 <see cref="T:System.Collections.DictionaryEntry" /> 구조체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Key">
      <summary>열거자의 현재 위치에 있는 요소의 키를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소의 키입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IDictionaryEnumerator#Value">
      <summary>열거자의 현재 위치에 있는 요소의 값을 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소의 값입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 키 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 키를 반영하는 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">해당 키가 새 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에 반영되는 <see cref="T:System.Collections.Generic.SortedDictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다. </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에 대한 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" /> 구조체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 항목을 추가합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 항목을 모두 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Contains(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 지정한 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 개체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#ICollection{T}#Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.ICollection`1" />에서 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 배열 인덱스부터 <see cref="T:System.Collections.ICollection" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.SortedDictionary`2.KeyCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.KeyCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 값 컬렉션을 나타냅니다.이 클래스는 상속될 수 없습니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.#ctor(System.Collections.Generic.SortedDictionary{`0,`1})">
      <summary>지정한 <see cref="T:System.Collections.Generic.SortedDictionary`2" />의 값을 반영하는 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">해당 값이 새 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에 반영되는 <see cref="T:System.Collections.Generic.SortedDictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.CopyTo(`1[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" /> 요소를 지정한 배열 인덱스에서 시작하여 기존의 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에 대한 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" /> 구조체입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Add(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 항목을 추가합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Clear">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 항목을 모두 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Contains(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 지정한 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>
        <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 개체입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#ICollection{T}#Remove(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.ICollection`1" />에서 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 개체를 제거합니다.  이 구현은 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>
        <paramref name="item" />이 <see cref="T:System.Collections.Generic.ICollection`1" />에서 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.이 메서드는 <paramref name="item" />가 <see cref="T:System.Collections.Generic.ICollection`1" />에 없는 경우에도 false를 반환합니다.</returns>
      <exception cref="T:System.NotSupportedException">항상 throw됩니다. 컬렉션이 읽기 전용입니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 배열 인덱스부터 <see cref="T:System.Collections.ICollection" />의 요소를 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에서 0부터 시작하는 인덱스를 사용해야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null입니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="index" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.  <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.SortedDictionary`2.ValueCollection" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedDictionary`2.ValueCollection.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.SortedList`2">
      <summary>연관된 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현을 기반으로 키에 따라 정렬된 키/값 쌍의 컬렉션을 나타냅니다. </summary>
      <typeparam name="TKey">컬렉션에 있는 키의 형식입니다.</typeparam>
      <typeparam name="TValue">컬렉션에 있는 값의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor">
      <summary>기본 초기 용량을 갖고 있고 기본 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>기본 초기 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현입니다.또는null이면 기본 <see cref="T:System.Collections.Generic.Comparer`1" />을 키의 형식으로 사용합니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1})">
      <summary>지정된 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소가 포함되어 있고, 복사된 요소의 수를 수용할 수 있는 충분한 용량을 가지며, 기본 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.SortedList`2" />에 복사되는 <see cref="T:System.Collections.Generic.IDictionary`2" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Collections.Generic.IDictionary{`0,`1},System.Collections.Generic.IComparer{`0})">
      <summary>지정된 <see cref="T:System.Collections.Generic.IDictionary`2" />에서 복사된 요소가 포함되어 있고, 복사된 요소의 수를 수용할 수 있는 충분한 용량을 가지며, 지정된 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="dictionary">요소가 새 <see cref="T:System.Collections.Generic.SortedList`2" />에 복사되는 <see cref="T:System.Collections.Generic.IDictionary`2" />입니다.</param>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현입니다.또는null이면 기본 <see cref="T:System.Collections.Generic.Comparer`1" />을 키의 형식으로 사용합니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="dictionary" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="dictionary" />에 중복 키가 하나 이상 포함된 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32)">
      <summary>지정된 초기 용량을 갖고 있고 기본 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.#ctor(System.Int32,System.Collections.Generic.IComparer{`0})">
      <summary>지정된 초기 용량을 갖고 있고 지정된 <see cref="T:System.Collections.Generic.IComparer`1" />을 사용하는 비어 있는 <see cref="T:System.Collections.Generic.SortedList`2" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <param name="comparer">키를 비교할 때 사용하는 <see cref="T:System.Collections.Generic.IComparer`1" /> 구현입니다.또는null이면 기본 <see cref="T:System.Collections.Generic.Comparer`1" />을 키의 형식으로 사용합니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Add(`0,`1)">
      <summary>지정한 키와 값을 가지는 요소를 <see cref="T:System.Collections.Generic.SortedList`2" />에 추가합니다.</summary>
      <param name="key">추가할 요소의 키입니다.</param>
      <param name="value">추가할 요소의 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">같은 키를 가진 요소가 이미 <see cref="T:System.Collections.Generic.SortedList`2" />에 있는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Capacity">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함될 수 있는 요소의 수를 가져오거나 설정합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함될 수 있는 요소의 수입니다.</returns>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <see cref="P:System.Collections.Generic.SortedList`2.Capacity" />가 <see cref="P:System.Collections.Generic.SortedList`2.Count" />보다 작은 값으로 설정된 경우</exception>
      <exception cref="T:System.OutOfMemoryException">시스템에 사용 가능한 메모리가 부족합니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 모든 요소를 제거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Comparer">
      <summary>정렬된 목록에 대한 <see cref="T:System.Collections.Generic.IComparer`1" />을 가져옵니다. </summary>
      <returns>현재 <see cref="T:System.Collections.Generic.SortedList`2" />에 대한 <see cref="T:System.IComparable`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsKey(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 특정 키가 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.ContainsValue(`1)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 특정 값이 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified value; otherwise, false.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 찾을 수 있는 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함된 키/값 쌍의 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 포함된 키/값 쌍의 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 대한 <see cref="T:System.Collections.Generic.KeyValuePair`2" /> 형식의 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfKey(`0)">
      <summary>지정한 키를 검색하고 전체 <see cref="T:System.Collections.Generic.SortedList`2" />에서 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>전체 <see cref="T:System.Collections.Generic.SortedList`2" />에 <paramref name="key" />가 있으면 해당 인덱스(0부터 시작)이고, 그렇지 않으면 -1입니다.</returns>
      <param name="key">
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.IndexOfValue(`1)">
      <summary>지정한 값을 검색하고 전체 <see cref="T:System.Collections.Generic.SortedList`2" />에서 이 값이 맨 처음 발견되는 인덱스(0부터 시작)를 반환합니다.</summary>
      <returns>
        <paramref name="value" />이 있으면 전체 <see cref="T:System.Collections.Generic.SortedList`2" />에서 맨 처음 발견되는 값의 0부터 시작하는 인덱스이고, 그렇지 않으면 -1입니다.</returns>
      <param name="value">
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 찾을 수 있는 값입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Item(`0)">
      <summary>지정된 키에 연결된 값을 가져오거나 설정합니다.</summary>
      <returns>지정한 키와 연결된 값입니다.지정한 키가 없으면 get 작업에서 <see cref="T:System.Collections.Generic.KeyNotFoundException" />을 throw하고 set 작업에서 지정한 키를 사용하여 새 요소를 만듭니다.</returns>
      <param name="key">가져오거나 설정할 값이 있는 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.Collections.Generic.KeyNotFoundException">속성을 검색할 때 컬렉션에 <paramref name="key" />가 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Keys">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에 있는 키가 들어있는 컬렉션을 정렬 순서대로 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.IList`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <returns>요소가 성공적으로 제거되면 true이고, 그렇지 않으면 false입니다.This method also returns false if <paramref name="key" /> was not found in the original <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.RemoveAt(System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />의 지정된 인덱스에 있는 요소를 제거합니다.</summary>
      <param name="index">제거할 요소의 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는<paramref name="index" />가 <see cref="P:System.Collections.Generic.SortedList`2.Count" />보다 크거나 같은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Add(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 키/값 쌍을 추가합니다.</summary>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에 추가할 <see cref="T:System.Collections.Generic.KeyValuePair`2" />입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Contains(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에 특정 요소가 들어 있는지 여부를 확인합니다.</summary>
      <returns>true if <paramref name="keyValuePair" /> is found in the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 찾을 <see cref="T:System.Collections.Generic.KeyValuePair`2" />입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#CopyTo(System.Collections.Generic.KeyValuePair{`0,`1}[],System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 복사한 요소의 대상인 일차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우 </exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작은 경우 </exception>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.ICollection`1" />의 요소 수가 <paramref name="arrayIndex" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.ICollection`1" />이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.SortedList`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#ICollection{T}#Remove(System.Collections.Generic.KeyValuePair{`0,`1})">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 맨 처음 발견되는 특정 키/값 쌍을 제거합니다.</summary>
      <returns>true if <paramref name="keyValuePair" /> was successfully removed from the <see cref="T:System.Collections.Generic.ICollection`1" />; otherwise, false.This method also returns false if <paramref name="keyValuePair" /> was not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.</returns>
      <param name="keyValuePair">
        <see cref="T:System.Collections.Generic.ICollection`1" />에서 제거할 <see cref="T:System.Collections.Generic.KeyValuePair`2" />입니다.</param>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Keys">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 키를 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IDictionary{TKey@TValue}#Values">
      <summary>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.IDictionary`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.ICollection`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Keys">
      <summary>읽기 전용 사전의 키를 포함하는 열거 가능한 컬렉션을 가져옵니다.</summary>
      <returns>읽기 전용 사전의 키를 포함하는 열거 가능한 컬렉션입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#Generic#IReadOnlyDictionary{TKey@TValue}#Values">
      <summary>읽기 전용 사전의 값을 포함하는 열거 가능한 컬렉션을 가져옵니다.</summary>
      <returns>읽기 전용 사전의 값을 포함하는 열거 가능한 컬렉션입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">The one-dimensional <see cref="T:System.Array" /> that is the destination of the elements copied from <see cref="T:System.Collections.ICollection" />.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">복사가 시작되는 <paramref name="array" />의 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" />가 0보다 작은 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" />가 다차원 배열인 경우또는<paramref name="array" />에 0부터 시작하는 인덱스가 없습니다.또는소스 <see cref="T:System.Collections.ICollection" />의 요소 수가 <paramref name="arrayIndex" />에서 대상 <paramref name="array" /> 끝까지 사용 가능한 공간보다 큰 경우또는소스 <see cref="T:System.Collections.ICollection" /> 형식을 대상 <paramref name="array" /> 형식으로 자동 캐스팅할 수 없는 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if access to the <see cref="T:System.Collections.ICollection" /> is synchronized (thread safe); otherwise, false.<see cref="T:System.Collections.Generic.SortedList`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.<see cref="T:System.Collections.Generic.SortedList`2" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Add(System.Object,System.Object)">
      <summary>제공된 키와 값을 가진 요소를 <see cref="T:System.Collections.IDictionary" />에 추가합니다.</summary>
      <param name="key">추가할 요소의 키로 사용하는 <see cref="T:System.Object" />입니다.</param>
      <param name="value">추가할 요소의 값으로 사용하는 <see cref="T:System.Object" />입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.IDictionary" />.또는<paramref name="value" />의 형식을 <see cref="T:System.Collections.IDictionary" />의 값 형식 <paramref name="TValue" />에 할당할 수 없는 경우또는같은 키를 가진 요소가 이미 <see cref="T:System.Collections.IDictionary" />에 있는 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Contains(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에 지정된 키를 가진 요소가 포함되어 있는지 여부를 확인합니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> contains an element with the key; otherwise, false.</returns>
      <param name="key">
        <see cref="T:System.Collections.IDictionary" />에서 찾을 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#GetEnumerator">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 <see cref="T:System.Collections.IDictionaryEnumerator" />를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />에 대한 <see cref="T:System.Collections.IDictionaryEnumerator" />입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsFixedSize">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 크기가 고정되어 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> has a fixed size; otherwise, false.<see cref="T:System.Collections.Generic.SortedList`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.IDictionary" />가 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.IDictionary" /> is read-only; otherwise, false.<see cref="T:System.Collections.Generic.SortedList`2" />의 기본 구현에서 이 속성은 언제나 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Item(System.Object)">
      <summary>지정한 키를 가진 요소를 가져오거나 설정합니다.</summary>
      <returns>The element with the specified key, or null if <paramref name="key" /> is not in the dictionary or <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</returns>
      <param name="key">가져오거나 설정할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">A value is being assigned, and <paramref name="key" /> is of a type that is not assignable to the key type <paramref name="TKey" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.또는A value is being assigned, and <paramref name="value" /> is of a type that is not assignable to the value type <paramref name="TValue" /> of the <see cref="T:System.Collections.Generic.SortedList`2" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Keys">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 키를 포함하는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />의 키를 포함하는 <see cref="T:System.Collections.ICollection" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Remove(System.Object)">
      <summary>
        <see cref="T:System.Collections.IDictionary" />에서 지정한 키를 가진 요소를 제거합니다.</summary>
      <param name="key">제거할 요소의 키입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.System#Collections#IDictionary#Values">
      <summary>
        <see cref="T:System.Collections.IDictionary" />의 값을 포함하는 <see cref="T:System.Collections.ICollection" />을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.IDictionary" />의 값을 포함하는 <see cref="T:System.Collections.ICollection" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />의 실제 요소 수가 현재 용량의 90% 미만인 경우 용량을 이 값으로 설정합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.SortedList`2.TryGetValue(`0,`1@)">
      <summary>지정한 키와 연결된 값을 가져옵니다.</summary>
      <returns>true if the <see cref="T:System.Collections.Generic.SortedList`2" /> contains an element with the specified key; otherwise, false.</returns>
      <param name="key">가져올 값이 있는 키입니다.</param>
      <param name="value">이 메서드가 반환될 때 지정한 키가 있으면 해당 키와 연결된 값이고, 그렇지 않으면 <paramref name="value" /> 매개 변수 형식의 기본값을 포함합니다.이 매개 변수는 초기화되지 않은 상태로 전달됩니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="key" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedList`2.Values">
      <summary>
        <see cref="T:System.Collections.Generic.SortedList`2" />의 값을 포함하는 컬렉션을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedList`2" />의 값을 포함하는 <see cref="T:System.Collections.Generic.IList`1" />입니다.</returns>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1">
      <summary>정렬된 순서대로 유지 관리되는 개체의 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">집합에 있는 요소의 형식입니다.</typeparam>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 클래스의 새 인스턴스를 초기화합니다. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IComparer{`0})">
      <summary>지정된 비교자를 사용하는 <see cref="T:System.Collections.Generic.SortedSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="comparer">개체 비교에 사용할 기본 비교자입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="comparer" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정한 열거 가능한 컬렉션에서 복사된 요소를 포함하는 <see cref="T:System.Collections.Generic.SortedSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">복사할 열거 가능한 컬렉션입니다. </param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.#ctor(System.Collections.Generic.IEnumerable{`0},System.Collections.Generic.IComparer{`0})">
      <summary>지정한 열거 가능한 컬렉션에서 복사된 요소를 포함하고 지정된 비교자를 사용하는 <see cref="T:System.Collections.Generic.SortedSet`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">복사할 열거 가능한 컬렉션입니다. </param>
      <param name="comparer">개체 비교에 사용할 기본 비교자입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Add(`0)">
      <summary>집합에 요소를 추가하고 성공적으로 추가되었는지 여부를 나타내는 값을 반환합니다.</summary>
      <returns>
        <paramref name="item" />이 집합에 추가되었으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="item">집합에 추가할 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Clear">
      <summary>집합에서 요소를 모두 제거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Comparer">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />의 값이 같은지 확인하는 데 사용되는 <see cref="T:System.Collections.Generic.IEqualityComparer`1" /> 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" />의 값이 같은지 확인하는 데 사용되는 비교자입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Contains(`0)">
      <summary>집합에 특정 요소가 들어 있는지 여부를 확인합니다.</summary>
      <returns>집합에 <paramref name="item" />이 들어 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">집합에서 찾을 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[])">
      <summary>대상 배열의 처음부터 시작하여 전체 <see cref="T:System.Collections.Generic.SortedSet`1" />를 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 복사한 요소의 대상인 1차원 배열입니다.</param>
      <exception cref="T:System.ArgumentException">소스 <see cref="T:System.Collections.Generic.SortedSet`1" />의 요소의 수가 대상 배열이 포함할 수 있는 요소의 수를 초과하는 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32)">
      <summary>지정된 배열 인덱스에서 시작하여 전체 <see cref="T:System.Collections.Generic.SortedSet`1" />를 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentException">소스 배열의 요소 수가 <paramref name="index" />에서 대상 배열 끝까지의 사용 가능한 공간보다 큰 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.CopyTo(`0[],System.Int32,System.Int32)">
      <summary>지정된 배열 인덱스에서 시작하여 <see cref="T:System.Collections.Generic.SortedSet`1" />에 있는 지정된 개수의 요소를 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <param name="count">복사할 요소의 수입니다.</param>
      <exception cref="T:System.ArgumentException">소스 배열의 요소 수가 <paramref name="index" />에서 대상 배열 끝까지의 사용 가능한 공간보다 큰 경우</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우또는<paramref name="count" />가 0보다 작은 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />의 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" />의 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.ExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체에서 지정된 컬렉션에 있는 모든 요소를 제거합니다.</summary>
      <param name="other">
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체에서 제거할 항목 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />을 반복하는 열거자를 반환합니다.</summary>
      <returns>정렬된 순서로 <see cref="T:System.Collections.Generic.SortedSet`1" />을 반복하는 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.GetViewBetween(`0,`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />에 있는 하위 집합의 뷰를 반환합니다.</summary>
      <returns>지정된 범위의 값만 포함하는 하위 집합 뷰입니다.</returns>
      <param name="lowerValue">뷰에서 원하는 가장 낮은 값입니다.</param>
      <param name="upperValue">뷰에서 원하는 가장 높은 값입니다. </param>
      <exception cref="T:System.ArgumentException">비교자에 따르면 <paramref name="lowerValue" />은 <paramref name="upperValue" />보다 큽니다.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">뷰에서 시도한 작업이 <paramref name="lowerValue" /> 및 <paramref name="upperValue" />에 의해 지정된 범위를 벗어났습니다.</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IntersectWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에도 있는 요소만 포함되도록 현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체를 수정합니다.</summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 지정된 컬렉션의 진 부분 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 <paramref name="other" />의 진 부분 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsProperSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 지정된 컬렉션의 진 상위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 <paramref name="other" />의 진 포함 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSubsetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 지정된 컬렉션의 하위 집합인지 확인합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 <paramref name="other" />의 하위 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.IsSupersetOf(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 지정된 컬렉션의 상위 집합인지 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 <paramref name="other" />의 포함 집합이면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다. </param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Max">
      <summary>비교자로 정의된 <see cref="T:System.Collections.Generic.SortedSet`1" />의 최대값을 가져옵니다.</summary>
      <returns>집합의 최대값입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Min">
      <summary>비교자로 정의된 <see cref="T:System.Collections.Generic.SortedSet`1" />의 최소값을 가져옵니다.</summary>
      <returns>집합의 최소값입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Overlaps(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 지정된 컬렉션이 공통 요소를 공유하는지 여부를 확인합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 <paramref name="other" />가 최소한 하나의 요소를 공유하면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Remove(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 지정된 항목을 제거합니다.</summary>
      <returns>요소를 찾아서 성공적으로 제거했으면 true이고, 그렇지 않으면 false입니다. </returns>
      <param name="item">제거할 요소입니다.</param>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.RemoveWhere(System.Predicate{`0})">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 지정된 조건자로 정의된 조건과 일치하는 요소를 모두 제거합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 컬렉션에서 제거된 요소 수입니다. </returns>
      <param name="match">제거할 요소의 조건을 정의하는 대리자입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="match" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Reverse">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" />를 역순으로 반복하는 <see cref="T:System.Collections.Generic.IEnumerable`1" />을 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.SortedSet`1" />를 역순으로 반복하는 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SetEquals(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 지정된 컬렉션에 같은 요소가 들어 있는지 여부를 확인합니다.</summary>
      <returns>현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체가 <paramref name="other" />와 같으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.SymmetricExceptWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 개체나 지정된 컬렉션에 있고 둘 다에는 없는 요소만 포함되도록 현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체를 수정합니다.</summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#Add(`0)">
      <summary>
        <see cref="T:System.Collections.Generic.ICollection`1" /> 개체에 항목을 추가합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.ICollection`1" /> 개체에 추가할 개체입니다.</param>
      <exception cref="T:System.NotSupportedException">
        <see cref="T:System.Collections.Generic.ICollection`1" />가 읽기 전용인 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#Generic#ICollection{T}#IsReadOnly">
      <summary>
        <see cref="T:System.Collections.ICollection" />이 읽기 전용인지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>컬렉션이 읽기 전용이면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>지정된 배열 인덱스에서 시작하여 전체 <see cref="T:System.Collections.Generic.SortedSet`1" />를 호환되는 1차원 배열에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.SortedSet`1" />에서 복사한 요소의 대상인 1차원 배열입니다.배열에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="index">
        <paramref name="array" />에서 복사가 시작되는 인덱스(0부터 시작)입니다.</param>
      <exception cref="T:System.ArgumentException">소스 배열의 요소 수가 <paramref name="index" />에서 대상 배열 끝까지의 사용 가능한 공간보다 큰 경우 </exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="index" />가 0보다 작은 경우</exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되었는지 여부, 즉 스레드로부터 안전한지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되었으면 true이고, 그렇지 않으면 false입니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.<see cref="T:System.Collections.Generic.Dictionary`2.KeyCollection" />의 기본 구현에서 이 속성은 언제나 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션을 반복하는 데 사용할 수 있는 열거자입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.UnionWith(System.Collections.Generic.IEnumerable{`0})">
      <summary>현재 개체 또는 지정된 컬렉션 중 하나에 있는 요소가 모두 포함되도록 현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체를 수정합니다. </summary>
      <param name="other">현재 <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체와 비교할 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="other" />가 null인 경우</exception>
    </member>
    <member name="T:System.Collections.Generic.SortedSet`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1" /> 개체의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.SortedSet`1.Enumerator" />에서 사용하는 모든 리소스를 해제합니다. </summary>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.SortedSet`1" /> 컬렉션의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.SortedSet`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="T:System.Collections.Generic.Stack`1">
      <summary>지정한 동일 형식의 인스턴스로 이루어진 가변 크기 LIFO(후입선출) 방식의 컬렉션을 나타냅니다.</summary>
      <typeparam name="T">스택에 있는 요소의 형식을 지정합니다.</typeparam>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor">
      <summary>비어 있는 상태에서 기본 초기 용량을 가지는 <see cref="T:System.Collections.Generic.Stack`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Collections.Generic.IEnumerable{`0})">
      <summary>지정된 컬렉션에서 복사한 요소를 포함하고 복사한 요소를 모두 수용할 수 있을 정도의 용량을 가진 <see cref="T:System.Collections.Generic.Stack`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="collection">요소를 복사할 원본 컬렉션입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="collection" /> is null.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.#ctor(System.Int32)">
      <summary>비어 있는 상태이고 지정한 초기 용량과 기본 초기 용량 중에서 더 큰 용량을 가지는 <see cref="T:System.Collections.Generic.Stack`1" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="capacity">
        <see cref="T:System.Collections.Generic.Stack`1" />에 포함될 수 있는 초기 요소 수입니다.</param>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="capacity" /> is less than zero.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Clear">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />에서 개체를 모두 제거합니다.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Contains(`0)">
      <summary>요소가 <see cref="T:System.Collections.Generic.Stack`1" />에 있는지를 확인합니다.</summary>
      <returns>
        <paramref name="item" />가 <see cref="T:System.Collections.Generic.Stack`1" />에 있으면 true이고, 그렇지 않으면 false입니다.</returns>
      <param name="item">
        <see cref="T:System.Collections.Generic.Stack`1" />에서 찾을 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.CopyTo(`0[],System.Int32)">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />을 지정한 배열 인덱스에서 시작하여 기존의 1차원 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.Generic.Stack`1" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">The number of elements in the source <see cref="T:System.Collections.Generic.Stack`1" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Count">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />에 포함된 요소 수를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />에 포함된 요소 수입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.GetEnumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />에 대한 열거자를 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />에 대한 <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Peek">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />의 맨 위에서 개체를 제거하지 않고 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />의 맨 위에 있는 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Pop">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />의 맨 위에서 개체를 제거하고 반환합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />의 맨 위에서 제거되는 개체입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The <see cref="T:System.Collections.Generic.Stack`1" /> is empty.</exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Push(`0)">
      <summary>개체를 <see cref="T:System.Collections.Generic.Stack`1" />의 맨 위에 삽입합니다.</summary>
      <param name="item">
        <see cref="T:System.Collections.Generic.Stack`1" />에 적용할 개체입니다.참조 형식에 대해 값은 null이 될 수 있습니다.</param>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#Generic#IEnumerable{T}#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.Generic.IEnumerator`1" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#ICollection#CopyTo(System.Array,System.Int32)">
      <summary>특정 <see cref="T:System.Array" /> 인덱스부터 시작하여 <see cref="T:System.Collections.ICollection" />의 요소를 <see cref="T:System.Array" />에 복사합니다.</summary>
      <param name="array">
        <see cref="T:System.Collections.ICollection" />에서 복사한 요소의 대상인 1차원 <see cref="T:System.Array" />입니다.<see cref="T:System.Array" />에는 0부터 시작하는 인덱스가 있어야 합니다.</param>
      <param name="arrayIndex">
        <paramref name="array" />에서 복사가 시작되는 0부터 시작하는 인덱스입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="arrayIndex" /> is less than zero.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="array" /> is multidimensional.-or-<paramref name="array" /> does not have zero-based indexing.-or-The number of elements in the source <see cref="T:System.Collections.ICollection" /> is greater than the available space from <paramref name="arrayIndex" /> to the end of the destination <paramref name="array" />.-or-The type of the source <see cref="T:System.Collections.ICollection" /> cannot be cast automatically to the type of the destination <paramref name="array" />.</exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#IsSynchronized">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되는지를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스가 동기화되어 스레드로부터 안전하게 보호되면 true이고, 그렇지 않으면 false입니다.<see cref="T:System.Collections.Generic.Stack`1" />의 기본 구현에서 이 속성은 항상 false를 반환합니다.</returns>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.System#Collections#ICollection#SyncRoot">
      <summary>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.ICollection" />에 대한 액세스를 동기화하는 데 사용할 수 있는 개체입니다.<see cref="T:System.Collections.Generic.Stack`1" />의 기본 구현에서 이 속성은 항상 현재 인스턴스를 반환합니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.System#Collections#IEnumerable#GetEnumerator">
      <summary>컬렉션을 반복하는 열거자를 반환합니다.</summary>
      <returns>컬렉션에서 반복하는 데 사용할 수 있는 <see cref="T:System.Collections.IEnumerator" />입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.ToArray">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />을 새 배열에 복사합니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />의 요소 복사본을 포함하는 새 배열입니다.</returns>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.TrimExcess">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />의 실제 요소 수가 현재 용량의 90% 미만인 경우 용량을 이 값으로 설정합니다.</summary>
    </member>
    <member name="T:System.Collections.Generic.Stack`1.Enumerator">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1" />의 요소를 열거합니다.</summary>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>
        <see cref="T:System.Collections.Generic.Stack`1" />에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.Dispose">
      <summary>
        <see cref="T:System.Collections.Generic.Stack`1.Enumerator" />에서 사용하는 모든 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.MoveNext">
      <summary>열거자를 <see cref="T:System.Collections.Generic.Stack`1" />의 다음 요소로 이동합니다.</summary>
      <returns>열거자가 다음 요소로 이동한 경우 true가 반환되고, 컬렉션의 끝을 지난 경우 false가 반환됩니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
    <member name="P:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Current">
      <summary>열거자의 현재 위치에 있는 요소를 가져옵니다.</summary>
      <returns>컬렉션에서 열거자의 현재 위치에 있는 요소입니다.</returns>
      <exception cref="T:System.InvalidOperationException">열거자가 컬렉션의 첫 번째 요소 앞 또는 마지막 요소 뒤에 배치되는 경우 </exception>
    </member>
    <member name="M:System.Collections.Generic.Stack`1.Enumerator.System#Collections#IEnumerator#Reset">
      <summary>컬렉션의 첫 번째 요소 앞의 초기 위치에 열거자를 설정합니다.이 클래스는 상속될 수 없습니다.</summary>
      <exception cref="T:System.InvalidOperationException">열거자가 만들어진 후 컬렉션이 수정된 경우 </exception>
    </member>
  </members>
</doc>