﻿<?xml version="1.0" encoding="utf-8"?>
<package xmlns="http://schemas.microsoft.com/packaging/2013/05/nuspec.xsd">
  <metadata minClientVersion="2.12">
    <id>System.Collections</id>
    <version>4.3.0</version>
    <title>System.Collections</title>
    <authors>Microsoft</authors>
    <owners>microsoft,dotnetframework</owners>
    <requireLicenseAcceptance>true</requireLicenseAcceptance>
    <licenseUrl>http://go.microsoft.com/fwlink/?LinkId=329770</licenseUrl>
    <projectUrl>https://dot.net/</projectUrl>
    <iconUrl>http://go.microsoft.com/fwlink/?LinkID=288859</iconUrl>
    <description>Provides classes that define generic collections, which allow developers to create strongly typed collections that provide better type safety and performance than non-generic strongly typed collections.

Commonly Used Types:
System.Collections.Generic.List&lt;T&gt;
System.Collections.Generic.Dictionary&lt;<PERSON><PERSON>ey, TValue&gt;
System.Collections.Generic.Queue&lt;T&gt;
System.Collections.Generic.Stack&lt;T&gt;
System.Collections.Generic.HashSet&lt;T&gt;
System.Collections.Generic.LinkedList&lt;T&gt;
System.Collections.Generic.EqualityComparer&lt;T&gt;
System.Collections.Generic.Comparer&lt;T&gt;
System.Collections.Generic.SortedDictionary&lt;TKey, TValue&gt;
 
When using NuGet 3.x this package requires at least version 3.4.</description>
    <releaseNotes>https://go.microsoft.com/fwlink/?LinkID=799421</releaseNotes>
    <copyright>© Microsoft Corporation.  All rights reserved.</copyright>
    <serviceable>true</serviceable>
    <dependencies>
      <group targetFramework="MonoAndroid1.0" />
      <group targetFramework="MonoTouch1.0" />
      <group targetFramework=".NETFramework4.5" />
      <group targetFramework=".NETCore5.0">
        <dependency id="Microsoft.NETCore.Platforms" version="1.1.0" />
        <dependency id="Microsoft.NETCore.Targets" version="1.1.0" />
        <dependency id="System.Runtime" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.0">
        <dependency id="Microsoft.NETCore.Platforms" version="1.1.0" />
        <dependency id="Microsoft.NETCore.Targets" version="1.1.0" />
        <dependency id="System.Runtime" version="4.3.0" />
      </group>
      <group targetFramework=".NETStandard1.3">
        <dependency id="Microsoft.NETCore.Platforms" version="1.1.0" />
        <dependency id="Microsoft.NETCore.Targets" version="1.1.0" />
        <dependency id="System.Runtime" version="4.3.0" />
      </group>
      <group targetFramework=".NETPortable0.0-Profile259" />
      <group targetFramework="Windows8.0" />
      <group targetFramework="WindowsPhone8.0" />
      <group targetFramework="WindowsPhoneApp8.1" />
      <group targetFramework="Xamarin.iOS1.0" />
      <group targetFramework="Xamarin.Mac2.0" />
      <group targetFramework="Xamarin.TVOS1.0" />
      <group targetFramework="Xamarin.WatchOS1.0" />
    </dependencies>
    <frameworkAssemblies>
      <frameworkAssembly assemblyName="System" targetFramework=".NETFramework4.5" />
      <frameworkAssembly assemblyName="System.Core" targetFramework=".NETFramework4.5" />
    </frameworkAssemblies>
  </metadata>
</package>