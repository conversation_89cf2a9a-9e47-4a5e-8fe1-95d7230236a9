﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Diagnostics.Tracing</name>
  </assembly>
  <members>
    <member name="T:System.Diagnostics.Tracing.EventActivityOptions">
      <summary>Spécifie le suivi de l'activité de démarrer et arrêter des événements. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Detachable">
      <summary>Autoriser le chevauchement d'activités.Par défaut, les démarrages et les arrêts d'activités doivent être imbriqués au niveau des propriétés.Autrement dit, une séquence Démarrer A, Démarrer B, Arrêter A, Arrêter B n'est pas autorisée. Ceci fera que B s'arrêtera en même temps que A.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Disable">
      <summary>Désactivation du démarrage et arrêter le suivi. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.None">
      <summary>Utiliser le comportement par défaut pour le suivi des démarrages et des arrêts.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventActivityOptions.Recursive">
      <summary>Autoriser les démarrages d'activités récursives.Par défaut, une activité ne peut pas être récursive.Autrement dit, une séquence Démarrer A, Démarrer A, Arrêter A, Arrêter A n'est pas autorisée.Des activités récursives non intentionnelles peuvent se produire si l'application s'exécute et que pour certaines activités, l'arrêt n'est pas atteint avant qu'une autre activité soit appelée.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventAttribute">
      <summary>Spécifie les informations de schéma d'événement supplémentaires pour un événement.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventAttribute.#ctor(System.Int32)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> avec l'identificateur d'événement spécifié.</summary>
      <param name="eventId">Identificateur de l'événement.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.ActivityOptions">
      <summary>Spécifie le comportement des événements de démarrage et d'arrêt d'une activité.Une activité est la période de temps dans une application entre le démarrage et l'arrêt.</summary>
      <returns>Retourne <see cref="T:System.Diagnostics.Tracing.EventActivityOptions" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Channel">
      <summary>Obtient ou définit un journal des événements supplémentaire dans lequel l'événement doit être écrit.</summary>
      <returns>Journal des événements supplémentaire dans lequel l'événement doit être écrit.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.EventId">
      <summary>Obtient ou définit l'identificateur pour l'événement.</summary>
      <returns>Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Keywords">
      <summary>Obtient ou définit les mots clés pour l'événement.</summary>
      <returns>Combinaison d'opérations de bits des valeurs d'énumération.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Level">
      <summary>Obtient ou définit le niveau de l'événement.</summary>
      <returns>L'une des valeurs d'énumération qui spécifie le niveau de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Message">
      <summary>Obtient ou définit le message pour l'événement.</summary>
      <returns>Message pour l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Opcode">
      <summary>Obtient ou définit le code d'opération pour l'événement.</summary>
      <returns>L'une des valeurs d'énumération qui spécifie le code d'opération.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Tags">
      <summary>Obtient et définit les <see cref="T:System.Diagnostics.Tracing.EventTags" /> valeur <see cref="T:System.Diagnostics.Tracing.EventAttribute" /> objet.Une balise d'événement est une valeur définie par l'utilisateur qui est passée directement quand l'événement est consigné.</summary>
      <returns>Retourne la valeur <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Task">
      <summary>Obtient ou définit la tâche de l'événement.</summary>
      <returns>Tâche de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventAttribute.Version">
      <summary>Obtient ou définit la version de l'événement.</summary>
      <returns>Version de l'événement.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventChannel">
      <summary>Spécifie le canal de journal des événements pour l'événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Admin">
      <summary>Canal de journal d'administrateur.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Analytic">
      <summary>Canal d'analyse.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Debug">
      <summary>Canal de débogage.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.None">
      <summary>Aucun canal spécifié.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventChannel.Operational">
      <summary>Canal opérationnel. </summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommand">
      <summary>Décrit la commande (propriété <see cref="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command" /> ) qui est passée au rappel de <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" /> .</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Disable">
      <summary>Désactive l'événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Enable">
      <summary>Active l'événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.SendManifest">
      <summary>Envoie le manifeste.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventCommand.Update">
      <summary>Met à jour l'événement.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventCommandEventArgs">
      <summary>Fournit les arguments pour le rappel <see cref="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Arguments">
      <summary>Obtient le tableau des arguments pour le rappel.</summary>
      <returns>Tableau d'arguments de rappel.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventCommandEventArgs.Command">
      <summary>Obtient la commande de rappel.</summary>
      <returns>Commande de rappel.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.DisableEvent(System.Int32)">
      <summary>Désactive l'événement avec l'identificateur spécifié.</summary>
      <returns>true si <paramref name="eventId" /> se situe dans la plage ; sinon false.</returns>
      <param name="eventId">Identificateur de l'événement à désactiver.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventCommandEventArgs.EnableEvent(System.Int32)">
      <summary>Active l'événement avec l'identificateur spécifié.</summary>
      <returns>true si <paramref name="eventId" /> se situe dans la plage ; sinon false.</returns>
      <param name="eventId">Identificateur de l'événement à activer.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventDataAttribute">
      <summary>Spécifie un type à passer à la <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> (méthode).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventDataAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />. </summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventDataAttribute.Name">
      <summary>Obtient ou définit le nom à appliquer à un événement si le type d'événement ou la propriété n'est pas nommé de manière explicite.</summary>
      <returns>Nom à appliquer à l'événement ou à la propriété.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldAttribute">
      <summary>Le <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" /> est placé dans les champs de types définis par l'utilisateur qui sont passés en tant que <see cref="T:System.Diagnostics.Tracing.EventSource" /> charges utiles. </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventFieldAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Format">
      <summary>Obtient et définit la valeur qui spécifie comment mettre en forme la valeur d'un type défini par l'utilisateur.</summary>
      <returns>Retourne une valeur <see cref="T:System.Diagnostics.Tracing.EventFieldFormat" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventFieldAttribute.Tags">
      <summary>Obtient et définit défini par l'utilisateur <see cref="T:System.Diagnostics.Tracing.EventFieldTags" /> valeur est requise pour les champs qui contiennent des données qui ne sont pas pris en charge. </summary>
      <returns>Retourne <see cref="T:System.Diagnostics.Tracing.EventFieldTags" />.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldFormat">
      <summary>Spécifie comment mettre en forme la valeur d'un type défini par l'utilisateur et comment cela peut être utilisé pour remplacer la mise en forme par défaut pour un champ.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Boolean">
      <summary>Boolean</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Default">
      <summary>Par défaut.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Hexadecimal">
      <summary>Hexadécimal</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.HResult">
      <summary>HResult</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Json">
      <summary>JSON</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.String">
      <summary>Chaîne.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldFormat.Xml">
      <summary>XML</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventFieldTags">
      <summary>Spécifie la balise définie par l'utilisateur qui est placée sur des champs de types définis par l'utilisateur qui sont passés en tant que <see cref="T:System.Diagnostics.Tracing.EventSource" /> charges via le <see cref="T:System.Diagnostics.Tracing.EventFieldAttribute" />. </summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventFieldTags.None">
      <summary>Spécifie qu'il n'y a aucune balise et est égal à zéro.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventIgnoreAttribute">
      <summary>Spécifie une propriété doit être ignorée lors de l'écriture d'un type d'événement avec le <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)" /> (méthode).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventIgnoreAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventIgnoreAttribute" />.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventKeywords">
      <summary>Définit les mots clés standard qui s'appliquent aux événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.All">
      <summary>Tous les bits sont définis à 1, représentant chaque groupe d'événements possible.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditFailure">
      <summary>Joint à tous les événements d'audit de sécurité ayant échoué.Utilisez ce mot clé uniquement pour les événements du journal de sécurité.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.AuditSuccess">
      <summary>Joint à tous les événements d'audit de sécurité réussi.Utilisez ce mot clé uniquement pour les événements du journal de sécurité.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.CorrelationHint">
      <summary>Joint aux événements de transfert dont l'ID d'activité (ID de corrélation) associé est une valeur calculée et dont l'unicité n'est pas garantie (c'est-à-dire qu'il ne s'agit pas d'un vrai GUID).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.EventLogClassic">
      <summary>Joint aux événements qui sont déclenchés par l'utilisation de la fonction RaiseEvent.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.None">
      <summary>Aucun filtre sur les mots clés n'est exécuté lorsque l'événement est publié.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.Sqm">
      <summary>Joint à tous les événements du mécanisme de qualité de service (SQM, Service Quality Mechanism).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiContext">
      <summary>Joint à tous les événements de contexte de l'infrastructure de diagnostics Windows (WDI, Windows Diagnostics Infrastructure).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventKeywords.WdiDiagnostic">
      <summary>Joint à tous les événements de diagnostic de l'infrastructure de diagnostics Windows (WDI, Windows Diagnostics Infrastructure).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventLevel">
      <summary>Identifie le niveau d'un événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Critical">
      <summary>Ce niveau correspond à une erreur critique (une erreur grave ayant provoqué une panne importante).</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Error">
      <summary>Ce niveau correspond aux erreurs standard qui signifient l'existence d'un problème.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Informational">
      <summary>Ce niveau ajoute des événements d'information ou des messages qui ne signalent pas des erreurs.Ces événements peuvent aider à suivre la progression ou l'état d'une application.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.LogAlways">
      <summary>Aucun filtrage de niveau ne s'effectue sur l'événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Verbose">
      <summary>Ce niveau ajoute des événements ou des messages longs.Il provoque l'enregistrement de tous les événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventLevel.Warning">
      <summary>Ce niveau ajoute des événements d'avertissement (par exemple, des événements publiés parce que l'utilisation d'un disque approche de sa capacité totale).</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventListener">
      <summary>Fournit les méthodes d'activation et de désactivation des événements à partir des sources d'événements.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.DisableEvents(System.Diagnostics.Tracing.EventSource)">
      <summary>Désactive tous les événements de la source d'événements spécifiée.</summary>
      <param name="eventSource">Source d'événements pour laquelle désactiver les événements.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.Dispose">
      <summary>Libère les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Diagnostics.Tracing.EventListener" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel)">
      <summary>Active les événements pour la source d'événements spécifiée qui a le niveau de commentaires spécifié ou un niveau inférieur.</summary>
      <param name="eventSource">Source d'événements pour laquelle activer les événements.</param>
      <param name="level">Niveau des événements à activer.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Active les événements pour la source d'événements spécifiée qui a le niveau de commentaires spécifié ou un niveau inférieur, et les indicateurs de mot clé de correspondance.</summary>
      <param name="eventSource">Source d'événements pour laquelle activer les événements.</param>
      <param name="level">Niveau des événements à activer.</param>
      <param name="matchAnyKeyword">Indicateurs de mots clés nécessaires pour activer les événements.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EnableEvents(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Active les événements pour la source d'événements spécifiée qui a le niveau de commentaires spécifié ou un niveau inférieur, les indicateurs de mot clé de correspondance et les arguments de correspondance.</summary>
      <param name="eventSource">Source d'événements pour laquelle activer les événements.</param>
      <param name="level">Niveau des événements à activer.</param>
      <param name="matchAnyKeyword">Indicateurs de mots clés nécessaires pour activer les événements.</param>
      <param name="arguments">Arguments à faire correspondre pour activer les événements.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.EventSourceIndex(System.Diagnostics.Tracing.EventSource)">
      <summary>Obtient un petit nombre non négatif qui représente la source d'événements spécifiée.</summary>
      <returns>Petit nombre non négatif qui représente la source d'événements spécifiée.</returns>
      <param name="eventSource">Source d'événements dont l'index doit être recherché.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventSourceCreated(System.Diagnostics.Tracing.EventSource)">
      <summary>Appelée pour toutes les sources d'événements existantes lorsque l'écouteur d'événements est créé et lorsqu'une nouvelle source d'événements est attachée à l'écouteur.</summary>
      <param name="eventSource">Source de l'événement.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)">
      <summary>Appelée lorsqu'un événement a été écrit par une source d'événements pour laquelle l'écouteur d'événements a des événements activés.</summary>
      <param name="eventData">Arguments d'événement qui décrivent cet événement.</param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventManifestOptions">
      <summary>Indique comment est généré le manifeste ETW pour la source d'événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllCultures">
      <summary>Génère un nœud de ressources sous le dossier de localisation pour chaque assembly satellite fourni.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.AllowEventSourceOverride">
      <summary>Remplace le comportement par défaut qui actuel <see cref="T:System.Diagnostics.Tracing.EventSource" /> doit être la classe de base du type défini par l'utilisateur passée à la méthode write.Cela permet la validation des sources d'événements .NET.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.None">
      <summary>Aucune option n'est spécifiée.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.OnlyIfNeededForRegistration">
      <summary>Un manifeste est généré. Seule la source d'événements doit être enregistrée sur l'ordinateur hôte.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventManifestOptions.Strict">
      <summary>Lève une exception en cas d'incohérence lors de l'écriture du fichier manifeste.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventOpcode">
      <summary>Définit les codes d'opération standard que la source d'événement joint aux événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStart">
      <summary>Événement de démarrage d'une collecte de données de trace.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.DataCollectionStop">
      <summary>Événement d'arrêt d'une collecte de données de trace.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Extension">
      <summary>Evénement d'extension.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Info">
      <summary>Événement d'informations.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Receive">
      <summary>Événement qui est publié lorsqu'une activité dans une application reçoit des données.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Reply">
      <summary>Événement qui est publié après qu'une activité dans une application a répondu à un événement.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Resume">
      <summary>Événement qui est publié après qu'une activité dans une application a repris à partir d'un état suspendu.L'événement doit suivre un événement portant le code d'opération <see cref="F:System.Diagnostics.Tracing.EventOpcode.Suspend" /> .</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Send">
      <summary>Événement qui est publié lorsqu'une activité dans une application transfère des données ou des ressources système à une autre activité.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Start">
      <summary>Événement qui est publié lorsqu'une application démarre une nouvelle transaction ou activité.Ce code d'opération peut être incorporé à une autre transaction ou activité lorsque plusieurs événements comprenant le code <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> se suivent sans événement intervenant comprenant le code <see cref="F:System.Diagnostics.Tracing.EventOpcode.Stop" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Stop">
      <summary>Événement qui est publié lorsqu'une activité ou une transaction dans une application se termine.L'événement correspond au dernier événement non apparié qui porte le code d'opération <see cref="F:System.Diagnostics.Tracing.EventOpcode.Start" /> .</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventOpcode.Suspend">
      <summary>Événement qui est publié lorsqu'une activité dans une application est suspendue.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource">
      <summary>Permet de créer des événements pour le suivi d'événements pour Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Boolean)">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> et spécifie si une exception doit être levée lorsqu'une erreur se produit dans le code Windows sous-jacent.</summary>
      <param name="throwOnEventWriteErrors">true pour lever une exception lorsqu'une erreur est détectée dans le code Windows sous-jacent ; sinon, false.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> avec les paramètres de configuration spécifiés.</summary>
      <param name="settings">Combinaison d'opérations de bits des valeurs d'énumération qui spécifient les paramètres de configuration à appliquer à la source d'événements.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Initialise une nouvelle instance de <see cref="T:System.Diagnostics.Tracing.EventSource" /> à utiliser avec les événements non liés à un contrat qui contiennent les paramètres et les caractéristiques spécifiés.</summary>
      <param name="settings">Combinaison d'opérations de bits des valeurs d'énumération qui spécifient les paramètres de configuration à appliquer à la source d'événements.</param>
      <param name="traits">Paires clé-valeur qui spécifient les caractéristiques de la source de l'événement.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String)">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> avec le nom spécifié.</summary>
      <param name="eventSourceName">Nom à appliquer à la source d'événements.Ne doit pas être null.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings)">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> avec le nom et les paramètres spécifiés.</summary>
      <param name="eventSourceName">Nom à appliquer à la source d'événements.Ne doit pas être null.</param>
      <param name="config">Combinaison d'opérations de bits des valeurs d'énumération qui spécifient les paramètres de configuration à appliquer à la source d'événements.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.#ctor(System.String,System.Diagnostics.Tracing.EventSourceSettings,System.String[])">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> avec les paramètres de configuration spécifiés.</summary>
      <param name="eventSourceName">Nom à appliquer à la source d'événements.Ne doit pas être null.</param>
      <param name="config">Combinaison d'opérations de bits des valeurs d'énumération qui spécifient les paramètres de configuration à appliquer à la source d'événements.</param>
      <param name="traits">Paires clé-valeur qui spécifient les caractéristiques de la source de l'événement.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventSourceName" /> is null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="traits" /> is not specified in key-value pairs.</exception>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.ConstructionException">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Obtient les exceptions levées lors de la construction de la source d'événements.</summary>
      <returns>L'exception a été levée lors de la construction de la source d'événements, ou null si aucune exception n'a été levée. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.CurrentThreadActivityId">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Obtient l'ID d'activité du thread actif. </summary>
      <returns>ID d'activité du thread actif. </returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose">
      <summary>Libère toutes les ressources utilisées par l'instance actuelle de la classe <see cref="T:System.Diagnostics.Tracing.EventSource" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Dispose(System.Boolean)">
      <summary>Libère les ressources non managées utilisées par la classe <see cref="T:System.Diagnostics.Tracing.EventSource" /> et libère éventuellement les ressources managées.</summary>
      <param name="disposing">true pour libérer les ressources managées et non managées ; false pour ne libérer que les ressources non managées. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Finalize">
      <summary>Permet à l'objet <see cref="T:System.Diagnostics.Tracing.EventSource" /> de tenter de libérer des ressources et d'effectuer d'autres opérations de nettoyage avant que l'objet ne soit récupéré par le garbage collection.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String)">
      <summary>Retourne une chaîne du manifeste XML associé à la source d'événements actuelle.</summary>
      <returns>Chaîne de données XML.</returns>
      <param name="eventSourceType">Type de l'événement source.</param>
      <param name="assemblyPathToIncludeInManifest">Chemin d'accès au fichier d'assembly (.dll) à inclure dans l'élément provider du manifeste. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GenerateManifest(System.Type,System.String,System.Diagnostics.Tracing.EventManifestOptions)">
      <summary>Retourne une chaîne du manifeste XML associé à la source d'événements actuelle.</summary>
      <returns>Chaîne de données XML ou null (voir les notes).</returns>
      <param name="eventSourceType">Type de l'événement source.</param>
      <param name="assemblyPathToIncludeInManifest">Chemin d'accès au fichier d'assembly (.dll) à inclure dans l'élément provider du manifeste. </param>
      <param name="flags">Combinaison d'opérations de bits des valeurs d'énumération qui spécifient comment le manifeste est généré.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetGuid(System.Type)">
      <summary>Obtient l'identificateur unique de cette implémentation de la source d'événements.</summary>
      <returns>Identificateur unique pour ce type de source d'événements.</returns>
      <param name="eventSourceType">Type de l'événement source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetName(System.Type)">
      <summary>Obtient le nom convivial de la source d'événements.</summary>
      <returns>Nom convivial de la source d'événements.La valeur par défaut est le nom simple de la classe.</returns>
      <param name="eventSourceType">Type de l'événement source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetSources">
      <summary>Obtient un instantané de toutes les sources d'événements pour le domaine d'application.</summary>
      <returns>Énumération de toutes les sources d'événements dans le domaine d'application.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.GetTrait(System.String)">
      <summary>Obtient la valeur de la caractéristique associée à la clé spécifiée.</summary>
      <returns>Valeur de caractéristique associée à la clé spécifiée.Si la clé est introuvable, retourne null.</returns>
      <param name="key">Clé de la caractéristique à obtenir.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Guid">
      <summary>Identificateur unique de la source d'événement.</summary>
      <returns>Identificateur unique de la source d'événement.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled">
      <summary>Détermine si la source d'événements actuelle est activée.</summary>
      <returns>true si la source d'événements actuelle est activée ; sinon, false.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords)">
      <summary>Détermine si la source d'événements actuelle qui a le niveau et le mot clé spécifiés est activée.</summary>
      <returns>true si la source d'événements est activée ; sinon, false.</returns>
      <param name="level">Niveau de la source d'événements.</param>
      <param name="keywords">Mot clé de l'événement source.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.IsEnabled(System.Diagnostics.Tracing.EventLevel,System.Diagnostics.Tracing.EventKeywords,System.Diagnostics.Tracing.EventChannel)">
      <summary>Détermine si la source d'événements actuelle est activée pour les événements avec le niveau, les mots clés et le canal spécifiés.</summary>
      <returns>true si la source d'événements est activée pour le niveau d'événement, les mots clés et le canal spécifiés ; sinon, false.Le résultat de cette méthode est seulement une approximation du caractère actif d'un événement.Utilisez-la pour éviter des calculs coûteux quand la journalisation est désactivée.Les sources d'événements peuvent avoir un filtrage supplémentaire qui détermine leur activité.</returns>
      <param name="level">Niveau d'événement à vérifier.Une source d'événements est considérée comme activée quand son niveau est supérieur ou égal à <paramref name="level" />.</param>
      <param name="keywords">Mots clés d'événement à vérifier.</param>
      <param name="channel">Canal d'événement à vérifier.</param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Name">
      <summary>Nom convivial de la classe dérivé de la source d'événements.</summary>
      <returns>Nom convivial de la classe dérivée.La valeur par défaut est le nom simple de la classe.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.OnEventCommand(System.Diagnostics.Tracing.EventCommandEventArgs)">
      <summary>Appelée quand la source d'événements actuelle est mise à jour par le contrôleur.</summary>
      <param name="command">Arguments pour l'événement.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SendCommand(System.Diagnostics.Tracing.EventSource,System.Diagnostics.Tracing.EventCommand,System.Collections.Generic.IDictionary{System.String,System.String})">
      <summary>Envoie une commande à une source d'événements spécifiée.</summary>
      <param name="eventSource">Source d'événements à laquelle envoyer la commande.</param>
      <param name="command">Commande d'événement à envoyer.</param>
      <param name="commandArguments">Arguments pour la commande d'événement.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Définit l'ID d'activité du thread actif.</summary>
      <param name="activityId">Nouvel ID d'activité du thread actif, ou <see cref="F:System.Guid.Empty" /> pour indiquer que le travail sur le thread actif n'est associé à aucune activité. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.SetCurrentThreadActivityId(System.Guid,System.Guid@)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Définit l'ID d'activité sur le thread actif et retourne l'ID de l'activité précédente.</summary>
      <param name="activityId">Nouvel ID d'activité du thread actif, ou <see cref="F:System.Guid.Empty" /> pour indiquer que le travail sur le thread actif n'est associé à aucune activité.</param>
      <param name="oldActivityThatWillContinue">Quand cette méthode est retournée, contient l'ID de l'activité précédente sur le thread actif. </param>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.Settings">
      <summary>Obtient les paramètres appliqués à cette source d'événements.</summary>
      <returns>Paramètres appliqués à cette source d'événements.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.ToString">
      <summary>Obtient une représentation sous forme de chaîne de l'instance de source d'événements actuelle.</summary>
      <returns>Nom et identificateur unique qui identifient la source d'événements actuelle.</returns>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String)">
      <summary>Écrit un événement sans champ, mais avec le nom spécifié et les options par défaut.</summary>
      <param name="eventName">Nom de l'événement à écrire.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)">
      <summary>Écrit un événement sans champ, mais avec le nom et les options spécifiés.</summary>
      <param name="eventName">Nom de l'événement à écrire.</param>
      <param name="options">Options pour l'événement, telles que le niveau, les mots clés et le code d'opération.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="eventName" /> is null.</exception>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)">
      <summary>Écrit un événement avec le nom, les données d'événements et les options spécifiés.</summary>
      <param name="eventName">Nom de l'événement.</param>
      <param name="options">Options d'événement.</param>
      <param name="data">Données d'événement.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Type qui définit l'événement et ses données associées.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,System.Guid@,System.Guid@,``0@)">
      <summary>Écrit un événement avec le nom, les options, l'activité associée et les données d'événements spécifiés.</summary>
      <param name="eventName">Nom de l'événement.</param>
      <param name="options">Options d'événement.</param>
      <param name="activityId">ID de l'activité associée à l'événement.</param>
      <param name="relatedActivityId">ID d'une activité associée, ou <see cref="F:System.Guid.Empty" /> en l'absence d'activité associée.</param>
      <param name="data">Données d'événement.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Type qui définit l'événement et ses données associées.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions@,``0@)">
      <summary>Écrit un événement avec le nom, les données d'événements et les options spécifiés.</summary>
      <param name="eventName">Nom de l'événement.</param>
      <param name="options">Options d'événement.</param>
      <param name="data">Données d'événement.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Type qui définit l'événement et ses données associées.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,``0)">
      <summary>Écrit un événement avec le nom et les données spécifiés.</summary>
      <param name="eventName">Nom de l'événement.</param>
      <param name="data">Données d'événement.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventDataAttribute" />.</param>
      <typeparam name="T">Type qui définit l'événement et ses données associées.Ce type doit être anonyme ou marqué avec l'attribut <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</typeparam>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement fourni.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Byte[])">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et de l'argument de tableau d'octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de tableau d'octets.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et de l'argument en entier d'une valeur de 32 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments en entier d'une valeur de 32 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier.</param>
      <param name="arg2">Argument entier.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments en entier d'une valeur de 32 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier.</param>
      <param name="arg2">Argument entier.</param>
      <param name="arg3">Argument entier.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int32,System.String)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments de chaînes et d'entiers 32 bits fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 32 bits.</param>
      <param name="arg2">Argument de chaîne.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et de l'argument en entier d'une valeur de 64 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Byte[])">
      <summary>Écrit les données d'événements à l'aide de l'identificateur et des arguments de tableau de chaînes et d'entiers 64 bits spécifiés.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 64 bits.</param>
      <param name="arg2">Argument de tableau d'octets.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments d'une valeur de 64 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 64 bits.</param>
      <param name="arg2">Argument entier 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.Int64,System.Int64)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments d'une valeur de 64 octets fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 64 bits.</param>
      <param name="arg2">Argument entier 64 bits.</param>
      <param name="arg3">Argument entier 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Int64,System.String)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments de chaînes et d'entiers 64 bits fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument entier 64 bits.</param>
      <param name="arg2">Argument de chaîne.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.Object[])">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et du tableau d'arguments fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="args">Tableau d'objets.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et de l'argument de chaîne fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et d'arguments fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
      <param name="arg2">Argument entier 32 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int32,System.Int32)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et d'arguments fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
      <param name="arg2">Argument entier 32 bits.</param>
      <param name="arg3">Argument entier 32 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.Int64)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et d'arguments fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
      <param name="arg2">Argument entier 64 bits.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments de chaîne fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
      <param name="arg2">Argument de chaîne.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEvent(System.Int32,System.String,System.String,System.String)">
      <summary>Écrit un événement à l'aide de l'identificateur d'événement et des arguments de chaîne fournis.</summary>
      <param name="eventId">Identificateur de l'événement.La valeur doit être comprise entre 0 et 65535.</param>
      <param name="arg1">Argument de chaîne.</param>
      <param name="arg2">Argument de chaîne.</param>
      <param name="arg3">Argument de chaîne.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>Crée une surcharge <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> à l'aide de l'identificateur d'événement et des données d'événement fournis.</summary>
      <param name="eventId">Identificateur de l'événement.</param>
      <param name="eventDataCount">Nombre d'éléments de données d'événement.</param>
      <param name="data">Structure contenant les données d'événement.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityId(System.Int32,System.Guid,System.Object[])">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Écrit un événement qui indique que l'activité actuelle est liée à une autre activité. </summary>
      <param name="eventId">Identificateur qui identifie de manière unique cet événement au sein de l'objet <see cref="T:System.Diagnostics.Tracing.EventSource" />. </param>
      <param name="relatedActivityId">Identificateur d'activité associé. </param>
      <param name="args">Tableau d'objets qui contient les données relatives à l'événement. </param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSource.WriteEventWithRelatedActivityIdCore(System.Int32,System.Guid*,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Écrit un événement qui indique que l'activité actuelle est liée à une autre activité.</summary>
      <param name="eventId">Identificateur qui identifie de manière unique cet événement au sein de l'objet <see cref="T:System.Diagnostics.Tracing.EventSource" />.</param>
      <param name="relatedActivityId">Pointeur vers le GUID de l'ID d'activité associé. </param>
      <param name="eventDataCount">Nombre d'éléments dans le champ <paramref name="data" />. </param>
      <param name="data">Pointeur vers le premier élément dans le champ de données de l'événement. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSource.EventData">
      <summary>Fournit les données d'événement pour créer des surcharges <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" /> rapides à l'aide de la méthode <see cref="M:System.Diagnostics.Tracing.EventSource.WriteEventCore(System.Int32,System.Int32,System.Diagnostics.Tracing.EventSource.EventData*)" /> .</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.DataPointer">
      <summary>Obtient ou définit le pointeur vers les données de la nouvelle surcharge <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" />.</summary>
      <returns>Pointeur vers les données.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSource.EventData.Size">
      <summary>Obtient ou définit le nombre d'éléments de charge dans la nouvelle surcharge <see cref="Overload:System.Diagnostics.Tracing.EventSource.WriteEvent" />.</summary>
      <returns>Nombre d'éléments de charge utile dans la nouvelle surcharge.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceAttribute">
      <summary>Permet au nom de suivi d'événements pour Windows (ETW) d'être défini indépendamment du nom de la classe d'événement.   </summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceAttribute.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSourceAttribute" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Guid">
      <summary>Obtient ou définit l'identificateur source d'événement.</summary>
      <returns>Identificateur source de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.LocalizationResources">
      <summary>Obtient ou définit le nom du fichier de ressources de localisation.</summary>
      <returns>Nom du fichier de ressources de localisation, ou null si le fichier de ressources de localisation n'existe pas.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceAttribute.Name">
      <summary>Obtient ou définit le nom de la source d'événement.</summary>
      <returns>Nom de la source d'événements.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceException">
      <summary>Exception levée lorsqu'une erreur se produit pendant le suivi d'événements pour Windows (ETW).</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSourceException" />.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> avec un message d'erreur spécifié.</summary>
      <param name="message">Message décrivant l'erreur.</param>
    </member>
    <member name="M:System.Diagnostics.Tracing.EventSourceException.#ctor(System.String,System.Exception)">
      <summary>Initialise une nouvelle instance de la classe <see cref="T:System.Diagnostics.Tracing.EventSourceException" /> avec un message d'erreur spécifié et une référence à l'exception interne ayant provoqué cette exception.</summary>
      <param name="message">Message d'erreur indiquant la raison de l'exception. </param>
      <param name="innerException">L'exception qui est la cause de l'exception actuelle ou null si aucune exception interne n'est spécifiée. </param>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventSourceOptions">
      <summary>Spécifie des remplacements de paramètres d'événements par défaut tels que le niveau de journalisation, les mots clés et opération code lorsque le <see cref="M:System.Diagnostics.Tracing.EventSource.Write``1(System.String,System.Diagnostics.Tracing.EventSourceOptions,``0)" /> méthode est appelée.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.ActivityOptions"></member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Keywords">
      <summary>Obtient ou définit les mots clés appliqués à l'événement.Si cette propriété n'est pas définie, les mots clés de l'événement sera None.</summary>
      <returns>Les mots clés appliquées à l'événement, ou None si aucun mot clé n'est définies.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Level">
      <summary>Obtient ou définit le niveau d'événement appliqué à l'événement. </summary>
      <returns>Niveau de l'événement.Si ce paramètre n'est pas défini, la valeur par défaut est Verbose (5).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Opcode">
      <summary>Obtient ou définit le code d'opération à utiliser pour l'événement spécifié. </summary>
      <returns>Code d'opération à utiliser pour l'événement spécifié.Si la valeur non, la valeur par défaut est Info (0).</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventSourceOptions.Tags"></member>
    <member name="T:System.Diagnostics.Tracing.EventSourceSettings">
      <summary>Spécifie les options de configuration pour une source d'événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.Default">
      <summary>Aucune des options de configuration spéciales n'est activée.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwManifestEventFormat">
      <summary>L'écouteur ETW doit utiliser un format basé sur un manifeste lors du déclenchement d'événement.Définir cette option équivaut à demander à l'écouteur ETW d'utiliser un format basé sur un manifeste lors du déclenchement d'événement.C'est l'option par défaut lors de la définition d'un type dérivé de <see cref="T:System.Diagnostics.Tracing.EventSource" /> à l'aide d'une des protégée <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructeurs.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.EtwSelfDescribingEventFormat">
      <summary>L'écouteur ETW doit utiliser le format d'événement autodescriptif.C'est l'option par défaut lorsque vous créez une nouvelle instance de la <see cref="T:System.Diagnostics.Tracing.EventSource" /> à l'aide de public <see cref="T:System.Diagnostics.Tracing.EventSource" /> constructeurs.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventSourceSettings.ThrowOnEventWriteErrors">
      <summary>La source d'événements lève une exception quand une erreur se produit.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTags">
      <summary>Spécifie le suivi des événements de début et de fin d'activité.Vous devez utiliser seulement les 24 bits de poids le plus faible.Pour plus d’informations, consultez <see cref="T:System.Diagnostics.Tracing.EventSourceOptions" /> et <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTags.None">
      <summary>Spécifie aucune balise et égal à zéro.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventTask">
      <summary>Définit les tâches qui s'appliquent aux événements.</summary>
    </member>
    <member name="F:System.Diagnostics.Tracing.EventTask.None">
      <summary>Tâche non définie.</summary>
    </member>
    <member name="T:System.Diagnostics.Tracing.EventWrittenEventArgs">
      <summary>Fournit des données pour le rappel <see cref="M:System.Diagnostics.Tracing.EventListener.OnEventWritten(System.Diagnostics.Tracing.EventWrittenEventArgs)" />.</summary>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.ActivityId">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Obtient l'ID d'activité du thread sur lequel l'événement a été écrit. </summary>
      <returns>ID d'activité du thread sur lequel l'événement a été écrit. </returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Channel">
      <summary>Obtient le canal pour l'événement.</summary>
      <returns>Canal pour l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventId">
      <summary>Obtient l'identificateur de l'événement.</summary>
      <returns>Identificateur de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventName">
      <summary>Obtient le nom de l'événement.</summary>
      <returns>Nom de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.EventSource">
      <summary>Obtient l'objet source de l'événement.</summary>
      <returns>Objet source d'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Keywords">
      <summary>Obtient les mots clés de l'événement.</summary>
      <returns>Mots clés de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Level">
      <summary>Obtient le niveau de l'événement.</summary>
      <returns>Niveau de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Message">
      <summary>Obtient le message pour l'événement.</summary>
      <returns>Message pour l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Opcode">
      <summary>Obtient le code d'opération pour l'événement.</summary>
      <returns>Code d'opération de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Payload">
      <summary>Obtient la charge utile de l'événement.</summary>
      <returns>Charge utile pour l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.PayloadNames">
      <summary>Retourne une liste de chaînes qui représentent les noms de propriétés de l'événement.</summary>
      <returns>Retourne <see cref="T:System.Collections.ObjectModel.ReadOnlyCollection`1" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.RelatedActivityId">
      <summary>[Pris en charge dans le .NET Framework 4.5.1 et versions ultérieures] Obtient l'identificateur d'une activité liée à l'activité représentée par l'instance actuelle. </summary>
      <returns>Identificateur de l'activité connexe, ou <see cref="F:System.Guid.Empty" />￼ s'il n'existe aucune activité connexe.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Tags">
      <summary>Retourne les balises spécifiées dans l'appel à la méthode <see cref="M:System.Diagnostics.Tracing.EventSource.Write(System.String,System.Diagnostics.Tracing.EventSourceOptions)" />.</summary>
      <returns>Retourne <see cref="T:System.Diagnostics.Tracing.EventTags" />.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Task">
      <summary>Obtient la tâche pour l'événement.</summary>
      <returns>Tâche de l'événement.</returns>
    </member>
    <member name="P:System.Diagnostics.Tracing.EventWrittenEventArgs.Version">
      <summary>Obtient la version de l'événement.</summary>
      <returns>Version de l'événement.</returns>
    </member>
    <member name="T:System.Diagnostics.Tracing.NonEventAttribute">
      <summary>Identifie une méthode qui ne génère pas d'événement.</summary>
    </member>
    <member name="M:System.Diagnostics.Tracing.NonEventAttribute.#ctor">
      <summary>Crée une instance de la classe <see cref="T:System.Diagnostics.Tracing.NonEventAttribute" />.</summary>
    </member>
  </members>
</doc>